/**
 * 信号生成器适配器
 * 将后端的信号生成逻辑适配为回测环境
 */

// 使用独立的技术指标模块（与后端逻辑保持一致）
const TechnicalIndicators = require('../utils/technicalIndicators');

class SignalAdapter {
  constructor(dataAdapter) {
    this.dataAdapter = dataAdapter;
    this.technicalIndicators = new TechnicalIndicators();
  }

  /**
   * 生成历史信号（适配版本）
   * @param {string} asset - 资产名称
   * @param {Object} config - 信号配置
   * @param {number} index - 当前K线索引
   * @returns {Object} 信号结果
   */
  async generateSignal(asset, config, index) {
    try {
      // 获取历史数据切片（只能看到当前时间点之前的数据）
      const historicalData = this.dataAdapter.getHistoricalSlice(index, 200);
      const currentCandle = this.dataAdapter.getCurrentCandle(index);
      
      if (!currentCandle || historicalData.length < 50) {
        return {
          signal_type: 'hold',
          signal_strength: 'skip',
          quality_score: 0,
          reason: '数据不足'
        };
      }

      // 计算技术指标
      const indicators = this.calculateIndicators(historicalData);
      
      // 获取当前价格信息
      const priceData = this.dataAdapter.getPriceData(asset, index);
      
      // 构建信号数据
      const signalData = {
        asset,
        price: currentCandle.close,
        timestamp: currentCandle.timestamp,
        ...indicators,
        ...priceData
      };

      // 使用原始的信号分析逻辑
      const signal = await this.analyzeSignal(signalData, config);
      
      return {
        ...signal,
        timestamp: currentCandle.timestamp,
        price: currentCandle.close,
        index: index
      };

    } catch (error) {
      console.error(`信号生成失败 (索引 ${index}):`, error.message);
      return {
        signal_type: 'hold',
        signal_strength: 'skip',
        quality_score: 0,
        reason: `错误: ${error.message}`
      };
    }
  }

  /**
   * 计算技术指标
   * @param {Array} historicalData - OHLCV历史数据
   * @returns {Object} 技术指标结果
   */
  calculateIndicators(historicalData) {
    const closes = historicalData.map(d => d[4]); // 收盘价
    const highs = historicalData.map(d => d[2]);  // 最高价
    const lows = historicalData.map(d => d[3]);   // 最低价
    const volumes = historicalData.map(d => d[5]); // 成交量

    // RSI (14周期) - 使用独立技术指标
    const rsi = this.technicalIndicators.calculateRSI(closes, 14);

    // EMA (9和21周期) - 使用独立技术指标
    const ema9 = this.technicalIndicators.calculateEMAFromPrices(closes, 9);
    const ema21 = this.technicalIndicators.calculateEMAFromPrices(closes, 21);

    // MACD (12, 26, 9) - 使用独立技术指标
    const macd = this.technicalIndicators.calculateMACD(historicalData, 12, 26, 9);

    // ATR (14周期) - 使用独立技术指标
    const atr = this.technicalIndicators.calculateATR(historicalData, 14);
    
    // 成交量比率（当前vs平均）
    const volumeRatio = this.technicalIndicators.calculateVolumeRatio(volumes);
    
    // 4小时趋势（简化版本）
    const trend4h = this.determineTrend(closes);

    return {
      rsi: rsi || 50, // TechnicalIndicators.calculateRSI返回单个值
      ema_short: ema9 || closes[closes.length - 1],
      ema_long: ema21 || closes[closes.length - 1],
      macd_line: macd.macd || 0,
      macd_signal: macd.signal || 0,
      macd_histogram: macd.histogram || 0,
      atr: atr || 0,
      volume_ratio: volumeRatio,
      trend_4h: trend4h,
      pattern: 'None' // 简化，不做复杂形态识别
    };
  }

  /**
   * 分析信号（使用与后端一致的分析逻辑）
   * @param {Object} signalData - 信号数据
   * @param {Object} config - 配置
   * @returns {Object} 信号结果
   */
  async analyzeSignal(signalData, config) {
    // 使用与后端SignalAnalyzer一致的分析逻辑
    const qualityScore = this.calculateQualityScore(signalData, config);

    // 信号类型和强度判断
    let signal_type = 'hold';
    let signal_strength = 'skip';

    // RSI判断
    if (signalData.rsi < config.rsi_strong_threshold && signalData.ema_short > signalData.ema_long) {
      signal_type = 'buy';
      signal_strength = 'strong';
    } else if (signalData.rsi < config.rsi_medium_threshold && signalData.ema_short > signalData.ema_long) {
      signal_type = 'buy';
      signal_strength = 'medium';
    } else if (signalData.rsi < 50 && signalData.ema_short > signalData.ema_long) {
      signal_type = 'buy';
      signal_strength = 'weak';
    }

    // 质量过滤
    if (qualityScore < config.min_quality_score) {
      signal_type = 'hold';
      signal_strength = 'skip';
    }

    // 计算入场价格和止损止盈
    const entryPrice = signalData.price;
    const stopLoss = entryPrice - (signalData.atr * config.atr_multiplier);
    const takeProfit = entryPrice + ((entryPrice - stopLoss) * config.risk_reward_ratio);

    return {
      asset: signalData.asset, // 添加资产名称
      signal_type,
      signal_strength,
      quality_score: qualityScore,
      entry_price: entryPrice,
      stop_loss: stopLoss,
      take_profit: takeProfit,
      rsi: signalData.rsi,
      ema_short: signalData.ema_short,
      ema_long: signalData.ema_long,
      atr: signalData.atr,
      volume_ratio: signalData.volume_ratio,
      reason: this.getSignalReason(signal_type, signal_strength, qualityScore)
    };
  }

  /**
   * 计算信号质量评分（与后端SignalAnalyzer保持一致）
   * @param {Object} signalData - 信号数据
   * @param {Object} config - 配置
   * @returns {number} 质量评分
   */
  calculateQualityScore(signalData, config) {
    let score = 0;

    // 1. RSI指标评分 (40分) - 与后端一致
    if (signalData.rsi < 20) score += 40; // 极度超卖
    else if (signalData.rsi < 25) score += 35; // 严重超卖
    else if (signalData.rsi < 30) score += 30; // 超卖
    else if (signalData.rsi < 35) score += 25; // 偏低
    else if (signalData.rsi < 40) score += 20; // 弱势
    else if (signalData.rsi < 45) score += 15; // 中性偏弱
    else if (signalData.rsi < 50) score += 10; // 中性
    // RSI>50时不给分

    // 2. EMA交叉评分 (30分) - 与后端一致
    if (signalData.ema_short > signalData.ema_long) {
      if (signalData.price > signalData.ema_long) {
        score += 30; // 价格在长期均线之上
      } else {
        score += 20; // 均线金叉但价格偏低
      }
    } else {
      score += 5; // 均线死叉，给基础分
    }

    // 3. 成交量评分 (30分) - 与后端一致
    if (signalData.volume_ratio > config.volume_multiplier * 2) {
      score += 30; // 成交量爆发
    } else if (signalData.volume_ratio > config.volume_multiplier) {
      score += 20; // 成交量放大
    } else if (signalData.volume_ratio > 0.8) {
      score += 10; // 成交量正常
    } else {
      score += 5; // 成交量萎缩
    }

    // 确保评分在0-100范围内
    return Math.max(0, Math.min(100, score));
  }

  /**
   * 备用信号分析（简化版本）
   * @param {Object} signalData - 信号数据
   * @param {Object} config - 配置
   * @returns {Object} 信号结果
   */
  fallbackAnalyzeSignal(signalData, config) {
    const { rsi, ema_short, ema_long } = signalData;

    let signal_type = 'hold';
    let signal_strength = 'skip';
    let quality_score = 0;

    // 简化的RSI判断
    if (rsi < config.rsi_strong_threshold && ema_short > ema_long) {
      signal_type = 'buy';
      signal_strength = 'strong';
      quality_score = 70;
    } else if (rsi < config.rsi_medium_threshold && ema_short > ema_long) {
      signal_type = 'buy';
      signal_strength = 'medium';
      quality_score = 50;
    }

    // 质量过滤
    if (quality_score < config.min_quality_score) {
      signal_type = 'hold';
      signal_strength = 'skip';
    }

    // 计算入场价格和止损止盈
    const entryPrice = signalData.price;
    const stopLoss = entryPrice - (signalData.atr * config.atr_multiplier);
    const takeProfit = entryPrice + ((entryPrice - stopLoss) * config.risk_reward_ratio);

    return {
      signal_type,
      signal_strength,
      quality_score,
      entry_price: entryPrice,
      stop_loss: stopLoss,
      take_profit: takeProfit,
      rsi: signalData.rsi,
      ema_short: signalData.ema_short,
      ema_long: signalData.ema_long,
      atr: signalData.atr,
      volume_ratio: signalData.volume_ratio,
      reason: this.getSignalReason(signal_type, signal_strength, quality_score)
    };
  }

  /**
   * 计算成交量比率
   * @param {Array} volumes - 成交量数组
   * @returns {number} 当前成交量相对于平均值的比率
   */
  calculateVolumeRatio(volumes) {
    if (volumes.length < 20) return 1;
    
    const currentVolume = volumes[volumes.length - 1];
    const avgVolume = volumes.slice(-20, -1).reduce((a, b) => a + b, 0) / 19;
    
    return avgVolume > 0 ? currentVolume / avgVolume : 1;
  }

  /**
   * 判断趋势方向
   * @param {Array} closes - 收盘价数组
   * @returns {string} 趋势方向
   */
  determineTrend(closes) {
    if (closes.length < 20) return 'sideways';
    
    const recent = closes.slice(-20);
    const first = recent[0];
    const last = recent[recent.length - 1];
    const change = (last - first) / first;
    
    if (change > 0.02) return 'up';
    if (change < -0.02) return 'down';
    return 'sideways';
  }

  /**
   * 获取信号原因说明
   * @param {string} type - 信号类型
   * @param {string} strength - 信号强度
   * @param {number} score - 质量评分
   * @returns {string} 原因说明
   */
  getSignalReason(type, strength, score) {
    if (type === 'hold') {
      return `质量评分不足 (${score}分)`;
    }
    
    return `${strength === 'strong' ? '强' : '中等'}信号确认 (${score}分)`;
  }
}

module.exports = SignalAdapter;
