/**
 * 历史数据下载器
 * 从Binance API获取历史K线数据用于回测
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

class DataDownloader {
  constructor() {
    this.baseURL = 'https://api.binance.com/api/v3';
    this.requestDelay = 300; // 请求间隔(ms)，避免触发频率限制
  }

  /**
   * 下载指定币种的历史K线数据
   * @param {string} symbol - 交易对符号 (如 'BTCUSDT')
   * @param {string} interval - K线间隔 (如 '1h')
   * @param {Date} startDate - 开始日期
   * @param {Date} endDate - 结束日期
   */
  async downloadKlines(symbol, interval, startDate, endDate) {
    const startTime = startDate.getTime();
    const endTime = endDate.getTime();
    const limit = 1000; // Binance API单次最大限制
    
    let allKlines = [];
    let currentStartTime = startTime;
    let requestCount = 0;

    console.log(`🚀 开始下载 ${symbol} ${interval} 历史数据...`);
    console.log(`📅 时间范围: ${startDate.toISOString()} 至 ${endDate.toISOString()}`);

    while (currentStartTime < endTime) {
      const url = `${this.baseURL}/klines`;
      const params = {
        symbol,
        interval,
        startTime: currentStartTime,
        endTime: endTime,
        limit
      };

      try {
        const response = await axios.get(url, { params });
        const klines = response.data;
        
        if (klines.length === 0) {
          console.log('📭 没有更多数据，下载完成');
          break;
        }
        
        allKlines = allKlines.concat(klines);
        currentStartTime = klines[klines.length - 1][0] + 1;
        requestCount++;

        console.log(`📊 已下载 ${allKlines.length} 条数据，请求次数: ${requestCount}`);
        console.log(`⏰ 最新时间: ${new Date(currentStartTime - 1).toLocaleString()}`);
        
        // 尊重API频率限制
        await this.sleep(this.requestDelay);

      } catch (error) {
        console.error('❌ 下载失败:', error.message);
        if (error.response?.status === 429) {
          console.log('⏳ 触发频率限制，等待更长时间...');
          await this.sleep(5000);
        } else {
          await this.sleep(1000);
        }
      }
    }

    // 保存数据到文件
    const filename = `${symbol}_${interval}.json`;
    const filepath = path.join(__dirname, filename);
    
    const dataToSave = {
      symbol,
      interval,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      totalRecords: allKlines.length,
      downloadTime: new Date().toISOString(),
      data: allKlines
    };

    fs.writeFileSync(filepath, JSON.stringify(dataToSave, null, 2));
    
    console.log(`✅ 数据下载完成！`);
    console.log(`📁 文件保存至: ${filepath}`);
    console.log(`📈 总计 ${allKlines.length} 条K线数据`);
    
    return filepath;
  }

  /**
   * 验证下载的数据
   * @param {string} filepath - 数据文件路径
   */
  validateData(filepath) {
    try {
      const data = JSON.parse(fs.readFileSync(filepath, 'utf8'));
      const klines = data.data;
      
      console.log('\n🔍 数据验证结果:');
      console.log(`📊 总记录数: ${klines.length}`);
      console.log(`⏰ 时间范围: ${new Date(klines[0][0]).toLocaleString()} - ${new Date(klines[klines.length-1][0]).toLocaleString()}`);
      console.log(`💰 价格范围: $${Math.min(...klines.map(k => parseFloat(k[2])))} - $${Math.max(...klines.map(k => parseFloat(k[1])))}`);
      
      // 检查数据连续性
      let gaps = 0;
      const intervalMs = this.getIntervalMs(data.interval);
      
      for (let i = 1; i < klines.length; i++) {
        const timeDiff = klines[i][0] - klines[i-1][0];
        if (timeDiff > intervalMs) {
          gaps++;
        }
      }
      
      console.log(`🔗 数据连续性: ${gaps === 0 ? '✅ 完整' : `⚠️ 发现 ${gaps} 个时间间隔`}`);
      
      return { valid: true, gaps, totalRecords: klines.length };
      
    } catch (error) {
      console.error('❌ 数据验证失败:', error.message);
      return { valid: false, error: error.message };
    }
  }

  /**
   * 获取时间间隔的毫秒数
   */
  getIntervalMs(interval) {
    const intervals = {
      '1m': 60 * 1000,
      '5m': 5 * 60 * 1000,
      '15m': 15 * 60 * 1000,
      '30m': 30 * 60 * 1000,
      '1h': 60 * 60 * 1000,
      '4h': 4 * 60 * 60 * 1000,
      '1d': 24 * 60 * 60 * 1000
    };
    return intervals[interval] || 60 * 60 * 1000;
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 如果直接运行此脚本，下载默认数据
if (require.main === module) {
  const downloader = new DataDownloader();

  // 获取命令行参数
  const args = process.argv.slice(2);
  const symbol = args[0] || 'ETHUSDT';
  const interval = args[1] || '1h';
  const startYear = args[2] || '2017'; // 默认从2017年开始，获取更长历史

  // 下载更长生命周期的数据
  const startDate = new Date(`${startYear}-01-01T00:00:00Z`);
  const endDate = new Date();

  console.log(`📊 准备下载 ${symbol} ${interval} 数据`);
  console.log(`📅 时间范围: ${startYear}年1月1日 至 现在`);
  console.log(`⏱️ 预计数据跨度: ${new Date().getFullYear() - parseInt(startYear)} 年`);

  downloader.downloadKlines(symbol, interval, startDate, endDate)
    .then(filepath => {
      downloader.validateData(filepath);
      console.log(`\n🎉 下载完成！数据文件: ${filepath}`);
      console.log(`💡 使用方法:`);
      console.log(`   node downloader.js ETHUSDT 1h 2017  # 下载ETH 1小时数据从2017年`);
      console.log(`   node downloader.js BTCUSDT 4h 2018  # 下载BTC 4小时数据从2018年`);
      console.log(`   node downloader.js ETHUSDT 1d 2019  # 下载ETH 日线数据从2019年`);
    })
    .catch(error => {
      console.error('❌ 下载失败:', error.message);
    });
}

module.exports = DataDownloader;
