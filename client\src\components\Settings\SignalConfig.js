import React, { useState, useEffect } from 'react';
import { signalConfigAPI } from '../../utils/api';
import toast from 'react-hot-toast';

const SignalConfig = () => {
  const [config, setConfig] = useState({
    rsi_strong_threshold: 30,
    rsi_medium_threshold: 40,
    risk_strong: 0.01,           // 更新为新的风险参数
    risk_medium: 0.008,          // 更新为新的风险参数
    risk_weak: 0.005,            // 更新为新的风险参数
    atr_multiplier: 1.5,         // 新增ATR倍数
    risk_reward_ratio: 2.0,      // 新增风险回报比
    volume_multiplier: 1.5,
    enable_volume_filter: true,
    enable_m_head_filter: false, // 关闭M头过滤，增加信号机会
    enable_w_bottom_bonus: true,
    skip_weak_signals: false,
    min_quality_score: 40        // 降低最低质量要求
  });
  
  const [presets, setPresets] = useState({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activePreset, setActivePreset] = useState(null);

  useEffect(() => {
    loadConfig();
    loadPresets();
  }, []);

  // 当预设加载完成后，重新检测当前配置
  useEffect(() => {
    if (presets && Object.keys(presets).length > 0 && config) {
      detectActivePreset(config);
    }
  }, [presets, config]);

  const loadConfig = async () => {
    try {
      const response = await signalConfigAPI.getConfig();
      if (response.data.success) {
        // 转换数据库中的数值为布尔值
        const configData = {
          ...response.data.data,
          enable_volume_filter: Boolean(response.data.data.enable_volume_filter),
          enable_m_head_filter: Boolean(response.data.data.enable_m_head_filter),
          enable_w_bottom_bonus: Boolean(response.data.data.enable_w_bottom_bonus),
          skip_weak_signals: Boolean(response.data.data.skip_weak_signals)
        };

        setConfig(configData);
        console.log('📋 加载的配置:', configData);
        // 检测当前配置匹配哪个预设
        detectActivePreset(configData);
      }
    } catch (error) {
      console.error('加载配置失败:', error);
      toast.error('加载配置失败');
    } finally {
      setLoading(false);
    }
  };

  const loadPresets = async () => {
    try {
      const response = await signalConfigAPI.getPresets();
      if (response.data.success) {
        setPresets(response.data.data || {});
      } else {
        setPresets({});
      }
    } catch (error) {
      console.error('加载预设失败:', error);
      setPresets({}); // 确保在错误时设置为空对象
    }
  };

  // 检测当前配置匹配哪个预设
  const detectActivePreset = (currentConfig) => {
    if (!presets || Object.keys(presets).length === 0) return;

    // 转换数据库中的数值为布尔值进行比较
    const normalizeConfig = (config) => ({
      ...config,
      enable_volume_filter: Boolean(config.enable_volume_filter),
      enable_m_head_filter: Boolean(config.enable_m_head_filter),
      enable_w_bottom_bonus: Boolean(config.enable_w_bottom_bonus),
      skip_weak_signals: Boolean(config.skip_weak_signals)
    });

    const normalizedCurrent = normalizeConfig(currentConfig);

    for (const [key, preset] of Object.entries(presets || {})) {
      const normalizedPreset = normalizeConfig(preset);

      const isMatch =
        normalizedCurrent.rsi_strong_threshold === normalizedPreset.rsi_strong_threshold &&
        normalizedCurrent.rsi_medium_threshold === normalizedPreset.rsi_medium_threshold &&
        normalizedCurrent.risk_strong === normalizedPreset.risk_strong &&           // 更新为新的风险参数
        normalizedCurrent.risk_medium === normalizedPreset.risk_medium &&           // 更新为新的风险参数
        normalizedCurrent.risk_weak === normalizedPreset.risk_weak &&               // 更新为新的风险参数
        normalizedCurrent.atr_multiplier === normalizedPreset.atr_multiplier &&     // 新增ATR倍数比较
        normalizedCurrent.risk_reward_ratio === normalizedPreset.risk_reward_ratio && // 新增风险回报比比较
        normalizedCurrent.volume_multiplier === normalizedPreset.volume_multiplier &&
        normalizedCurrent.enable_volume_filter === normalizedPreset.enable_volume_filter &&
        normalizedCurrent.enable_m_head_filter === normalizedPreset.enable_m_head_filter &&
        normalizedCurrent.enable_w_bottom_bonus === normalizedPreset.enable_w_bottom_bonus &&
        normalizedCurrent.skip_weak_signals === normalizedPreset.skip_weak_signals &&
        normalizedCurrent.min_quality_score === normalizedPreset.min_quality_score;

      if (isMatch) {
        setActivePreset(key);
        console.log(`🎯 检测到匹配的预设: ${preset.name}`);
        return;
      }
    }
    setActivePreset(null); // 没有匹配的预设，表示是自定义配置
    console.log(`🔧 当前为自定义配置`);
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      const response = await signalConfigAPI.saveConfig(config);
      if (response.data.success) {
        toast.success('配置保存成功');
      }
    } catch (error) {
      console.error('保存配置失败:', error);
      toast.error(error.response?.data?.message || '保存配置失败');
    } finally {
      setSaving(false);
    }
  };

  const handleReset = async () => {
    if (!window.confirm('确定要重置为默认配置吗？')) return;
    
    try {
      const response = await signalConfigAPI.resetConfig();
      if (response.data.success) {
        toast.success('配置已重置');
        loadConfig();
      }
    } catch (error) {
      console.error('重置配置失败:', error);
      toast.error('重置配置失败');
    }
  };

  const applyPreset = async (presetKey) => {
    const preset = presets[presetKey];
    if (preset) {
      const newConfig = {
        ...config,
        ...preset
      };
      setConfig(newConfig);
      setActivePreset(presetKey); // 设置当前活跃的预设

      // 自动保存到服务器
      setSaving(true);
      try {
        const response = await signalConfigAPI.saveConfig(newConfig);
        if (response.data.success) {
          toast.success(`已应用并保存${preset.name}配置`);
        }
      } catch (error) {
        console.error('保存配置失败:', error);
        toast.error(error.response?.data?.message || '保存配置失败');
        setActivePreset(null); // 保存失败时重置
      } finally {
        setSaving(false);
      }
    }
  };

  const handleInputChange = (field, value) => {
    const newConfig = {
      ...config,
      [field]: value
    };
    setConfig(newConfig);
    // 配置改变后重新检测预设
    detectActivePreset(newConfig);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 当前配置状态 */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-sm font-medium text-blue-900">当前配置</h3>
            <p className="text-sm text-blue-700 mt-1">
              {activePreset ? `${presets[activePreset]?.name}预设` : '自定义配置'}
            </p>
          </div>
          <div className="text-xs text-blue-600">
            <div>RSI: {config.rsi_strong_threshold}/{config.rsi_medium_threshold}</div>
            <div>质量: {config.min_quality_score}分</div>
          </div>
        </div>
      </div>

      {/* 预设配置 */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">配置预设</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {Object.entries(presets || {}).map(([key, preset]) => {
            const isActive = activePreset === key;
            return (
              <div
                key={key}
                className={`border rounded-lg p-4 cursor-pointer transition-all ${
                  isActive
                    ? 'border-blue-500 bg-blue-50 shadow-md'
                    : 'border-gray-200 hover:bg-gray-50 hover:border-gray-300'
                }`}
                onClick={() => applyPreset(key)}
              >
                <div className="flex items-center justify-between mb-2">
                  <h4 className={`font-medium ${isActive ? 'text-blue-900' : 'text-gray-900'}`}>
                    {preset.name}
                  </h4>
                  {isActive && (
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  )}
                </div>
                <p className={`text-sm mt-1 ${isActive ? 'text-blue-700' : 'text-gray-600'}`}>
                  {preset.description}
                </p>
                <div className={`mt-2 text-xs ${isActive ? 'text-blue-600' : 'text-gray-500'}`}>
                  <div>RSI阈值: {preset.rsi_strong_threshold}/{preset.rsi_medium_threshold}</div>
                  <div>最低评分: {preset.min_quality_score}</div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* RSI 阈值设置 */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">RSI 阈值设置</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              强信号阈值 (当前: {config.rsi_strong_threshold})
            </label>
            <input
              type="range"
              min="15"
              max="30"
              value={config.rsi_strong_threshold}
              onChange={(e) => handleInputChange('rsi_strong_threshold', parseInt(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>15</span>
              <span>30</span>
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              中等信号阈值 (当前: {config.rsi_medium_threshold})
            </label>
            <input
              type="range"
              min="25"
              max="40"
              value={config.rsi_medium_threshold}
              onChange={(e) => handleInputChange('rsi_medium_threshold', parseInt(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>25</span>
              <span>40</span>
            </div>
          </div>
        </div>
      </div>

      {/* 风险管理设置 */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">风险管理设置</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              强信号风险 ({(config.risk_strong * 100).toFixed(1)}%)
            </label>
            <input
              type="number"
              step="0.001"
              min="0.001"
              max="0.02"
              value={config.risk_strong}
              onChange={(e) => handleInputChange('risk_strong', parseFloat(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <p className="text-xs text-gray-500 mt-1">每笔强信号交易愿意承担的资金风险</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              中等信号风险 ({(config.risk_medium * 100).toFixed(1)}%)
            </label>
            <input
              type="number"
              step="0.001"
              min="0.001"
              max="0.02"
              value={config.risk_medium}
              onChange={(e) => handleInputChange('risk_medium', parseFloat(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <p className="text-xs text-gray-500 mt-1">每笔中等信号交易愿意承担的资金风险</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              弱信号风险 ({(config.risk_weak * 100).toFixed(1)}%)
            </label>
            <input
              type="number"
              step="0.001"
              min="0.001"
              max="0.02"
              value={config.risk_weak}
              onChange={(e) => handleInputChange('risk_weak', parseFloat(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <p className="text-xs text-gray-500 mt-1">每笔弱信号交易愿意承担的资金风险</p>
          </div>
        </div>

        {/* ATR和风险回报比设置 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              ATR止损倍数 (当前: {config.atr_multiplier}x)
            </label>
            <input
              type="number"
              step="0.1"
              min="1.0"
              max="3.0"
              value={config.atr_multiplier}
              onChange={(e) => handleInputChange('atr_multiplier', parseFloat(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <p className="text-xs text-gray-500 mt-1">ATR倍数越大，止损距离越远</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              风险回报比 (当前: 1:{config.risk_reward_ratio})
            </label>
            <input
              type="number"
              step="0.1"
              min="1.5"
              max="4.0"
              value={config.risk_reward_ratio}
              onChange={(e) => handleInputChange('risk_reward_ratio', parseFloat(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <p className="text-xs text-gray-500 mt-1">止盈目标是止损距离的倍数</p>
          </div>
        </div>
      </div>

      {/* 成交量和质量设置 */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">成交量和质量控制</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              成交量放大倍数 (当前: {config.volume_multiplier}x)
            </label>
            <input
              type="range"
              min="1.0"
              max="3.0"
              step="0.1"
              value={config.volume_multiplier}
              onChange={(e) => handleInputChange('volume_multiplier', parseFloat(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>1.0x</span>
              <span>3.0x</span>
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              最低质量评分 (当前: {config.min_quality_score})
            </label>
            <input
              type="range"
              min="30"
              max="90"
              value={config.min_quality_score}
              onChange={(e) => handleInputChange('min_quality_score', parseInt(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>30</span>
              <span>90</span>
            </div>
          </div>
        </div>
      </div>

      {/* 过滤条件开关 */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">过滤条件</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-gray-900">启用成交量过滤</h4>
              <p className="text-sm text-gray-600">只在成交量放大时接受信号</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={config.enable_volume_filter}
                onChange={(e) => handleInputChange('enable_volume_filter', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-gray-900">启用M头过滤</h4>
              <p className="text-sm text-gray-600">检测到M头形态时降低信号评分</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={config.enable_m_head_filter}
                onChange={(e) => handleInputChange('enable_m_head_filter', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-gray-900">启用W底加分</h4>
              <p className="text-sm text-gray-600">检测到W底形态时提高信号评分</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={config.enable_w_bottom_bonus}
                onChange={(e) => handleInputChange('enable_w_bottom_bonus', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-gray-900">跳过弱信号</h4>
              <p className="text-sm text-gray-600">自动跳过质量评分较低的弱信号</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={config.skip_weak_signals}
                onChange={(e) => handleInputChange('skip_weak_signals', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="flex justify-between">
        <button
          onClick={handleReset}
          className="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors"
        >
          重置为默认
        </button>
        
        <button
          onClick={handleSave}
          disabled={saving}
          className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
        >
          {saving ? '保存中...' : '保存配置'}
        </button>
      </div>
    </div>
  );
};

export default SignalConfig;
