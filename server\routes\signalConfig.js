const express = require('express');
const router = express.Router();
const database = require('../config/database');
const { authenticateToken } = require('../middleware/auth');
const { getAllPresets, convertToDbFormat, detectConfigType } = require('../config/signalPresets');

// 获取用户信号配置
router.get('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    
    const config = await database.get(
      'SELECT * FROM signal_config WHERE user_id = ?',
      [userId]
    );

    if (!config) {
      // 如果没有配置，返回默认配置
      const defaultConfig = {
        user_id: userId,
        rsi_strong_threshold: 30,
        rsi_medium_threshold: 40,
        risk_strong: 0.01,           // 更新为新的风险参数
        risk_medium: 0.008,          // 更新为新的风险参数
        risk_weak: 0.005,            // 更新为新的风险参数
        atr_multiplier: 1.5,         // 新增ATR倍数
        risk_reward_ratio: 2.0,      // 新增风险回报比
        volume_multiplier: 1.5,
        enable_volume_filter: 1,
        enable_m_head_filter: 0,     // 关闭M头过滤，增加信号机会
        enable_w_bottom_bonus: 1,
        skip_weak_signals: 0,
        min_quality_score: 40        // 降低最低质量要求
      };

      res.json({
        success: true,
        data: defaultConfig
      });
    } else {
      res.json({
        success: true,
        data: config
      });
    }
  } catch (error) {
    console.error('获取信号配置失败:', error);
    res.status(500).json({
      success: false,
      message: '获取配置失败'
    });
  }
});

// 保存用户信号配置
router.post('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    console.log(`🔧 保存信号配置 - 用户ID: ${userId}`);
    console.log(`📝 配置数据:`, req.body);

    const {
      rsi_strong_threshold,
      rsi_medium_threshold,
      risk_strong,              // 更新为新的风险参数
      risk_medium,              // 更新为新的风险参数
      risk_weak,                // 更新为新的风险参数
      atr_multiplier,           // 新增ATR倍数
      risk_reward_ratio,        // 新增风险回报比
      volume_multiplier,
      enable_volume_filter,
      enable_m_head_filter,
      enable_w_bottom_bonus,
      skip_weak_signals,
      min_quality_score
    } = req.body;

    // 验证输入参数
    if (rsi_strong_threshold >= rsi_medium_threshold) {
      return res.status(400).json({
        success: false,
        message: '强信号RSI阈值必须小于中等信号阈值'
      });
    }

    if (risk_strong <= 0 || risk_medium <= 0 || risk_weak <= 0) {
      return res.status(400).json({
        success: false,
        message: '风险比例必须大于0'
      });
    }

    if (atr_multiplier <= 0 || risk_reward_ratio <= 0) {
      return res.status(400).json({
        success: false,
        message: 'ATR倍数和风险回报比必须大于0'
      });
    }

    if (min_quality_score < 0 || min_quality_score > 100) {
      return res.status(400).json({
        success: false,
        message: '最低质量评分必须在0-100之间'
      });
    }

    // 检查配置是否存在
    const existingConfig = await database.get(
      'SELECT user_id FROM signal_config WHERE user_id = ?',
      [userId]
    );

    if (existingConfig) {
      // 更新配置
      await database.run(`
        UPDATE signal_config SET
          rsi_strong_threshold = ?,
          rsi_medium_threshold = ?,
          risk_strong = ?,
          risk_medium = ?,
          risk_weak = ?,
          atr_multiplier = ?,
          risk_reward_ratio = ?,
          volume_multiplier = ?,
          enable_volume_filter = ?,
          enable_m_head_filter = ?,
          enable_w_bottom_bonus = ?,
          skip_weak_signals = ?,
          min_quality_score = ?,
          updated_at = CURRENT_TIMESTAMP
        WHERE user_id = ?
      `, [
        rsi_strong_threshold,
        rsi_medium_threshold,
        risk_strong,
        risk_medium,
        risk_weak,
        atr_multiplier,
        risk_reward_ratio,
        volume_multiplier,
        enable_volume_filter ? 1 : 0,
        enable_m_head_filter ? 1 : 0,
        enable_w_bottom_bonus ? 1 : 0,
        skip_weak_signals ? 1 : 0,
        min_quality_score,
        userId
      ]);
    } else {
      // 创建新配置
      await database.run(`
        INSERT INTO signal_config (
          user_id, rsi_strong_threshold, rsi_medium_threshold,
          risk_strong, risk_medium, risk_weak,
          atr_multiplier, risk_reward_ratio,
          volume_multiplier, enable_volume_filter, enable_m_head_filter,
          enable_w_bottom_bonus, skip_weak_signals, min_quality_score
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        userId,
        rsi_strong_threshold,
        rsi_medium_threshold,
        risk_strong,
        risk_medium,
        risk_weak,
        atr_multiplier,
        risk_reward_ratio,
        volume_multiplier,
        enable_volume_filter ? 1 : 0,
        enable_m_head_filter ? 1 : 0,
        enable_w_bottom_bonus ? 1 : 0,
        skip_weak_signals ? 1 : 0,
        min_quality_score
      ]);
    }

    console.log(`✅ 信号配置保存成功 - 用户ID: ${userId}`);

    res.json({
      success: true,
      message: '配置保存成功'
    });

  } catch (error) {
    console.error('保存信号配置失败:', error);
    res.status(500).json({
      success: false,
      message: '保存配置失败'
    });
  }
});

// 重置为默认配置
router.post('/reset', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;

    await database.run('DELETE FROM signal_config WHERE user_id = ?', [userId]);

    res.json({
      success: true,
      message: '配置已重置为默认值'
    });

  } catch (error) {
    console.error('重置信号配置失败:', error);
    res.status(500).json({
      success: false,
      message: '重置配置失败'
    });
  }
});

// 获取配置预设模板
router.get('/presets', authenticateToken, (req, res) => {
  const presets = getAllPresets();

  res.json({
    success: true,
    data: presets
  });
});

module.exports = router;
