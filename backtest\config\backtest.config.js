/**
 * 回测配置文件
 * 定义各种回测参数和策略配置
 */

// 基础回测配置
const backtestConfig = {
  // 资金设置
  initialCapital: 10000,    // 初始资金
  tradingFee: 0.001,        // 交易手续费 (0.1%)
  
  // 数据设置
  startIndex: 50,           // 开始索引（确保有足够数据计算指标）
  
  // 输出设置
  outputDir: './backtest_results',
  exportResults: true,
  
  // 进度报告
  progressInterval: 1000    // 每1000根K线报告一次进度
};

// 策略配置预设
const strategyConfigs = {
  // 保守型策略 - 平衡优化版
  conservative: {
    name: '保守优化型',
    rsi_strong_threshold: 25,    // 适度严格：从原来的25保持不变
    rsi_medium_threshold: 32,    // 适度收紧：从35降到32
    risk_strong: 0.004,          // 适度降低：0.4%资金风险
    risk_medium: 0.003,          // 适度降低：0.3%资金风险
    risk_weak: 0.002,            // 适度降低：0.2%资金风险
    atr_multiplier: 2.0,         // 适度扩大：从1.5提高到2.0
    risk_reward_ratio: 2.5,      // 适度提高：从2.0提高到2.5
    volume_multiplier: 1.8,      // 保持原有要求
    enable_volume_filter: true,
    enable_m_head_filter: false, // 暂时关闭，避免过度过滤
    enable_w_bottom_bonus: true,
    skip_weak_signals: false,    // 暂时不跳过弱信号
    min_quality_score: 55        // 适度提高：从50提高到55
  },

  // 平衡型策略 - 渐进优化版
  balanced: {
    name: '平衡优化型',
    rsi_strong_threshold: 28,    // 轻微收紧：从30到28
    rsi_medium_threshold: 38,    // 轻微收紧：从40到38
    risk_strong: 0.008,          // 适度降低：从1%到0.8%
    risk_medium: 0.006,          // 适度降低：从0.8%到0.6%
    risk_weak: 0.004,            // 适度降低：从0.5%到0.4%
    atr_multiplier: 1.8,         // 适度扩大：从1.5到1.8
    risk_reward_ratio: 2.2,      // 适度提高：从2.0到2.2
    volume_multiplier: 1.6,      // 轻微提高：从1.5到1.6
    enable_volume_filter: true,
    enable_m_head_filter: false, // 暂时关闭
    enable_w_bottom_bonus: true,
    skip_weak_signals: false,    // 暂时不跳过
    min_quality_score: 45        // 轻微提高：从40到45
  },

  // 激进型策略
  aggressive: {
    name: '激进型',
    rsi_strong_threshold: 35,
    rsi_medium_threshold: 45,
    risk_strong: 0.015,      // 1.5%资金风险
    risk_medium: 0.012,      // 1.2%资金风险
    risk_weak: 0.008,        // 0.8%资金风险
    atr_multiplier: 1.5,
    risk_reward_ratio: 2.5,
    volume_multiplier: 1.2,
    enable_volume_filter: false,
    enable_m_head_filter: false,
    enable_w_bottom_bonus: true,
    skip_weak_signals: false,
    min_quality_score: 30
  },

  // 超保守型（测试用）
  ultraConservative: {
    name: '超保守型',
    rsi_strong_threshold: 20,
    rsi_medium_threshold: 25,
    risk_strong: 0.002,      // 0.2%资金风险
    risk_medium: 0.001,      // 0.1%资金风险
    risk_weak: 0.0005,       // 0.05%资金风险
    atr_multiplier: 2.0,     // 更大的止损距离
    risk_reward_ratio: 3.0,  // 更高的风险回报比
    volume_multiplier: 2.0,
    enable_volume_filter: true,
    enable_m_head_filter: true,
    enable_w_bottom_bonus: true,
    skip_weak_signals: true, // 跳过弱信号
    min_quality_score: 60
  },

  // 质量优先策略 - 适度优化版
  qualityFirst: {
    name: '质量优先型',
    rsi_strong_threshold: 22,    // 严格但不极端：从15调整到22
    rsi_medium_threshold: 28,    // 严格但不极端：从20调整到28
    risk_strong: 0.006,          // 适中风险：0.6%资金风险
    risk_medium: 0.004,          // 适中风险：0.4%资金风险
    risk_weak: 0.002,            // 低风险：0.2%资金风险
    atr_multiplier: 2.2,         // 较大的止损空间
    risk_reward_ratio: 3.0,      // 高盈亏比：从4.0调整到3.0
    volume_multiplier: 2.2,      // 较严格的成交量要求：从3.0调整到2.2
    enable_volume_filter: true,
    enable_m_head_filter: false, // 暂时关闭
    enable_w_bottom_bonus: true,
    skip_weak_signals: true,     // 跳过弱信号
    min_quality_score: 60        // 高质量要求：从80调整到60
  },

  // 高频型（测试用）- 保留原版本用于对比
  highFrequency: {
    name: '高频型',
    rsi_strong_threshold: 40,
    rsi_medium_threshold: 50,
    risk_strong: 0.02,       // 2%资金风险
    risk_medium: 0.015,      // 1.5%资金风险
    risk_weak: 0.01,         // 1%资金风险
    atr_multiplier: 1.0,     // 更小的止损距离
    risk_reward_ratio: 1.5,  // 更低的风险回报比
    volume_multiplier: 1.0,
    enable_volume_filter: false,
    enable_m_head_filter: false,
    enable_w_bottom_bonus: false,
    skip_weak_signals: false,
    min_quality_score: 20    // 更低的质量要求
  }
};

// 回测场景配置
const backtestScenarios = {
  // 快速测试（最近3个月）
  quickTest: {
    name: '快速测试',
    description: '最近3个月数据，快速验证策略',
    // 需要在运行时根据数据长度计算具体索引
    relativePeriod: {
      type: 'recent',
      months: 3
    }
  },

  // 完整回测（全部历史数据）
  fullBacktest: {
    name: '完整回测',
    description: '使用全部历史数据进行完整回测',
    relativePeriod: {
      type: 'full'
    }
  },

  // 牛市测试（2023年）
  bullMarket: {
    name: '牛市测试',
    description: '2023年牛市期间的策略表现',
    absolutePeriod: {
      start: '2023-01-01',
      end: '2023-12-31'
    }
  },

  // 熊市测试（2022年）
  bearMarket: {
    name: '熊市测试',
    description: '2022年熊市期间的策略表现',
    absolutePeriod: {
      start: '2022-01-01',
      end: '2022-12-31'
    }
  },

  // 震荡市测试
  sidewaysMarket: {
    name: '震荡市测试',
    description: '震荡市场环境下的策略表现',
    // 需要根据实际数据选择震荡期间
    relativePeriod: {
      type: 'custom',
      description: '需要手动指定震荡期间'
    }
  }
};

// 优化参数范围
const optimizationRanges = {
  rsi_strong_threshold: {
    min: 15,
    max: 35,
    step: 5
  },
  rsi_medium_threshold: {
    min: 25,
    max: 50,
    step: 5
  },
  risk_strong: {
    min: 0.005,
    max: 0.02,
    step: 0.0025
  },
  atr_multiplier: {
    min: 1.0,
    max: 2.5,
    step: 0.25
  },
  risk_reward_ratio: {
    min: 1.5,
    max: 3.0,
    step: 0.25
  },
  min_quality_score: {
    min: 20,
    max: 70,
    step: 10
  }
};

// 生成参数组合的辅助函数
function generateParameterCombinations(baseConfig, paramRanges, maxCombinations = 100) {
  const combinations = [];
  const paramNames = Object.keys(paramRanges);
  
  // 简单的网格搜索（可以改进为更智能的优化算法）
  function generateRecursive(currentConfig, paramIndex) {
    if (paramIndex >= paramNames.length) {
      combinations.push({ ...currentConfig });
      return;
    }
    
    if (combinations.length >= maxCombinations) {
      return;
    }
    
    const paramName = paramNames[paramIndex];
    const range = paramRanges[paramName];
    
    for (let value = range.min; value <= range.max; value += range.step) {
      const newConfig = { ...currentConfig };
      newConfig[paramName] = value;
      generateRecursive(newConfig, paramIndex + 1);
    }
  }
  
  generateRecursive(baseConfig, 0);
  return combinations.slice(0, maxCombinations);
}

module.exports = {
  backtestConfig,
  strategyConfigs,
  backtestScenarios,
  optimizationRanges,
  generateParameterCombinations
};
