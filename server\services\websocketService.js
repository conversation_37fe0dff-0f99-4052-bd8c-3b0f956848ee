const WebSocket = require('ws');
const signalService = require('./signalService');
const notificationService = require('./notificationService');
const database = require('../config/database');

class WebSocketService {
  constructor() {
    this.wss = null;
    this.clients = new Map(); // userId -> WebSocket connection
    this.priceUpdateInterval = null;
  }

  // 初始化WebSocket服务器
  initialize(server) {
    this.wss = new WebSocket.Server({ 
      server,
      path: '/ws'
    });

    this.wss.on('connection', (ws, req) => {
      console.log('新的WebSocket连接');
      
      ws.on('message', async (message) => {
        try {
          const data = JSON.parse(message);
          await this.handleMessage(ws, data);
        } catch (error) {
          console.error('处理WebSocket消息失败:', error);
          ws.send(JSON.stringify({
            type: 'error',
            message: '消息格式错误'
          }));
        }
      });

      ws.on('close', () => {
        // 从客户端映射中移除
        for (const [userId, client] of this.clients.entries()) {
          if (client === ws) {
            this.clients.delete(userId);
            console.log(`用户 ${userId} 断开WebSocket连接`);
            break;
          }
        }
      });

      ws.on('error', (error) => {
        console.error('WebSocket错误:', error);
      });
    });

    // 启动价格更新定时器
    this.startPriceUpdates();
    
    console.log('WebSocket服务器已启动');
  }

  // 处理客户端消息
  async handleMessage(ws, data) {
    switch (data.type) {
      case 'auth':
        await this.handleAuth(ws, data);
        break;
      case 'subscribe_prices':
        await this.handleSubscribePrices(ws, data);
        break;
      case 'unsubscribe_prices':
        await this.handleUnsubscribePrices(ws, data);
        break;
      default:
        ws.send(JSON.stringify({
          type: 'error',
          message: '未知的消息类型'
        }));
    }
  }

  // 处理用户认证
  async handleAuth(ws, data) {
    try {
      const { token } = data;
      
      // 验证JWT token（这里简化处理，实际应该使用JWT验证）
      if (!token) {
        ws.send(JSON.stringify({
          type: 'auth_error',
          message: '缺少认证token'
        }));
        return;
      }

      // 这里应该验证token并获取用户ID
      // 为了演示，我们假设token就是用户ID
      const userId = data.userId;
      
      if (userId) {
        this.clients.set(userId, ws);
        ws.userId = userId;
        
        ws.send(JSON.stringify({
          type: 'auth_success',
          message: '认证成功'
        }));
        
        console.log(`用户 ${userId} 已认证WebSocket连接`);
      } else {
        ws.send(JSON.stringify({
          type: 'auth_error',
          message: '无效的认证token'
        }));
      }
    } catch (error) {
      console.error('WebSocket认证失败:', error);
      ws.send(JSON.stringify({
        type: 'auth_error',
        message: '认证失败'
      }));
    }
  }

  // 处理价格订阅
  async handleSubscribePrices(ws, data) {
    const { assets } = data;
    
    if (!ws.userId) {
      ws.send(JSON.stringify({
        type: 'error',
        message: '请先进行认证'
      }));
      return;
    }

    ws.subscribedAssets = assets || ['bitcoin', 'ethereum', 'binancecoin'];
    
    ws.send(JSON.stringify({
      type: 'subscribe_success',
      assets: ws.subscribedAssets
    }));
    
    console.log(`用户 ${ws.userId} 订阅价格更新:`, ws.subscribedAssets);
  }

  // 处理取消价格订阅
  async handleUnsubscribePrices(ws, data) {
    ws.subscribedAssets = [];
    
    ws.send(JSON.stringify({
      type: 'unsubscribe_success'
    }));
    
    console.log(`用户 ${ws.userId} 取消价格订阅`);
  }

  // 启动价格更新定时器
  startPriceUpdates() {
    // 每30秒更新一次价格
    this.priceUpdateInterval = setInterval(async () => {
      await this.broadcastPriceUpdates();
    }, 30000);
  }

  // 广播价格更新
  async broadcastPriceUpdates() {
    try {
      const assets = ['bitcoin', 'ethereum', 'binancecoin'];
      const priceUpdates = {};

      // 获取所有资产的价格
      for (const asset of assets) {
        try {
          const priceData = await signalService.getPriceData(asset);
          priceUpdates[asset] = {
            price: priceData.usd,
            change_24h: priceData.usd_24h_change,
            timestamp: new Date().toISOString()
          };
        } catch (error) {
          console.error(`获取 ${asset} 价格失败:`, error);
        }
      }

      // 向所有订阅的客户端发送价格更新
      for (const [userId, ws] of this.clients.entries()) {
        if (ws.readyState === WebSocket.OPEN && ws.subscribedAssets) {
          const userPriceUpdates = {};
          
          for (const asset of ws.subscribedAssets) {
            if (priceUpdates[asset]) {
              userPriceUpdates[asset] = priceUpdates[asset];
            }
          }

          if (Object.keys(userPriceUpdates).length > 0) {
            ws.send(JSON.stringify({
              type: 'price_update',
              data: userPriceUpdates
            }));
          }
        }
      }
    } catch (error) {
      console.error('广播价格更新失败:', error);
    }
  }

  // 向特定用户发送消息
  sendToUser(userId, message) {
    const ws = this.clients.get(userId);
    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
      return true;
    }
    return false;
  }

  // 向所有用户广播消息
  broadcast(message) {
    for (const [userId, ws] of this.clients.entries()) {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify(message));
      }
    }
  }

  // 发送新信号通知
  async sendSignalNotification(userId, signal) {
    const message = {
      type: 'new_signal',
      data: signal
    };
    
    const sent = this.sendToUser(userId, message);
    if (sent) {
      console.log(`向用户 ${userId} 发送新信号通知`);
    }
  }

  // 发送交易更新通知
  async sendTradeUpdate(userId, trade) {
    const message = {
      type: 'trade_update',
      data: trade
    };
    
    const sent = this.sendToUser(userId, message);
    if (sent) {
      console.log(`向用户 ${userId} 发送交易更新`);
    }
  }

  // 关闭WebSocket服务
  close() {
    if (this.priceUpdateInterval) {
      clearInterval(this.priceUpdateInterval);
    }
    
    if (this.wss) {
      this.wss.close();
    }
    
    console.log('WebSocket服务已关闭');
  }
}

module.exports = new WebSocketService();
