const axios = require('axios');
const database = require('../config/database');
require('dotenv').config();

class NotificationService {
  constructor() {
    this.ntfyUrl = process.env.NTFY_SERVER_URL || 'https://home.917999.xyz:18030';
    this.topic = process.env.NTFY_TOPIC || 'scalp-alert';
  }

  // 发送通知到ntfy服务
  async sendNotification(title, message, priority = 'default', tags = []) {
    try {
      const response = await axios.post(`${this.ntfyUrl}/${this.topic}`, message, {
        headers: {
          'Title': title,
          'Priority': priority,
          'Tags': tags.join(','),
          'Content-Type': 'text/plain'
        }
      });

      console.log('通知发送成功:', title);
      return response.data;
    } catch (error) {
      console.error('发送通知失败:', error.message);
      throw error;
    }
  }

  // 保存通知记录到数据库
  async saveNotification(userId, type, title, message) {
    try {
      const result = await database.run(
        'INSERT INTO notifications (user_id, type, title, message) VALUES (?, ?, ?, ?)',
        [userId, type, title, message]
      );
      
      return result.id;
    } catch (error) {
      console.error('保存通知记录失败:', error.message);
      throw error;
    }
  }

  // 发送交易信号通知
  async sendSignalNotification(userId, signal) {
    const assetName = this.getAssetDisplayName(signal.asset);
    const title = `🚨 ${assetName} 交易信号`;
    const message = `
信号类型: ${signal.signal_type === 'buy' ? '🟢 买入' : '🔴 卖出'}
当前价格: $${signal.price?.toLocaleString()}
RSI: ${signal.rsi?.toFixed(1)}
信号强度: ${this.getStrengthText(signal.signal_strength)}
建议仓位: $${signal.suggested_position?.toLocaleString()}
入场价格: $${signal.entry_price?.toFixed(2)}
止损价格: $${signal.stop_loss?.toFixed(2)}
止盈价格: $${signal.take_profit?.toFixed(2)}
${signal.pattern !== 'None' ? `形态: ${signal.pattern}` : ''}
    `.trim();

    try {
      // 发送到ntfy
      await this.sendNotification(
        title,
        message,
        signal.signal_strength === 'strong' ? 'high' : 'default',
        ['trading', 'signal']
      );

      // 保存到数据库
      await this.saveNotification(userId, 'signal', title, message);
      
      return true;
    } catch (error) {
      console.error('发送信号通知失败:', error.message);
      return false;
    }
  }

  // 发送风险警告通知
  async sendRiskWarning(userId, warningType, details) {
    let title, message;

    switch (warningType) {
      case 'daily_limit':
        title = '⚠️ 每日交易次数限制';
        message = `您今日已达到最大交易次数限制 (${details.current}/${details.max})，请明日再进行交易。`;
        break;
      case 'loss_limit':
        title = '🚨 亏损限制警告';
        message = `累计亏损已达 $${details.current_loss}，接近限制 $${details.loss_limit}。请谨慎交易！`;
        break;
      case 'position_deviation':
        title = '📊 仓位偏离建议';
        message = `实际仓位 $${details.actual} 超出建议仓位 $${details.suggested}，风险增加。`;
        break;
      default:
        title = '⚠️ 风险提醒';
        message = details.message || '请注意交易风险';
    }

    try {
      // 发送到ntfy
      await this.sendNotification(
        title, 
        message, 
        'high',
        ['warning', 'risk']
      );

      // 保存到数据库
      await this.saveNotification(userId, 'warning', title, message);
      
      return true;
    } catch (error) {
      console.error('发送风险警告失败:', error.message);
      return false;
    }
  }

  // 发送统计报告通知
  async sendStatisticsReport(userId, report) {
    const title = `📈 ${report.period === 'daily' ? '日' : report.period === 'weekly' ? '周' : '月'}报告`;
    const message = `
交易次数: ${report.total_trades}
胜率: ${(report.win_rate * 100).toFixed(1)}%
总盈亏: $${report.total_profit > 0 ? '+' : ''}${report.total_profit}
策略遵守率: ${(report.compliance_rate * 100).toFixed(1)}%
平均持仓时间: ${Math.round(report.avg_hold_time / 60)}小时
    `.trim();

    try {
      // 发送到ntfy
      await this.sendNotification(
        title, 
        message, 
        'default',
        ['report', 'statistics']
      );

      // 保存到数据库
      await this.saveNotification(userId, 'report', title, message);
      
      return true;
    } catch (error) {
      console.error('发送统计报告失败:', error.message);
      return false;
    }
  }

  // 获取用户通知历史
  async getUserNotifications(userId, limit = 50, offset = 0) {
    try {
      const notifications = await database.query(
        `SELECT * FROM notifications 
         WHERE user_id = ? 
         ORDER BY sent_at DESC 
         LIMIT ? OFFSET ?`,
        [userId, limit, offset]
      );

      return notifications;
    } catch (error) {
      console.error('获取通知历史失败:', error.message);
      throw error;
    }
  }

  // 标记通知为已读
  async markAsRead(notificationId, userId) {
    try {
      const result = await database.run(
        'UPDATE notifications SET is_read = 1 WHERE id = ? AND user_id = ?',
        [notificationId, userId]
      );

      return result.changes > 0;
    } catch (error) {
      console.error('标记通知已读失败:', error.message);
      throw error;
    }
  }

  // 获取未读通知数量
  async getUnreadCount(userId) {
    try {
      const result = await database.get(
        'SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND is_read = 0',
        [userId]
      );

      return result.count;
    } catch (error) {
      console.error('获取未读通知数量失败:', error.message);
      throw error;
    }
  }

  // 获取信号强度文本
  getStrengthText(strength) {
    switch (strength) {
      case 'strong': return '强';
      case 'medium': return '中';
      case 'weak': return '弱';
      case 'skip': return '跳过';
      default: return '未知';
    }
  }

  // 获取资产显示名称
  getAssetDisplayName(asset) {
    const assetMap = {
      'bitcoin': 'BTC',
      'ethereum': 'ETH',
      'binancecoin': 'BNB',
      'solana': 'SOL'
    };
    return assetMap[asset] || asset.toUpperCase();
  }
}

module.exports = new NotificationService();
