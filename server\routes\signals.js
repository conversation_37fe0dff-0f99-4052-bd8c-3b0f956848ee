const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const database = require('../config/database');
const signalService = require('../services/signalService');
const notificationService = require('../services/notificationService');
const signalGenerator = require('../services/signalGenerator');
const { detectConfigType } = require('../config/signalPresets');

const router = express.Router();

// 获取最新信号
router.get('/latest', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { asset, limit = 5 } = req.query;

    let query, params;
    if (asset && asset !== 'all') {
      // 查询特定资产的信号
      query = `SELECT * FROM signals
               WHERE user_id = ? AND asset = ? AND is_active = 1
               ORDER BY timestamp DESC
               LIMIT ?`;
      params = [userId, asset, parseInt(limit)];
    } else {
      // 查询所有资产的信号
      query = `SELECT * FROM signals
               WHERE user_id = ? AND is_active = 1
               ORDER BY timestamp DESC
               LIMIT ?`;
      params = [userId, parseInt(limit)];
    }

    const signals = await database.query(query, params);

    res.json({
      success: true,
      data: signals
    });

  } catch (error) {
    console.error('获取信号失败:', error.message);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 生成新信号
router.post('/generate', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { asset = 'bitcoin' } = req.body;

    console.log(`🚀 手动信号生成开始 - 用户ID: ${userId}, 资产: ${asset}`);

    // 获取用户设置
    const user = await database.get(
      'SELECT capital, risk_percentage, max_daily_trades FROM users WHERE id = ?',
      [userId]
    );

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 获取用户信号配置
    const userConfig = await database.get(
      'SELECT * FROM signal_config WHERE user_id = ?',
      [userId]
    );

    // 使用用户配置或默认配置
    const config = userConfig || {
      rsi_strong_threshold: 30,
      rsi_medium_threshold: 40,
      risk_strong: 0.01,           // 更新为新的风险参数
      risk_medium: 0.008,          // 更新为新的风险参数
      risk_weak: 0.005,            // 更新为新的风险参数
      atr_multiplier: 1.5,         // 新增ATR倍数
      risk_reward_ratio: 2.0,      // 新增风险回报比
      volume_multiplier: 1.5,
      enable_volume_filter: 1,
      enable_m_head_filter: 0,     // 关闭M头过滤，增加信号机会
      enable_w_bottom_bonus: 1,
      skip_weak_signals: 0,
      min_quality_score: 40        // 降低最低质量要求
    };

    // 检测配置类型
    const configType = detectConfigType(config);
    console.log(`⚙️ 使用配置: ${configType}`);
    console.log(`📊 RSI阈值: 强=${config.rsi_strong_threshold}, 中=${config.rsi_medium_threshold}`);
    console.log(`💼 风险设置: 强=${config.risk_strong}, 中=${config.risk_medium}, 弱=${config.risk_weak}`);
    console.log(`🔍 质量阈值: ${config.min_quality_score}分`);

    // 检查今日交易次数
    const today = new Date().toISOString().split('T')[0];
    const todayTrades = await database.get(
      'SELECT COUNT(*) as count FROM trades WHERE user_id = ? AND DATE(buy_time) = ?',
      [userId, today]
    );

    if (todayTrades.count >= user.max_daily_trades) {
      return res.status(400).json({
        success: false,
        message: `今日交易次数已达上限 (${todayTrades.count}/${user.max_daily_trades})`
      });
    }

    // 使用统一的信号生成器
    console.log(`🎯 使用统一信号生成器...`);
    let generatedSignal;
    try {
      generatedSignal = await signalGenerator.generateSignal(asset, config);
    } catch (error) {
      console.error(`❌ 信号生成失败: ${error.message}`);
      return res.status(500).json({
        success: false,
        message: `信号生成失败: ${error.message}`
      });
    }

    // 提取关键数据用于后续处理
    const currentPrice = generatedSignal.price;
    const rsi = generatedSignal.rsi;
    const emaShort = generatedSignal.ema_short;
    const emaLong = generatedSignal.ema_long;
    const trend4h = generatedSignal.trend_4h;
    const pattern = generatedSignal.pattern;
    const macd = generatedSignal.macd;
    const atr = generatedSignal.atr;
    const volumeRatio = generatedSignal.volume_ratio;
    const mtfAnalysis = generatedSignal.mtf_analysis;
    const analysisResult = {
      strength: generatedSignal.signal_strength,
      quality_score: generatedSignal.quality_score,
      suggested_position: generatedSignal.suggested_position,
      analysis_details: generatedSignal.analysis_details,
      description: generatedSignal.description
    };
    const signalType = generatedSignal.signal_type;
    const signalReason = generatedSignal.signal_reason;

    console.log(`📊 统一生成结果 - 类型: ${signalType}, 强度: ${analysisResult.strength}, 评分: ${analysisResult.quality_score}`);

    // 技术指标已由统一信号生成器计算完成
    console.log(`📈 RSI: ${rsi}, EMA短: ${emaShort}, EMA长: ${emaLong}, 趋势: ${trend4h}, 形态: ${pattern}`);
    console.log(`📊 MACD: ${macd.macd.toFixed(4)}, 信号线: ${macd.signal.toFixed(4)}, 柱状线: ${macd.histogram.toFixed(4)}`);
    console.log(`📏 ATR: ${atr.toFixed(4)} (动态止损参考)`);
    console.log(`📊 日线趋势: ${mtfAnalysis.daily.trend} (强度: ${mtfAnalysis.daily.strength.toFixed(2)})`);
    console.log(`📊 4小时趋势: ${mtfAnalysis.h4.trend} (强度: ${mtfAnalysis.h4.strength.toFixed(2)})`);
    console.log(`📊 1小时状态: ${mtfAnalysis.h1.trend} (强度: ${mtfAnalysis.h1.strength.toFixed(2)})`);
    console.log(`🎯 综合建议: ${mtfAnalysis.recommendation}`);

    // 信号分析已由统一信号生成器完成
    console.log(`📊 分析结果 - 强度: ${analysisResult.strength}, 评分: ${analysisResult.quality_score}, 仓位: ${analysisResult.suggested_position}`);

    // 信号类型判断已由统一信号生成器完成
    const signalTypeText = signalType === 'buy' ? '买入' : signalType === 'sell' ? '卖出' : signalType === 'hold' ? '持有' : '无信号';
    console.log(`📈 信号类型: ${signalTypeText} (${signalReason})`);

    // 显示动态风险管理信息
    if (signalType !== 'hold') {
      console.log(`💰 动态仓位: ${(analysisResult.suggested_position * 100).toFixed(2)}% (基于ATR: ${atr.toFixed(4)})`);
      console.log(`🛡️ 动态止损: ${generatedSignal.stop_loss.toFixed(2)}`);
      console.log(`🎯 动态止盈: ${generatedSignal.take_profit.toFixed(2)} (1:${config.risk_reward_ratio}风险收益比)`);
    }

    // 检查是否应该跳过信号
    const shouldSkip = generatedSignal.should_skip;
    const reason = generatedSignal.skip_reason;

    if (shouldSkip) {
      console.log(`⏭️ 信号被跳过: ${reason}，不记录到数据库`);
      return res.json({
        success: true,
        message: `信号检测完成，但${reason}`,
        data: null
      });
    }

    console.log(`✅ 生成有效信号: ${analysisResult.strength}强度`);

    // 保存信号到数据库（使用新的字段结构）
    console.log(`💾 保存信号到数据库...`);
    const signalResult = await database.run(
      `INSERT INTO signals (
        user_id, asset, price, rsi, ema_short, ema_long,
        trend_4h, pattern, signal_type, signal_strength,
        suggested_position, risk_amount, stop_loss_distance,
        volume_ratio, quality_score, m_head_detected, w_bottom_detected,
        entry_price, stop_loss, take_profit, is_active
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        userId, asset, currentPrice, rsi, emaShort, emaLong,
        trend4h, pattern, signalType, analysisResult.strength,
        actualPositionAmount, // 使用实际美元金额
        user.capital * user.risk_percentage, // risk_amount
        Math.abs(generatedSignal.stop_loss - currentPrice) / currentPrice, // stop_loss_distance
        volumeRatio,
        analysisResult.quality_score,
        generatedSignal.m_head_detected ? 1 : 0,
        generatedSignal.w_bottom_detected ? 1 : 0,
        generatedSignal.entry_price, // entry_price
        generatedSignal.stop_loss, // 动态止损
        generatedSignal.take_profit, // 动态止盈
        1 // is_active: 所有保存的信号都是有效的
      ]
    );

    // 计算实际的美元仓位金额
    const actualPositionAmount = analysisResult.suggested_position * user.capital;

    const signal = {
      id: signalResult.id,
      ...generatedSignal, // 使用统一生成器的完整结果
      suggested_position: actualPositionAmount, // 转换为实际美元金额
      risk_amount: user.capital * user.risk_percentage,
      analysis_details: analysisResult.analysis_details,
      description: analysisResult.description
    };

    // 只为有效信号发送通知
    if (!shouldSkip) {
      await notificationService.sendSignalNotification(userId, signal);
    }

    const message = shouldSkip ?
      `信号已记录但${reason}` :
      '有效信号生成成功';

    console.log(`✅ ${message}`);

    res.json({
      success: true,
      message: message,
      data: signal
    });

  } catch (error) {
    console.error('❌ 生成信号失败:', error.message);
    console.error('❌ 错误堆栈:', error.stack);
    res.status(500).json({
      success: false,
      message: '服务器内部错误: ' + error.message
    });
  }
});

// 获取信号统计数据 - 必须在 /:id 路由之前
router.get('/stats/:timeRange?', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const timeRange = req.params.timeRange || '7d';

    // 计算时间范围
    const days = timeRange === '30d' ? 30 : timeRange === '90d' ? 90 : 7;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // 获取时间范围内的信号
    console.log(`统计查询 - 用户ID: ${userId}, 开始时间: ${startDate.toISOString()}`);

    const signals = await database.query(
      `SELECT * FROM signals
       WHERE user_id = ? AND timestamp >= ?
       ORDER BY timestamp DESC`,
      [userId, startDate.toISOString()]
    );

    console.log(`找到 ${signals.length} 个信号记录`);

    // 如果没有信号，先查询该用户的所有信号
    if (signals.length === 0) {
      const allSignals = await database.query(
        'SELECT COUNT(*) as total FROM signals WHERE user_id = ?',
        [userId]
      );
      console.log(`用户总信号数: ${allSignals[0]?.total || 0}`);
    }

    // 计算统计数据
    const stats = {
      totalSignals: signals.length,
      avgQualityScore: signals.length > 0 ?
        Math.round(signals.reduce((sum, s) => sum + (s.quality_score || 0), 0) / signals.length) : 0,
      strongSignalRatio: signals.length > 0 ?
        Math.round((signals.filter(s => s.signal_strength === 'strong').length / signals.length) * 100) : 0
    };

    // 信号强度分布
    const strengthCounts = signals.reduce((acc, signal) => {
      const strength = signal.signal_strength || 'unknown';
      acc[strength] = (acc[strength] || 0) + 1;
      return acc;
    }, {});

    stats.strengthDistribution = Object.entries(strengthCounts).map(([strength, count]) => ({
      strength,
      count
    }));

    // 如果没有数据，提供默认的空分布
    if (stats.strengthDistribution.length === 0) {
      stats.strengthDistribution = [
        { strength: 'strong', count: 0 },
        { strength: 'medium', count: 0 },
        { strength: 'weak', count: 0 },
        { strength: 'skip', count: 0 }
      ];
    }

    // 质量评分分布
    const qualityRanges = [
      { range: '0-40', min: 0, max: 40 },
      { range: '40-60', min: 40, max: 60 },
      { range: '60-80', min: 60, max: 80 },
      { range: '80-100', min: 80, max: 100 }
    ];

    stats.qualityDistribution = qualityRanges.map(range => ({
      range: range.range,
      count: signals.filter(s =>
        (s.quality_score || 0) >= range.min && (s.quality_score || 0) < range.max
      ).length
    }));

    // 币种分布
    const assetCounts = signals.reduce((acc, signal) => {
      const asset = signal.asset || 'unknown';
      acc[asset] = (acc[asset] || 0) + 1;
      return acc;
    }, {});

    stats.assetDistribution = Object.entries(assetCounts).map(([asset, count]) => ({
      asset,
      count
    }));

    // 如果没有数据，提供默认的空分布
    if (stats.assetDistribution.length === 0) {
      stats.assetDistribution = [
        { asset: 'bitcoin', count: 0 },
        { asset: 'ethereum', count: 0 },
        { asset: 'binancecoin', count: 0 },
        { asset: 'solana', count: 0 }
      ];
    }

    // 每日信号趋势
    const dailyCounts = {};
    signals.forEach(signal => {
      const date = new Date(signal.timestamp).toISOString().split('T')[0];
      dailyCounts[date] = (dailyCounts[date] || 0) + 1;
    });

    stats.dailySignals = Object.entries(dailyCounts).map(([date, count]) => ({
      date,
      count
    })).sort((a, b) => new Date(a.date) - new Date(b.date));

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('获取信号统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取信号统计失败'
    });
  }
});

// 获取信号详情
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const signalId = req.params.id;

    const signal = await database.get(
      'SELECT * FROM signals WHERE id = ? AND user_id = ?',
      [signalId, userId]
    );

    if (!signal) {
      return res.status(404).json({
        success: false,
        message: '信号不存在'
      });
    }

    // 获取相关的交易记录（如果有）
    const relatedTrade = await database.get(
      'SELECT * FROM trades WHERE signal_id = ? AND user_id = ?',
      [signalId, userId]
    );

    // 构建详细的信号数据
    const detailedSignal = {
      ...signal,
      // 添加计算字段
      risk_reward_ratio: signal.take_profit && signal.stop_loss && signal.entry_price ?
        ((signal.take_profit - signal.entry_price) / (signal.entry_price - signal.stop_loss)).toFixed(2) : null,

      // 添加百分比字段
      stop_loss_percent: signal.stop_loss && signal.entry_price ?
        (((signal.entry_price - signal.stop_loss) / signal.entry_price) * 100).toFixed(2) : null,

      take_profit_percent: signal.take_profit && signal.entry_price ?
        (((signal.take_profit - signal.entry_price) / signal.entry_price) * 100).toFixed(2) : null,

      // 添加相关交易信息
      related_trade: relatedTrade,

      // 添加信号状态
      is_executed: !!relatedTrade,
      execution_status: relatedTrade ? relatedTrade.status : 'not_executed',

      // 添加分析详情（如果存在）
      analysis_summary: {
        rsi_status: signal.rsi < 30 ? 'oversold' : signal.rsi > 70 ? 'overbought' : 'neutral',
        ema_trend: signal.ema_short > signal.ema_long ? 'bullish' : 'bearish',
        trend_4h_status: signal.trend_4h,
        pattern_detected: signal.pattern !== 'None' ? signal.pattern : null,
        volume_status: signal.volume_ratio > 1.5 ? 'high' : signal.volume_ratio > 1.2 ? 'normal' : 'low'
      }
    };

    res.json({
      success: true,
      data: detailedSignal
    });

  } catch (error) {
    console.error('获取信号详情失败:', error.message);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 停用信号
router.put('/:id/deactivate', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const signalId = req.params.id;

    const result = await database.run(
      'UPDATE signals SET is_active = 0 WHERE id = ? AND user_id = ?',
      [signalId, userId]
    );

    if (result.changes === 0) {
      return res.status(404).json({
        success: false,
        message: '信号不存在'
      });
    }

    res.json({
      success: true,
      message: '信号已停用'
    });

  } catch (error) {
    console.error('停用信号失败:', error.message);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 获取信号历史
router.get('/history/:asset', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const asset = req.params.asset;
    const { page = 1, limit = 20 } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);

    const signals = await database.query(
      `SELECT * FROM signals 
       WHERE user_id = ? AND asset = ?
       ORDER BY timestamp DESC 
       LIMIT ? OFFSET ?`,
      [userId, asset, parseInt(limit), offset]
    );

    // 获取总数
    const totalResult = await database.get(
      'SELECT COUNT(*) as total FROM signals WHERE user_id = ? AND asset = ?',
      [userId, asset]
    );

    res.json({
      success: true,
      data: {
        signals,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalResult.total,
          pages: Math.ceil(totalResult.total / parseInt(limit))
        }
      }
    });

  } catch (error) {
    console.error('获取信号历史失败:', error.message);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 获取策略表现数据
router.get('/performance/:timeRange?', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const timeRange = req.params.timeRange || '30d';

    // 计算时间范围
    const days = timeRange === '90d' ? 90 : timeRange === '7d' ? 7 : 30;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    console.log(`策略表现查询 - 用户ID: ${userId}, 时间范围: ${timeRange}`);

    // 获取信号和对应的交易数据
    const signalsWithTrades = await database.query(`
      SELECT
        s.*,
        t.id as trade_id,
        t.buy_price,
        t.sell_price,
        t.profit_loss,
        t.status as trade_status,
        t.buy_time,
        t.sell_time
      FROM signals s
      LEFT JOIN trades t ON s.id = t.signal_id
      WHERE s.user_id = ? AND s.timestamp >= ?
      ORDER BY s.timestamp DESC
    `, [userId, startDate.toISOString()]);

    console.log(`找到 ${signalsWithTrades.length} 个信号记录`);

    // 计算策略表现指标
    const totalSignals = signalsWithTrades.length;
    const signalsWithTrades_count = signalsWithTrades.filter(s => s.trade_id).length;
    const closedTrades = signalsWithTrades.filter(s => s.trade_status === 'closed');
    const profitableTrades = closedTrades.filter(s => s.profit_loss > 0);

    const totalProfit = closedTrades.reduce((sum, s) => sum + (s.profit_loss || 0), 0);
    const totalLoss = closedTrades.filter(s => s.profit_loss < 0)
                                 .reduce((sum, s) => sum + Math.abs(s.profit_loss || 0), 0);

    // 按信号强度分组统计
    const strengthPerformance = ['strong', 'medium', 'weak'].map(strength => {
      const strengthSignals = signalsWithTrades.filter(s => s.signal_strength === strength);
      const strengthTrades = strengthSignals.filter(s => s.trade_id);
      const strengthClosed = strengthSignals.filter(s => s.trade_status === 'closed');
      const strengthProfitable = strengthClosed.filter(s => s.profit_loss > 0);

      return {
        strength,
        total_signals: strengthSignals.length,
        traded_signals: strengthTrades.length,
        closed_trades: strengthClosed.length,
        profitable_trades: strengthProfitable.length,
        win_rate: strengthClosed.length > 0 ?
          Math.round((strengthProfitable.length / strengthClosed.length) * 100) : 0,
        avg_profit: strengthClosed.length > 0 ?
          strengthClosed.reduce((sum, s) => sum + (s.profit_loss || 0), 0) / strengthClosed.length : 0
      };
    });

    // 按币种分组统计
    const assetPerformance = ['bitcoin', 'ethereum', 'binancecoin', 'solana'].map(asset => {
      const assetSignals = signalsWithTrades.filter(s => s.asset === asset);
      const assetTrades = assetSignals.filter(s => s.trade_id);
      const assetClosed = assetSignals.filter(s => s.trade_status === 'closed');
      const assetProfitable = assetClosed.filter(s => s.profit_loss > 0);

      return {
        asset,
        total_signals: assetSignals.length,
        traded_signals: assetTrades.length,
        closed_trades: assetClosed.length,
        profitable_trades: assetProfitable.length,
        win_rate: assetClosed.length > 0 ?
          Math.round((assetProfitable.length / assetClosed.length) * 100) : 0,
        total_profit: assetClosed.reduce((sum, s) => sum + (s.profit_loss || 0), 0)
      };
    });

    // 计算风险指标
    const profitLossRatio = totalLoss > 0 ? totalProfit / totalLoss : totalProfit > 0 ? 999 : 0;
    const avgProfit = closedTrades.length > 0 ? totalProfit / closedTrades.length : 0;
    const winRate = closedTrades.length > 0 ? Math.round((profitableTrades.length / closedTrades.length) * 100) : 0;

    // 最大回撤计算（简化版）
    let maxDrawdown = 0;
    let peak = 0;
    let runningProfit = 0;

    closedTrades.sort((a, b) => new Date(a.sell_time) - new Date(b.sell_time))
                .forEach(trade => {
      runningProfit += trade.profit_loss || 0;
      if (runningProfit > peak) {
        peak = runningProfit;
      }
      const drawdown = peak - runningProfit;
      if (drawdown > maxDrawdown) {
        maxDrawdown = drawdown;
      }
    });

    const performance = {
      total_signals: totalSignals,
      signals_with_trades: signalsWithTrades_count,
      closed_trades: closedTrades.length,
      win_rate: winRate,
      total_profit: Math.round(totalProfit * 100) / 100,
      total_loss: Math.round(totalLoss * 100) / 100,
      net_profit: Math.round((totalProfit - totalLoss) * 100) / 100,
      avg_profit: Math.round(avgProfit * 100) / 100,
      profit_loss_ratio: Math.round(profitLossRatio * 100) / 100,
      max_drawdown: Math.round(maxDrawdown * 100) / 100,
      signal_adoption_rate: totalSignals > 0 ?
        Math.round((signalsWithTrades_count / totalSignals) * 100) : 0,
      strength_performance: strengthPerformance,
      asset_performance: assetPerformance
    };

    res.json({
      success: true,
      data: performance
    });

  } catch (error) {
    console.error('获取策略表现失败:', error);
    res.status(500).json({
      success: false,
      message: '获取策略表现失败'
    });
  }
});

// 获取当前市场分析数据
router.get('/analysis/:asset', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const asset = req.params.asset;

    // 获取用户信息
    const user = await database.get(
      'SELECT capital, risk_percentage FROM users WHERE id = ?',
      [userId]
    );

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 获取用户信号配置
    const userConfig = await database.get(
      'SELECT * FROM signal_config WHERE user_id = ?',
      [userId]
    );

    const config = userConfig || {
      rsi_strong_threshold: 30,
      rsi_medium_threshold: 40,
      risk_strong: 0.01,           // 更新为新的风险参数
      risk_medium: 0.008,          // 更新为新的风险参数
      risk_weak: 0.005,            // 更新为新的风险参数
      atr_multiplier: 1.5,         // 新增ATR倍数
      risk_reward_ratio: 2.0,      // 新增风险回报比
      volume_multiplier: 1.5,
      enable_volume_filter: 1,
      enable_m_head_filter: 0,     // 关闭M头过滤，增加信号机会
      enable_w_bottom_bonus: 1,
      skip_weak_signals: 0,
      min_quality_score: 40        // 降低最低质量要求
    };

    // 使用统一的信号生成器
    let generatedSignal;
    try {
      generatedSignal = await signalGenerator.generateSignal(asset, config);
    } catch (error) {
      console.error(`❌ 市场分析信号生成失败: ${error.message}`);
      return res.status(500).json({
        success: false,
        message: `市场分析失败: ${error.message}`
      });
    }

    // 获取价格数据用于24小时变化
    const priceData = await signalService.getPriceData(asset);

    // 计算实际的美元仓位金额
    const actualPositionAmount = generatedSignal.suggested_position * user.capital;

    // 如果是有效信号（非skip），保存到数据库
    if (generatedSignal.signal_type !== 'hold' && !generatedSignal.should_skip) {
      console.log(`💾 保存市场分析信号到数据库 - 强度: ${generatedSignal.signal_strength}`);

      try {
        const signalResult = await database.run(
          `INSERT INTO signals (
            user_id, asset, price, rsi, ema_short, ema_long,
            trend_4h, pattern, signal_type, signal_strength,
            suggested_position, risk_amount, stop_loss_distance,
            volume_ratio, quality_score, m_head_detected, w_bottom_detected,
            entry_price, stop_loss, take_profit, is_active
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            userId, asset, generatedSignal.price, generatedSignal.rsi,
            generatedSignal.ema_short, generatedSignal.ema_long,
            generatedSignal.trend_4h, generatedSignal.pattern,
            generatedSignal.signal_type, generatedSignal.signal_strength,
            actualPositionAmount, // 使用实际美元金额
            user.capital * user.risk_percentage,
            generatedSignal.stop_loss_distance || 0,
            generatedSignal.volume_ratio || 1,
            generatedSignal.quality_score,
            generatedSignal.m_head_detected ? 1 : 0,
            generatedSignal.w_bottom_detected ? 1 : 0,
            generatedSignal.entry_price,
            generatedSignal.stop_loss,
            generatedSignal.take_profit,
            1
          ]
        );

        console.log(`✅ 市场分析信号已保存 (ID: ${signalResult.id})`);

        // 发送通知
        const signal = {
          id: signalResult.id,
          ...generatedSignal,
          suggested_position: actualPositionAmount,
          timestamp: new Date().toISOString()
        };
        await notificationService.sendSignalNotification(userId, signal);

      } catch (error) {
        console.error('保存市场分析信号失败:', error.message);
      }
    }

    // 返回完整的分析数据
    res.json({
      success: true,
      data: {
        ...generatedSignal, // 使用统一生成器的完整结果
        suggested_position: actualPositionAmount, // 转换为实际美元金额
        price_change_24h: priceData.usd_24h_change,
        ema_trend: generatedSignal.ema_short > generatedSignal.ema_long ? 'up' : 'down'
      }
    });

  } catch (error) {
    console.error('获取当前分析失败:', error);
    res.status(500).json({
      success: false,
      message: '获取当前分析失败'
    });
  }
});



module.exports = router;
