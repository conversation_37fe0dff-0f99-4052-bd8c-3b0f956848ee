/**
 * 技术指标计算模块
 * 与后端SignalService保持完全一致的计算逻辑
 */

class TechnicalIndicators {
  /**
   * 计算RSI (相对强弱指数) - 与SignalService.calculateRSI保持一致
   * @param {Array} prices - 价格数组
   * @param {number} period - 周期 (默认14)
   * @returns {number} RSI值
   */
  calculateRSI(prices, period = 14) {
    if (prices.length < period + 1) {
      return null;
    }

    let gains = 0;
    let losses = 0;

    // 计算初始平均收益和损失
    for (let i = 1; i <= period; i++) {
      const change = prices[i] - prices[i - 1];
      if (change > 0) {
        gains += change;
      } else {
        losses += Math.abs(change);
      }
    }

    let avgGain = gains / period;
    let avgLoss = losses / period;

    // 计算后续周期的平均收益和损失
    for (let i = period + 1; i < prices.length; i++) {
      const change = prices[i] - prices[i - 1];
      if (change > 0) {
        avgGain = (avgGain * (period - 1) + change) / period;
        avgLoss = (avgLoss * (period - 1)) / period;
      } else {
        avgGain = (avgGain * (period - 1)) / period;
        avgLoss = (avgLoss * (period - 1) + Math.abs(change)) / period;
      }
    }

    if (avgLoss === 0) return 100;
    
    const rs = avgGain / avgLoss;
    const rsi = 100 - (100 / (1 + rs));
    
    return Math.round(rsi * 100) / 100;
  }

  /**
   * 从价格数组计算EMA - 与SignalService.calculateEMAFromPrices保持一致
   * @param {Array} prices - 价格数组
   * @param {number} period - 周期
   * @returns {number} EMA值
   */
  calculateEMAFromPrices(prices, period) {
    if (prices.length < period) return prices[prices.length - 1] || 0;

    const multiplier = 2 / (period + 1);
    let ema = prices.slice(0, period).reduce((sum, price) => sum + price, 0) / period;

    for (let i = period; i < prices.length; i++) {
      ema = (prices[i] - ema) * multiplier + ema;
    }

    return ema;
  }

  /**
   * 计算MACD - 与SignalService.calculateMACD保持一致
   * @param {Array} historicalData - OHLCV历史数据
   * @param {number} fastPeriod - 快线周期 (默认12)
   * @param {number} slowPeriod - 慢线周期 (默认26)
   * @param {number} signalPeriod - 信号线周期 (默认9)
   * @returns {Object} MACD对象 {macd, signal, histogram}
   */
  calculateMACD(historicalData, fastPeriod = 12, slowPeriod = 26, signalPeriod = 9) {
    if (historicalData.length < slowPeriod + signalPeriod) {
      return { macd: 0, signal: 0, histogram: 0 };
    }

    const prices = historicalData.map(d => d[4]); // 收盘价

    // 计算快速和慢速EMA
    const fastEMA = this.calculateEMAFromPrices(prices, fastPeriod);
    const slowEMA = this.calculateEMAFromPrices(prices, slowPeriod);

    // 计算MACD线
    const macdLine = fastEMA - slowEMA;

    // 计算信号线（MACD的EMA）
    const macdHistory = [];
    for (let i = slowPeriod - 1; i < prices.length; i++) {
      const fastEMAValue = this.calculateEMAFromPrices(prices.slice(0, i + 1), fastPeriod);
      const slowEMAValue = this.calculateEMAFromPrices(prices.slice(0, i + 1), slowPeriod);
      macdHistory.push(fastEMAValue - slowEMAValue);
    }

    const signalLine = this.calculateEMAFromPrices(macdHistory, signalPeriod);

    // 计算柱状线
    const histogram = macdLine - signalLine;

    return {
      macd: macdLine,
      signal: signalLine,
      histogram: histogram
    };
  }

  /**
   * 计算ATR (Average True Range) - 与SignalService.calculateATR保持一致
   * @param {Array} historicalData - OHLCV历史数据
   * @param {number} period - 周期 (默认14)
   * @returns {number} ATR值
   */
  calculateATR(historicalData, period = 14) {
    if (historicalData.length < period + 1) return 0;

    const trueRanges = [];

    for (let i = 1; i < historicalData.length; i++) {
      const high = historicalData[i][2];
      const low = historicalData[i][3];
      const prevClose = historicalData[i - 1][4];

      const tr1 = high - low;
      const tr2 = Math.abs(high - prevClose);
      const tr3 = Math.abs(low - prevClose);

      trueRanges.push(Math.max(tr1, tr2, tr3));
    }

    // 计算ATR（使用简单移动平均）
    const atrValues = [];
    for (let i = period - 1; i < trueRanges.length; i++) {
      const sum = trueRanges.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0);
      atrValues.push(sum / period);
    }

    return atrValues[atrValues.length - 1] || 0;
  }

  /**
   * 计算成交量比率 - 与SignalService逻辑保持一致
   * @param {Array} volumes - 成交量数组
   * @param {number} period - 平均周期 (默认20)
   * @returns {number} 成交量比率
   */
  calculateVolumeRatio(volumes, period = 20) {
    if (volumes.length < period) return 1.0;

    const currentVolume = volumes[volumes.length - 1];
    const recentVolumes = volumes.slice(-period);
    const avgVolume = recentVolumes.reduce((sum, vol) => sum + vol, 0) / period;

    return avgVolume > 0 ? currentVolume / avgVolume : 1.0;
  }

  /**
   * 识别市场状态 - 与SignalService.identifyMarketState保持一致
   * @param {Array} historicalData - OHLCV历史数据
   * @param {number} period - 分析周期 (默认20)
   * @returns {string} 市场状态
   */
  identifyMarketState(historicalData, period = 20) {
    if (historicalData.length < period) return 'unknown';

    const prices = historicalData.slice(-period).map(d => d[4]); // 收盘价
    const atr = this.calculateATR(historicalData, 14);
    const currentPrice = prices[prices.length - 1];

    // 计算价格通道
    const highest = Math.max(...prices);
    const lowest = Math.min(...prices);
    const priceRange = highest - lowest;
    const rangePercent = priceRange / currentPrice;

    // 计算趋势强度
    const ema20 = this.calculateEMAFromPrices(prices, 20);
    const pricePosition = (currentPrice - lowest) / priceRange;

    // 判断市场状态
    if (rangePercent < 0.05) {
      return 'consolidation'; // 盘整市
    } else if (pricePosition > 0.7 && currentPrice > ema20) {
      return 'trending_up'; // 上升趋势
    } else if (pricePosition < 0.3 && currentPrice < ema20) {
      return 'trending_down'; // 下降趋势
    } else {
      return 'ranging'; // 震荡市
    }
  }

  /**
   * 多时间框架分析 - 日线趋势
   * @param {Array} dailyData - 日线OHLCV数据
   * @returns {Object} 日线趋势分析
   */
  analyzeDailyTrend(dailyData) {
    if (dailyData.length < 50) {
      return { trend: 'unknown', strength: 0, description: '数据不足' };
    }

    const prices = dailyData.map(d => d[4]); // 收盘价
    const currentPrice = prices[prices.length - 1];

    // 计算日线EMA
    const ema20 = this.calculateEMAFromPrices(prices, 20);
    const ema50 = this.calculateEMAFromPrices(prices, 50);

    // 趋势判断
    let trend = 'neutral';
    let strength = 0;
    let description = '';

    if (ema20 > ema50 && currentPrice > ema20) {
      trend = 'bullish';
      strength = Math.min(((currentPrice - ema20) / ema20) * 100, 10);
      description = '日线多头趋势';
    } else if (ema20 < ema50 && currentPrice < ema20) {
      trend = 'bearish';
      strength = Math.min(((ema20 - currentPrice) / ema20) * 100, 10);
      description = '日线空头趋势';
    } else {
      trend = 'neutral';
      strength = 0;
      description = '日线震荡';
    }

    return { trend, strength, description };
  }

  /**
   * 多时间框架分析 - 4小时趋势
   * @param {Array} h4Data - 4小时OHLCV数据
   * @returns {Object} 4小时趋势分析
   */
  analyze4HTrend(h4Data) {
    if (h4Data.length < 26) {
      return { trend: 'unknown', strength: 0, description: '数据不足' };
    }

    const prices = h4Data.map(d => d[4]);
    const currentPrice = prices[prices.length - 1];

    // 计算4小时EMA
    const ema12 = this.calculateEMAFromPrices(prices, 12);
    const ema26 = this.calculateEMAFromPrices(prices, 26);

    let trend = 'neutral';
    let strength = 0;
    let description = '';

    if (ema12 > ema26 && currentPrice > ema12) {
      trend = 'bullish';
      strength = Math.min(((currentPrice - ema12) / ema12) * 100, 8);
      description = '4小时多头趋势';
    } else if (ema12 < ema26 && currentPrice < ema12) {
      trend = 'bearish';
      strength = Math.min(((ema12 - currentPrice) / ema12) * 100, 8);
      description = '4小时空头趋势';
    } else {
      trend = 'neutral';
      strength = 0;
      description = '4小时震荡';
    }

    return { trend, strength, description };
  }

  /**
   * 多时间框架分析 - 1小时状态
   * @param {Array} hourlyData - 1小时OHLCV数据
   * @returns {Object} 1小时状态分析
   */
  analyze1HState(hourlyData) {
    if (hourlyData.length < 21) {
      return { trend: 'unknown', strength: 0, description: '数据不足' };
    }

    const prices = hourlyData.map(d => d[4]);
    const currentPrice = prices[prices.length - 1];

    // 计算1小时EMA
    const ema9 = this.calculateEMAFromPrices(prices, 9);
    const ema21 = this.calculateEMAFromPrices(prices, 21);

    let trend = 'neutral';
    let strength = 0;
    let description = '';

    if (ema9 > ema21 && currentPrice > ema9) {
      trend = 'bullish';
      strength = Math.min(((currentPrice - ema9) / ema9) * 100, 5);
      description = '1小时多头状态';
    } else if (ema9 < ema21 && currentPrice < ema9) {
      trend = 'bearish';
      strength = Math.min(((ema9 - currentPrice) / ema9) * 100, 5);
      description = '1小时空头状态';
    } else {
      trend = 'neutral';
      strength = 0;
      description = '1小时震荡';
    }

    return { trend, strength, description };
  }
}

module.exports = TechnicalIndicators;
