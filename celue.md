📊 ScalpAlert交易信号策略总结
🎯 核心策略框架
1. 多时间周期分析 (MTA)
日线趋势：EMA20/50判断长期方向
4小时趋势：EMA12/26确定中期趋势
1小时状态：EMA9/21识别短期状态
趋势一致性过滤：只在多周期趋势一致时发出信号
2. 技术指标组合
RSI(14)：超卖/超买判断，强信号<25，中等信号<30
EMA双线：9/21周期，金叉死叉确认趋势
MACD(12,26,9)：动量确认，柱状线方向验证
ATR(14)：波动率测量，动态止损计算
3. 形态识别
M头形态：双顶反转信号识别
W底形态：双底反转信号识别
成交量确认：放量突破验证信号有效性
💰 风险管理策略
1. 基于风险的仓位计算
仓位大小 = 风险金额 / 止损距离
风险金额 = 账户资金 × 风险百分比
止损距离 = ATR × ATR倍数

2. 动态止损止盈
止损：当前价格 ± (ATR × 1.5倍数)
止盈：止损距离 × 风险回报比(1:2-2.5)
ATR自适应：根据市场波动调整止损距离
3. 信号强度分级
强信号：风险0.5%-1.5%，15分钟冷却
中等信号：风险0.3%-1.2%，20分钟冷却
弱信号：风险0.1%-0.8%，30分钟冷却
🔍 信号生成逻辑
1. 买入信号条件
RSI < 阈值 (强信号<25, 中等<30)
EMA短线 > EMA长线 (上升趋势)
4小时趋势向上
多时间周期趋势一致
成交量放大确认
2. 卖出信号条件
RSI > 70 (超买区域)
EMA短线 < EMA长线 (下降趋势)
4小时趋势向下
多时间周期趋势一致
成交量放大确认
3. 信号质量评分
基础分数：RSI位置(30分) + EMA趋势(20分)
MACD确认：柱状线方向一致(+15分)
多时间周期：趋势一致性(+20分)
成交量：放量突破(+10分)
形态：W底加分(+5分)，M头减分(-5分)
⚙️ 配置预设策略
1. 保守型 (Conservative)
风险：强0.5%/中0.3%/弱0.1%
ATR倍数：1.5
风险回报比：1:2
适合：稳健投资者
2. 平衡型 (Balanced)
风险：强1%/中0.8%/弱0.5%
ATR倍数：1.5
风险回报比：1:2
适合：一般投资者
3. 激进型 (Aggressive)
风险：强1.5%/中1.2%/弱0.8%
ATR倍数：1.5
风险回报比：1:2.5
适合：风险偏好投资者
🚀 智能优化机制
1. 动态检测频率
基础频率：10分钟
高频模式：5分钟(市场活跃时)
低频模式：15分钟(市场平静时)
K线进度调节：根据K线完成度调整
2. 信号冷却机制
防止同类型信号重复发出
根据信号强度设置不同冷却时间
价格变化阈值触发重新检测
3. 市场状态适应
评估市场活跃度
根据波动率调整参数
趋势强度影响信号敏感度
📈 支持的交易对
BTC/USDT (bitcoin)
ETH/USDT (ethereum)
BNB/USDT (binancecoin)
SOL/USDT (solana)
这套策略结合了趋势跟踪、均值回归、动量确认和风险管理，形成了完整的量化交易信号系统！