const express = require('express');
const bcrypt = require('bcryptjs');
const { generateToken, authenticateToken } = require('../middleware/auth');
const database = require('../config/database');

const router = express.Router();

// 用户注册
router.post('/register', async (req, res) => {
  try {
    const { username, password, capital, lossLimit } = req.body;

    // 验证输入
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名和密码不能为空'
      });
    }

    if (password.length < 6) {
      return res.status(400).json({
        success: false,
        message: '密码长度至少6位'
      });
    }

    // 检查用户名是否已存在
    const existingUser = await database.get(
      'SELECT id FROM users WHERE username = ?',
      [username]
    );

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: '用户名已存在'
      });
    }

    // 加密密码
    const saltRounds = 10;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // 创建用户
    const result = await database.run(
      `INSERT INTO users (username, password_hash, capital, loss_limit) 
       VALUES (?, ?, ?, ?)`,
      [
        username, 
        passwordHash, 
        capital || 5000, 
        lossLimit || 3000
      ]
    );

    // 生成JWT令牌
    const user = {
      id: result.id,
      username: username
    };
    const token = generateToken(user);

    res.status(201).json({
      success: true,
      message: '注册成功',
      data: {
        user: {
          id: user.id,
          username: user.username,
          capital: capital || 5000,
          lossLimit: lossLimit || 3000
        },
        token: token
      }
    });

  } catch (error) {
    console.error('注册失败:', error.message);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 用户登录
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    // 验证输入
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名和密码不能为空'
      });
    }

    // 查找用户
    const user = await database.get(
      'SELECT * FROM users WHERE username = ?',
      [username]
    );

    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    // 验证密码
    const isValidPassword = await bcrypt.compare(password, user.password_hash);
    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    // 生成JWT令牌
    const token = generateToken({
      id: user.id,
      username: user.username
    });

    // 更新最后登录时间
    await database.run(
      'UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [user.id]
    );

    res.json({
      success: true,
      message: '登录成功',
      data: {
        user: {
          id: user.id,
          username: user.username,
          capital: user.capital,
          lossLimit: user.loss_limit,
          riskPercentage: user.risk_percentage,
          maxDailyTrades: user.max_daily_trades
        },
        token: token
      }
    });

  } catch (error) {
    console.error('登录失败:', error.message);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 获取用户信息
router.get('/profile', authenticateToken, async (req, res) => {
  try {
    const user = await database.get(
      'SELECT id, username, capital, loss_limit, risk_percentage, max_daily_trades, created_at FROM users WHERE id = ?',
      [req.user.id]
    );

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.json({
      success: true,
      data: {
        id: user.id,
        username: user.username,
        capital: user.capital,
        lossLimit: user.loss_limit,
        riskPercentage: user.risk_percentage,
        maxDailyTrades: user.max_daily_trades,
        createdAt: user.created_at
      }
    });

  } catch (error) {
    console.error('获取用户信息失败:', error.message);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 更新用户设置
router.put('/settings', authenticateToken, async (req, res) => {
  try {
    const { capital, lossLimit, riskPercentage, maxDailyTrades } = req.body;
    const userId = req.user.id;

    // 验证输入
    if (capital && capital < 0) {
      return res.status(400).json({
        success: false,
        message: '本金不能为负数'
      });
    }

    if (lossLimit && lossLimit < 0) {
      return res.status(400).json({
        success: false,
        message: '亏损限制不能为负数'
      });
    }

    if (riskPercentage && (riskPercentage < 0 || riskPercentage > 0.1)) {
      return res.status(400).json({
        success: false,
        message: '风险百分比应在0-10%之间'
      });
    }

    // 构建更新SQL
    const updates = [];
    const params = [];

    if (capital !== undefined) {
      updates.push('capital = ?');
      params.push(capital);
    }
    if (lossLimit !== undefined) {
      updates.push('loss_limit = ?');
      params.push(lossLimit);
    }
    if (riskPercentage !== undefined) {
      updates.push('risk_percentage = ?');
      params.push(riskPercentage);
    }
    if (maxDailyTrades !== undefined) {
      updates.push('max_daily_trades = ?');
      params.push(maxDailyTrades);
    }

    if (updates.length === 0) {
      return res.status(400).json({
        success: false,
        message: '没有要更新的设置'
      });
    }

    updates.push('updated_at = CURRENT_TIMESTAMP');
    params.push(userId);

    const sql = `UPDATE users SET ${updates.join(', ')} WHERE id = ?`;
    await database.run(sql, params);

    res.json({
      success: true,
      message: '设置更新成功'
    });

  } catch (error) {
    console.error('更新用户设置失败:', error.message);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 验证令牌
router.post('/verify', authenticateToken, (req, res) => {
  res.json({
    success: true,
    message: '令牌有效',
    data: {
      user: req.user
    }
  });
});

module.exports = router;
