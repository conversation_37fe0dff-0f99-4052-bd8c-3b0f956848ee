/**
 * 方案二：专注震荡，高抛低吸
 * 核心理念：积少成多，高胜率低盈亏比
 * 交易周期：M15或M30
 * 目标：高胜率，稳定获利
 */

const TechnicalIndicators = require('../utils/technicalIndicators');

class RangeTradingStrategy {
  constructor(config = {}) {
    this.config = {
      name: '震荡交易策略',
      // 布林带参数
      bb_period: 20,          // 布林带周期
      bb_std_dev: 2.0,        // 标准差倍数
      
      // RSI参数
      rsi_period: 14,         // RSI周期
      rsi_oversold: 30,       // 超卖阈值
      rsi_overbought: 70,     // 超买阈值
      
      // 震荡识别参数
      range_stability_period: 50,    // 震荡稳定性检查周期
      max_band_width_change: 0.1,    // 最大带宽变化10%
      min_touches: 3,                // 最少触碰次数
      
      // 风险管理
      risk_per_trade: 0.01,          // 每笔交易风险1%
      profit_target_ratio: 1.2,     // 盈亏比1:1.2
      max_stop_distance: 0.02,       // 最大止损距离2%
      
      // 入场确认
      require_volume_confirmation: true,
      min_volume_ratio: 1.2,         // 最小成交量比例
      
      ...config
    };
    
    this.technicalIndicators = new TechnicalIndicators();
  }

  /**
   * 生成交易信号
   * @param {Array} historicalData - OHLCV历史数据
   * @param {Object} currentCandle - 当前K线
   * @returns {Object} 交易信号
   */
  generateSignal(historicalData, currentCandle) {
    // --- [添加调试代码] ---
    const currentIndex = historicalData.length - 1;
    if (currentIndex % 500 === 0) { // 每500根K线打印一次基本信息
      console.log(`[震荡策略] K线 #${currentIndex} | 历史数据长度: ${historicalData.length} | 需要最少: ${this.config.range_stability_period + 20} | 当前价格: ${currentCandle.close}`);
    }
    // --- [调试代码结束] ---

    if (historicalData.length < this.config.range_stability_period + 20) {
      return this.createSignal('hold', 'skip', '数据不足');
    }

    try {
      // 计算技术指标
      const prices = historicalData.map(candle => candle[4]); // 收盘价
      const highs = historicalData.map(candle => candle[2]);
      const lows = historicalData.map(candle => candle[3]);
      const volumes = historicalData.map(candle => candle[5]);
      
      const bollingerBands = this.calculateBollingerBands(prices);
      const rsi = this.technicalIndicators.calculateRSI(prices, this.config.rsi_period);
      
      const currentPrice = currentCandle.close;
      const currentVolume = currentCandle.volume;
      const currentRSI = rsi[rsi.length - 1];
      const currentBB = bollingerBands[bollingerBands.length - 1];
      
      // 1. 震荡区间识别
      const rangeAnalysis = this.analyzeRange(bollingerBands, prices, highs, lows);

      // --- [添加调试代码] ---
      const currentIndex = historicalData.length - 1;
      if (currentIndex % 100 === 0) {
        console.log(`[K线 #${currentIndex}] 震荡诊断: ${rangeAnalysis.isRanging} | 原因: ${rangeAnalysis.reason}`);
      }
      // --- [调试代码结束] ---

      if (!rangeAnalysis.isRanging) {
        return this.createSignal('hold', 'skip', rangeAnalysis.reason);
      }
      
      // 2. 成交量确认
      if (this.config.require_volume_confirmation) {
        const volumeConfirmation = this.checkVolumeConfirmation(volumes, currentVolume);
        if (!volumeConfirmation.valid) {
          return this.createSignal('hold', 'skip', volumeConfirmation.reason);
        }
      }
      
      // 3. 入场信号检查
      const entrySignal = this.checkRangeEntrySignal(
        currentPrice, 
        currentRSI, 
        currentBB,
        historicalData.slice(-5)
      );
      
      if (!entrySignal.valid) {
        return this.createSignal('hold', 'skip', entrySignal.reason);
      }
      
      // 4. 计算止损止盈
      const stopLoss = this.calculateRangeStopLoss(
        currentPrice, 
        currentBB, 
        entrySignal.direction
      );
      const takeProfit = this.calculateRangeTakeProfit(
        currentPrice, 
        currentBB, 
        entrySignal.direction
      );
      
      const riskAmount = this.calculatePositionSize(currentPrice, stopLoss);
      
      // 5. 生成信号
      return this.createSignal(
        entrySignal.direction === 'long' ? 'buy' : 'sell',
        entrySignal.strength,
        entrySignal.reason,
        {
          entry_price: currentPrice,
          stop_loss: stopLoss,
          take_profit: takeProfit,
          risk_amount: riskAmount,
          rsi: currentRSI,
          bb_position: entrySignal.bb_position,
          range_width: rangeAnalysis.width
        }
      );
      
    } catch (error) {
      return this.createSignal('hold', 'skip', `计算错误: ${error.message}`);
    }
  }

  /**
   * 计算布林带
   */
  calculateBollingerBands(prices) {
    const period = this.config.bb_period;
    const stdDev = this.config.bb_std_dev;
    const bands = [];
    
    for (let i = period - 1; i < prices.length; i++) {
      const slice = prices.slice(i - period + 1, i + 1);
      const sma = slice.reduce((sum, price) => sum + price, 0) / period;
      
      const variance = slice.reduce((sum, price) => sum + Math.pow(price - sma, 2), 0) / period;
      const std = Math.sqrt(variance);
      
      bands.push({
        upper: sma + (std * stdDev),
        middle: sma,
        lower: sma - (std * stdDev),
        width: (std * stdDev * 2) / sma // 标准化带宽
      });
    }
    
    return bands;
  }

  /**
   * 核心能力1: 震荡状态识别模块 (The Range Detector)
   * 区分"可交易的震荡"和"趋势前的盘整"
   */
  isTradeableRange(bollingerBands, prices) {
    const recentBands = bollingerBands.slice(-this.config.range_stability_period);
    const recentPrices = prices.slice(-this.config.range_stability_period);

    if (recentBands.length < this.config.range_stability_period) {
      return { isRange: false, reason: '数据不足以判断震荡' };
    }

    // 1. 带宽检查 - 低且稳定的波动性
    const bandWidths = recentBands.map(bb => bb.width);
    const avgBandWidth = bandWidths.reduce((sum, width) => sum + width, 0) / bandWidths.length;
    const maxBandWidth = Math.max(...bandWidths);
    const minBandWidth = Math.min(...bandWidths);
    const bandWidthVariation = (maxBandWidth - minBandWidth) / avgBandWidth;

    if (avgBandWidth > 0.05 || bandWidthVariation > this.config.max_band_width_change) {
      return {
        isRange: false,
        reason: `带宽过大或变化过大: 平均${(avgBandWidth * 100).toFixed(1)}%, 变化${(bandWidthVariation * 100).toFixed(1)}%`
      };
    }

    // 2. 通道斜率检查 - 中轨应该接近水平
    const middleBandSlope = this.calculateSlope(recentBands.map(bb => bb.middle), 20);

    if (Math.abs(middleBandSlope) > 0.0001) {
      return {
        isRange: false,
        reason: `中轨斜率过大: ${(middleBandSlope * 10000).toFixed(2)}`
      };
    }

    /* // 3. 边界触碰检查
    const touchAnalysis = this.analyzeBoundaryTouches(recentBands, recentPrices);

    if (!touchAnalysis.sufficient) {
      return {
        isRange: false,
        reason: touchAnalysis.reason
      };
    }
 */
    const currentBB = recentBands[recentBands.length - 1];
    return {
      isRange: true,
      upper: currentBB.upper,
      lower: currentBB.lower,
      middle: currentBB.middle,
      width: avgBandWidth,
      stability: 1 - bandWidthVariation,
      touches: touchAnalysis
    };
  }

  /**
   * 分析边界触碰情况
   */
  analyzeBoundaryTouches(recentBands, recentPrices) {
    let upperTouches = 0;
    let lowerTouches = 0;

    for (let i = 0; i < recentPrices.length; i++) {
      const bb = recentBands[i];
      const price = recentPrices[i];

      // 触碰上轨（0.5%容差）
      if (price >= bb.upper * 0.995) {
        upperTouches++;
      }

      // 触碰下轨（0.5%容差）
      if (price <= bb.lower * 1.005) {
        lowerTouches++;
      }
    }

    if (upperTouches < this.config.min_touches || lowerTouches < this.config.min_touches) {
      return {
        sufficient: false,
        reason: `触碰次数不足: 上轨${upperTouches}次, 下轨${lowerTouches}次`,
        upperTouches,
        lowerTouches
      };
    }

    return {
      sufficient: true,
      upperTouches,
      lowerTouches
    };
  }

  /**
   * 计算斜率
   */
  calculateSlope(values, period) {
    if (values.length < period) return 0;

    const recentValues = values.slice(-period);
    const firstValue = recentValues[0];
    const lastValue = recentValues[recentValues.length - 1];

    return (lastValue - firstValue) / firstValue / period;
  }

  /**
   * 分析震荡区间（保持兼容性）
   */
  analyzeRange(bollingerBands, prices, highs, lows) {
    const rangeInfo = this.isTradeableRange(bollingerBands, prices);

    if (!rangeInfo.isRange) {
      return { isRanging: false, reason: rangeInfo.reason };
    }

    return {
      isRanging: true,
      width: rangeInfo.width,
      stability: rangeInfo.stability,
      upperTouches: rangeInfo.touches.upperTouches,
      lowerTouches: rangeInfo.touches.lowerTouches
    };
  }

  /**
   * 检查成交量确认
   */
  checkVolumeConfirmation(volumes, currentVolume) {
    const recentVolumes = volumes.slice(-20);
    const avgVolume = recentVolumes.reduce((sum, vol) => sum + vol, 0) / recentVolumes.length;
    
    const volumeRatio = currentVolume / avgVolume;
    
    if (volumeRatio < this.config.min_volume_ratio) {
      return { 
        valid: false, 
        reason: `成交量不足: ${volumeRatio.toFixed(2)}倍平均量` 
      };
    }
    
    return { valid: true, volumeRatio };
  }

  /**
   * 核心能力2: 边界入场模块 (The Edge Entry)
   * 在确认的震荡区间边界，结合超买超卖指标寻找入场点
   */
  findRangeEntrySignal(currentPrice, currentRSI, rangeInfo, recentCandles) {
    const upperBand = rangeInfo.upper;
    const lowerBand = rangeInfo.lower;

    // 计算价格在布林带中的位置
    const bbPosition = (currentPrice - lowerBand) / (upperBand - lowerBand);

    // 1. 边界触碰检查
    const boundaryTouch = this.checkBoundaryTouch(currentPrice, rangeInfo, bbPosition);
    if (!boundaryTouch.touched) {
      return { valid: false, reason: boundaryTouch.reason };
    }

    // 2. RSI超区确认
    const rsiConfirmation = this.checkRSIConfirmation(currentRSI, boundaryTouch.direction);
    if (!rsiConfirmation.valid) {
      return { valid: false, reason: rsiConfirmation.reason };
    }

    // 3. 价格行为确认（进阶过滤）
    const priceActionConfirmation = this.checkPriceActionConfirmation(
      recentCandles,
      rangeInfo,
      boundaryTouch.direction
    );

    // --- [添加调试代码] ---
    console.log(`[震荡入场检查] 边界触碰: ${boundaryTouch.touched}, RSI确认: ${rsiConfirmation.valid}, 价格行为确认: ${priceActionConfirmation.valid}`);
    // --- [调试代码结束] ---

    if (!priceActionConfirmation.valid) {
      return { valid: false, reason: priceActionConfirmation.reason };
    }

    return {
      valid: true,
      direction: boundaryTouch.direction,
      strength: 'strong',
      reason: `${boundaryTouch.direction === 'long' ? '下轨' : '上轨'}${priceActionConfirmation.type}+RSI${rsiConfirmation.type}`,
      bb_position: bbPosition,
      rsi_value: currentRSI
    };
  }

  /**
   * 检查边界触碰
   */
  checkBoundaryTouch(currentPrice, rangeInfo, bbPosition) {
    // 做多信号：价格触及或跌破下轨
    if (currentPrice <= rangeInfo.lower * 1.005 || bbPosition <= 0.1) {
      return {
        touched: true,
        direction: 'long',
        reason: '价格触及下轨边界'
      };
    }

    // 做空信号：价格触及或突破上轨
    if (currentPrice >= rangeInfo.upper * 0.995 || bbPosition >= 0.9) {
      return {
        touched: true,
        direction: 'short',
        reason: '价格触及上轨边界'
      };
    }

    return {
      touched: false,
      reason: `价格位于区间中部(${(bbPosition * 100).toFixed(1)}%)`
    };
  }

  /**
   * 检查RSI确认
   */
  checkRSIConfirmation(currentRSI, direction) {
    if (direction === 'long') {
      // 做多需要RSI超卖
      if (currentRSI <= this.config.rsi_oversold) {
        return {
          valid: true,
          type: `超卖(${currentRSI.toFixed(1)})`
        };
      } else {
        return {
          valid: false,
          reason: `RSI未超卖(${currentRSI.toFixed(1)} > ${this.config.rsi_oversold})`
        };
      }
    } else {
      // 做空需要RSI超买
      if (currentRSI >= this.config.rsi_overbought) {
        return {
          valid: true,
          type: `超买(${currentRSI.toFixed(1)})`
        };
      } else {
        return {
          valid: false,
          reason: `RSI未超买(${currentRSI.toFixed(1)} < ${this.config.rsi_overbought})`
        };
      }
    }
  }

  /**
   * 检查价格行为确认（进阶过滤）
   */
  checkPriceActionConfirmation(recentCandles, rangeInfo, direction) {
    if (recentCandles.length < 2) {
      return { valid: false, reason: 'K线数据不足' };
    }

    const currentCandle = recentCandles[recentCandles.length - 1];
    const prevCandle = recentCandles[recentCandles.length - 2];

    const currentPrice = currentCandle[4]; // 收盘价
    const prevPrice = prevCandle[4];

    if (direction === 'long') {
      // 检查是否从轨道外重新回到轨道内（过滤假突破）
      const wasOutside = prevPrice < rangeInfo.lower;
      const nowInside = currentPrice >= rangeInfo.lower;

      if (wasOutside && nowInside) {
        return {
          valid: true,
          type: '重回下轨',
          reason: '价格从下轨外重新回到轨道内'
        };
      }

      // 或者检查反弹迹象
      const isBouncing = currentPrice > prevPrice;
      if (isBouncing) {
        return {
          valid: true,
          type: '反弹确认',
          reason: '价格出现反弹迹象'
        };
      }

    } else {
      // 检查是否从轨道外重新回到轨道内
      const wasOutside = prevPrice > rangeInfo.upper;
      const nowInside = currentPrice <= rangeInfo.upper;

      if (wasOutside && nowInside) {
        return {
          valid: true,
          type: '重回上轨',
          reason: '价格从上轨外重新回到轨道内'
        };
      }

      // 或者检查回落迹象
      const isFalling = currentPrice < prevPrice;
      if (isFalling) {
        return {
          valid: true,
          type: '回落确认',
          reason: '价格出现回落迹象'
        };
      }
    }

    return {
      valid: false,
      reason: `未找到有效的${direction === 'long' ? '反弹' : '回落'}确认`
    };
  }

  /**
   * 检查震荡入场信号（保持兼容性）
   */
  checkRangeEntrySignal(currentPrice, currentRSI, currentBB, recentCandles) {
    const rangeInfo = {
      upper: currentBB.upper,
      lower: currentBB.lower,
      middle: currentBB.middle
    };

    return this.findRangeEntrySignal(currentPrice, currentRSI, rangeInfo, recentCandles);
  }

  /**
   * 核心能力3: 精确风控模块 (The Pinpoint Risk Control)
   * 设置极其严格的止损，一旦突破立即最小化损失
   */
  calculateRangeStopLoss(entryPrice, rangeInfo, direction, atr = null) {
    // 使用ATR动态计算止损距离
    const atrBuffer = atr ? atr * 0.5 : entryPrice * 0.005; // 0.5倍ATR或0.5%

    if (direction === 'long') {
      // 买入止损：下轨外侧 + ATR缓冲
      const stopLoss = rangeInfo.lower - atrBuffer;
      const maxStop = entryPrice * (1 - this.config.max_stop_distance);

      // 确保止损不会过远
      return Math.max(stopLoss, maxStop);
    } else {
      // 卖出止损：上轨外侧 + ATR缓冲
      const stopLoss = rangeInfo.upper + atrBuffer;
      const maxStop = entryPrice * (1 + this.config.max_stop_distance);

      // 确保止损不会过远
      return Math.min(stopLoss, maxStop);
    }
  }

  /**
   * 核心能力4: 快速止盈模块 (The Profit Taker)
   * 明确的止盈目标，绝不贪心
   */
  getTakeProfitTarget(direction, rangeInfo, entryPrice) {
    if (direction === 'long') {
      // 主要目标：布林带中轨（最稳健）
      const primaryTarget = rangeInfo.middle;

      // 次要目标：上轨（可选）
      const secondaryTarget = rangeInfo.upper * 0.995; // 0.5%缓冲

      // 选择更保守的目标，确保高胜率
      return Math.min(primaryTarget, secondaryTarget);

    } else {
      // 主要目标：布林带中轨
      const primaryTarget = rangeInfo.middle;

      // 次要目标：下轨
      const secondaryTarget = rangeInfo.lower * 1.005; // 0.5%缓冲

      // 选择更保守的目标
      return Math.max(primaryTarget, secondaryTarget);
    }
  }

  /**
   * 分级止盈策略（可选）
   */
  getMultiLevelTakeProfits(direction, rangeInfo, entryPrice) {
    const targets = [];

    if (direction === 'long') {
      // 第一目标：50%仓位在中轨平仓
      targets.push({
        price: rangeInfo.middle,
        percentage: 0.5,
        reason: '中轨止盈（主要目标）'
      });

      // 第二目标：剩余50%在上轨平仓
      targets.push({
        price: rangeInfo.upper * 0.995,
        percentage: 0.5,
        reason: '上轨止盈（次要目标）'
      });

    } else {
      // 第一目标：50%仓位在中轨平仓
      targets.push({
        price: rangeInfo.middle,
        percentage: 0.5,
        reason: '中轨止盈（主要目标）'
      });

      // 第二目标：剩余50%在下轨平仓
      targets.push({
        price: rangeInfo.lower * 1.005,
        percentage: 0.5,
        reason: '下轨止盈（次要目标）'
      });
    }

    return targets;
  }

  /**
   * 计算ATR（用于动态止损）
   */
  calculateATRFromRecentData(recentCandles, period = 14) {
    if (recentCandles.length < period + 1) return null;

    let trSum = 0;
    for (let i = 1; i < Math.min(period + 1, recentCandles.length); i++) {
      const [, , high, low, close] = recentCandles[recentCandles.length - i];
      const [, , , , prevClose] = recentCandles[recentCandles.length - i - 1];

      const tr = Math.max(
        high - low,
        Math.abs(high - prevClose),
        Math.abs(low - prevClose)
      );
      trSum += tr;
    }

    return trSum / Math.min(period, recentCandles.length - 1);
  }

  /**
   * 计算震荡止损（保持兼容性）
   */
  calculateRangeStopLoss_Legacy(entryPrice, currentBB, direction) {
    const rangeInfo = {
      upper: currentBB.upper,
      lower: currentBB.lower,
      middle: currentBB.middle
    };

    return this.calculateRangeStopLoss(entryPrice, rangeInfo, direction);
  }

  /**
   * 计算震荡止盈（保持兼容性）
   */
  calculateRangeTakeProfit(entryPrice, currentBB, direction) {
    const rangeInfo = {
      upper: currentBB.upper,
      lower: currentBB.lower,
      middle: currentBB.middle
    };

    return this.getTakeProfitTarget(direction, rangeInfo, entryPrice);
  }

  /**
   * 计算仓位大小
   */
  calculatePositionSize(entryPrice, stopLoss) {
    const riskPerTrade = this.config.risk_per_trade;
    const stopDistance = Math.abs(entryPrice - stopLoss);
    
    if (stopDistance === 0) return 0;
    
    return riskPerTrade / (stopDistance / entryPrice);
  }

  /**
   * 创建信号对象
   */
  createSignal(signal_type, signal_strength, reason, details = {}) {
    return {
      signal_type,
      signal_strength,
      reason,
      strategy: this.config.name,
      timestamp: Date.now(),
      ...details
    };
  }
}

module.exports = RangeTradingStrategy;
