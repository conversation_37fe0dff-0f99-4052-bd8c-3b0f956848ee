const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const path = require('path');
require('dotenv').config();

// 导入路由
const authRoutes = require('./routes/auth');
const signalRoutes = require('./routes/signals');
const tradeRoutes = require('./routes/trades');
const exportRoutes = require('./routes/export');
const autoSignalService = require('./services/autoSignalService');

// 导入服务
const database = require('./config/database');
const websocketService = require('./services/websocketService');

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "https://api.coingecko.com", "https://home.917999.xyz:18030"]
    }
  }
}));

app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://your-domain.com'] 
    : ['http://localhost:3000', 'http://127.0.0.1:3000'],
  credentials: true
}));

app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务（生产环境）
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, '../client/build')));
}

// API路由
app.use('/api/auth', authRoutes);
app.use('/api/signals', signalRoutes);
app.use('/api/trades', tradeRoutes);
app.use('/api/export', exportRoutes);
app.use('/api/signal-config', require('./routes/signalConfig'));

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'ScalpAlert API 服务正常运行',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// 自动信号检测API
app.get('/api/auto-signal/status', (req, res) => {
  const status = autoSignalService.getStatus();
  res.json({
    success: true,
    data: status
  });
});

app.post('/api/auto-signal/start', (req, res) => {
  autoSignalService.start();
  res.json({
    success: true,
    message: '自动信号检测已启动'
  });
});

app.post('/api/auto-signal/stop', (req, res) => {
  autoSignalService.stop();
  res.json({
    success: true,
    message: '自动信号检测已停止'
  });
});

// 获取通知历史
app.get('/api/notifications', require('./middleware/auth').authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { page = 1, limit = 20 } = req.query;
    const offset = (parseInt(page) - 1) * parseInt(limit);

    // 添加超时处理
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('数据库查询超时')), 5000);
    });

    const notificationsPromise = database.query(
      `SELECT * FROM notifications
       WHERE user_id = ?
       ORDER BY sent_at DESC
       LIMIT ? OFFSET ?`,
      [userId, parseInt(limit), offset]
    );

    const totalPromise = database.get(
      'SELECT COUNT(*) as total FROM notifications WHERE user_id = ?',
      [userId]
    );

    const [notifications, totalResult] = await Promise.race([
      Promise.all([notificationsPromise, totalPromise]),
      timeoutPromise
    ]);

    res.json({
      success: true,
      data: {
        notifications: notifications || [],
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalResult ? totalResult.total : 0,
          pages: Math.ceil((totalResult ? totalResult.total : 0) / parseInt(limit))
        }
      }
    });

  } catch (error) {
    console.error('获取通知失败:', error.message);

    // 返回空数据而不是错误，避免阻塞前端
    res.json({
      success: true,
      data: {
        notifications: [],
        pagination: {
          page: parseInt(req.query.page || 1),
          limit: parseInt(req.query.limit || 20),
          total: 0,
          pages: 0
        }
      }
    });
  }
});

// 标记通知为已读
app.put('/api/notifications/:id/read', require('./middleware/auth').authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const notificationId = req.params.id;

    const result = await database.run(
      'UPDATE notifications SET is_read = 1 WHERE id = ? AND user_id = ?',
      [notificationId, userId]
    );

    if (result.changes === 0) {
      return res.status(404).json({
        success: false,
        message: '通知不存在'
      });
    }

    res.json({
      success: true,
      message: '通知已标记为已读'
    });

  } catch (error) {
    console.error('标记通知已读失败:', error.message);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 获取未读通知数量
app.get('/api/notifications/unread-count', require('./middleware/auth').authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    console.log('获取未读通知数量，用户ID:', userId);

    // 检查数据库连接
    if (!database.db) {
      console.error('数据库连接不存在');
      return res.json({
        success: true,
        data: { unreadCount: 0 }
      });
    }

    // 先检查表是否存在
    try {
      await database.get("SELECT name FROM sqlite_master WHERE type='table' AND name='notifications'");
    } catch (tableError) {
      console.error('notifications表不存在:', tableError.message);
      return res.json({
        success: true,
        data: { unreadCount: 0 }
      });
    }

    // 执行查询
    const result = await database.get(
      'SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND is_read = 0',
      [userId]
    );

    console.log('未读通知查询结果:', result);

    res.json({
      success: true,
      data: {
        unreadCount: result ? result.count : 0
      }
    });

  } catch (error) {
    console.error('获取未读通知数量失败:', error.message);

    // 如果数据库查询失败，返回0而不是错误，避免阻塞前端
    res.json({
      success: true,
      data: {
        unreadCount: 0
      }
    });
  }
});

// 生产环境下的前端路由处理
if (process.env.NODE_ENV === 'production') {
  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../client/build/index.html'));
  });
}

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error('服务器错误:', err.stack);
  res.status(500).json({
    success: false,
    message: process.env.NODE_ENV === 'production' ? '服务器内部错误' : err.message
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在'
  });
});

// 启动服务器
const server = app.listen(PORT, () => {
  console.log(`🚀 ScalpAlert 服务器运行在端口 ${PORT}`);
  console.log(`📊 环境: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🔗 API地址: http://localhost:${PORT}/api`);

  // 初始化WebSocket服务
  websocketService.initialize(server);

  // 启动自动信号检测服务
  setTimeout(() => {
    console.log('🤖 启动自动信号检测服务...');
    autoSignalService.start();
  }, 5000); // 延迟5秒启动，确保数据库连接稳定
});

// 优雅关闭
process.on('SIGTERM', async () => {
  console.log('收到 SIGTERM 信号，正在关闭服务器...');

  // 停止自动信号检测
  autoSignalService.stop();

  server.close(async () => {
    console.log('HTTP 服务器已关闭');
    try {
      websocketService.close();
      await database.close();
      console.log('数据库连接已关闭');
      process.exit(0);
    } catch (error) {
      console.error('关闭数据库连接失败:', error);
      process.exit(1);
    }
  });
});

process.on('SIGINT', async () => {
  console.log('收到 SIGINT 信号，正在关闭服务器...');
  server.close(async () => {
    console.log('HTTP 服务器已关闭');
    try {
      websocketService.close();
      await database.close();
      console.log('数据库连接已关闭');
      process.exit(0);
    } catch (error) {
      console.error('关闭数据库连接失败:', error);
      process.exit(1);
    }
  });
});

module.exports = app;
