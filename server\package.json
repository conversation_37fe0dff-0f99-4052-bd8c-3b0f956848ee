{"name": "scalp-alert-server", "version": "1.0.0", "description": "ScalpAlert 后端API服务", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "init-db": "node scripts/init-database.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "sqlite3": "^5.1.6", "ws": "^8.14.2", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "axios": "^1.6.2", "node-cron": "^3.0.3", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2"}}