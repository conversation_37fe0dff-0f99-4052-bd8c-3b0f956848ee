/**
 * 统一的信号生成核心逻辑
 * 确保手动触发、自动触发、市场分析使用相同的算法
 */

const signalService = require('./signalService');
const signalAnalyzer = require('./signalAnalyzer');

class SignalGenerator {
  constructor() {
    // 默认配置
    this.defaultConfig = {
      rsi_strong_threshold: 30,
      rsi_medium_threshold: 40,
      risk_strong: 0.01,
      risk_medium: 0.008,
      risk_weak: 0.005,
      atr_multiplier: 1.5,
      risk_reward_ratio: 2.0,
      volume_multiplier: 1.5,
      enable_volume_filter: 1,
      enable_m_head_filter: 0,
      enable_w_bottom_bonus: 1,
      skip_weak_signals: 0,
      min_quality_score: 40
    };
  }

  /**
   * 统一的信号生成核心逻辑
   * @param {string} asset - 资产名称
   * @param {Object} config - 用户配置
   * @returns {Object} 完整的信号分析结果
   */
  async generateSignal(asset, config = this.defaultConfig) {
    console.log(`🎯 开始统一信号生成 - 资产: ${asset}`);

    try {
      // 1. 获取历史数据
      const historicalData = await signalService.getHistoricalData(asset, 7);
      if (!historicalData || historicalData.length === 0) {
        throw new Error('无法获取历史数据');
      }

    // 2. 获取当前价格数据
    const priceData = await signalService.getPriceData(asset);
    const currentPrice = priceData.usd;

    // 3. 计算技术指标
    const rsi = signalService.calculateRSI(historicalData, 14);
    const emaShort = signalService.calculateEMA(historicalData.slice(-9), 9);
    const emaLong = signalService.calculateEMA(historicalData.slice(-21), 21);
    const trend4h = signalService.determine4hTrend(historicalData.slice(-4));
    const pattern = signalService.detectPattern(historicalData);
    const macd = signalService.calculateMACD(historicalData);
    const atr = signalService.calculateATR(historicalData);
    const volumeRatio = signalService.calculateVolumeRatio(historicalData);

    // 4. 市场状态识别
    const marketStateResult = signalService.identifyMarketState(historicalData);
    const marketState = {
      state: this.mapMarketState(marketStateResult),
      trend_strength: this.calculateTrendStrength(historicalData, marketStateResult),
      raw_state: marketStateResult
    };

    // 5. 多时间周期分析
    const mtfAnalysis = await signalService.analyzeMultiTimeframe(asset);

    console.log(`📊 技术指标 - RSI: ${rsi.toFixed(1)}, EMA短: ${emaShort.toFixed(2)}, EMA长: ${emaLong.toFixed(2)}`);
    console.log(`📈 趋势分析 - 4H: ${trend4h}, 形态: ${pattern}, ATR: ${atr.toFixed(4)}`);
    console.log(`🌊 市场状态: ${marketState.state} (趋势强度: ${marketState.trend_strength})`);
    console.log(`🔄 多时间周期建议: ${mtfAnalysis.recommendation}`);

    // 6. 构建信号数据
    const signalData = {
      rsi,
      ema_short: emaShort,
      ema_long: emaLong,
      price: currentPrice,
      trend_4h: trend4h,
      pattern,
      volume_ratio: volumeRatio,
      m_head_detected: pattern === 'MHead',
      w_bottom_detected: pattern === 'WBottom',
      macd,
      atr,
      mtfAnalysis,
      marketState // 添加市场状态
    };

    // 6. 使用信号分析器计算质量评分和强度
    const analysisResult = signalAnalyzer.analyzeSignal(signalData, config);
    if (!analysisResult) {
      throw new Error('信号分析失败');
    }
    console.log(`📊 分析结果 - 强度: ${analysisResult.strength}, 评分: ${analysisResult.quality_score}`);

    // 8. 统一的信号类型判断逻辑（基于市场状态）
    const signalTypeResult = this.determineSignalType(signalData, config, mtfAnalysis, macd, marketState);
    
    console.log(`📈 信号类型: ${signalTypeResult.type} (${signalTypeResult.reason})`);

    // 9. 计算动态止损止盈（考虑手续费和市场结构）
    const stopLossTakeProfit = this.calculateDynamicStopLossTakeProfit(
      currentPrice, atr, signalTypeResult.type, config, historicalData
    );
    const stopLossPrice = stopLossTakeProfit.stopLoss;
    const takeProfitPrice = stopLossTakeProfit.takeProfit;

    // 9. 返回完整的信号结果
    return {
      asset,
      price: currentPrice,
      rsi,
      ema_short: emaShort,
      ema_long: emaLong,
      trend_4h: trend4h,
      pattern,
      signal_type: signalTypeResult.type,
      signal_strength: analysisResult.strength,
      suggested_position: analysisResult.suggested_position,
      volume_ratio: volumeRatio,
      quality_score: analysisResult.quality_score,
      m_head_detected: signalData.m_head_detected,
      w_bottom_detected: signalData.w_bottom_detected,
      entry_price: currentPrice,
      stop_loss: stopLossPrice,
      take_profit: takeProfitPrice,
      macd,
      atr,
      mtf_analysis: mtfAnalysis,
      analysis_details: analysisResult.analysis_details,
      description: analysisResult.description,
      signal_reason: signalTypeResult.reason,
      timestamp: new Date().toISOString(),
      // 判断是否应该跳过
      should_skip: analysisResult.strength === 'skip' || signalTypeResult.type === 'hold',
      skip_reason: analysisResult.strength === 'skip' ? '信号质量不符合要求' :
                   signalTypeResult.type === 'hold' ? '当前无交易信号' : null
    };

    } catch (error) {
      console.error(`❌ 统一信号生成失败 - 资产: ${asset}`, error.message);
      throw error;
    }
  }

  /**
   * 统一的信号类型判断逻辑（基于市场状态的自适应策略）
   */
  determineSignalType(signalData, config, mtfAnalysis, macd, marketState) {
    const { rsi, ema_short, ema_long, trend_4h, price } = signalData;
    
    // MACD确认逻辑
    const macdBullish = macd.macd > macd.signal && macd.histogram > 0;
    const macdBearish = macd.macd < macd.signal && macd.histogram < 0;

    // 多时间周期过滤逻辑
    const canGoLong = ['long_only', 'long_preferred', 'long_bias'].includes(mtfAnalysis.recommendation);
    const canGoShort = ['short_only', 'short_preferred', 'short_bias'].includes(mtfAnalysis.recommendation);

    // 根据市场状态采用不同的交易策略
    if (marketState.state === 'trending') {
      // 趋势市：顺势交易策略
      return this.determineTrendingSignal(signalData, config, canGoLong, canGoShort, macdBullish, macdBearish);
    } else if (marketState.state === 'ranging') {
      // 震荡市：高抛低吸策略
      return this.determineRangingSignal(signalData, config, canGoLong, canGoShort);
    } else {
      // 不确定市场状态：使用保守策略
      return this.determineConservativeSignal(signalData, config, canGoLong, canGoShort, macdBullish, macdBearish);
    }
  }

  /**
   * 趋势市策略：顺势交易，等待回调买入
   */
  determineTrendingSignal(signalData, config, canGoLong, canGoShort, macdBullish, macdBearish) {
    const { rsi, ema_short, ema_long, trend_4h } = signalData;

    // 趋势市买入：等待价格回调至EMA附近，RSI非超买时买入
    if (canGoLong && rsi < 50 && ema_short > ema_long && trend_4h === 'up') {
      if (macdBullish && rsi < config.rsi_strong_threshold) {
        return { type: 'buy', reason: '趋势市强买入：价格回调至支撑 + MACD确认' };
      } else if (rsi < config.rsi_medium_threshold) {
        return { type: 'buy', reason: '趋势市买入：价格回调至超卖区域' };
      } else {
        return { type: 'buy', reason: '趋势市弱买入：价格回调机会' };
      }
    }

    // 趋势市卖出：等待价格反弹至阻力，RSI非超卖时卖出
    if (canGoShort && rsi > 50 && ema_short < ema_long && trend_4h === 'down') {
      if (macdBearish && rsi > 70) {
        return { type: 'sell', reason: '趋势市强卖出：价格反弹至阻力 + MACD确认' };
      } else if (rsi > 65) {
        return { type: 'sell', reason: '趋势市卖出：价格反弹至超买区域' };
      } else {
        return { type: 'sell', reason: '趋势市弱卖出：价格反弹机会' };
      }
    }

    return { type: 'hold', reason: '趋势市：等待更好的回调/反弹机会' };
  }

  /**
   * 震荡市策略：高抛低吸（适度放宽条件）
   */
  determineRangingSignal(signalData, config, canGoLong, canGoShort) {
    const { rsi, ema_short, ema_long } = signalData;

    // 震荡市买入：放宽RSI条件，增加交易机会
    if (canGoLong && rsi < config.rsi_strong_threshold) {
      return { type: 'buy', reason: '震荡市强买入：RSI超卖，抄底机会' };
    } else if (canGoLong && rsi < config.rsi_medium_threshold && ema_short > ema_long) {
      return { type: 'buy', reason: '震荡市买入：RSI偏低 + EMA支撑' };
    } else if (canGoLong && rsi < 50 && ema_short > ema_long) {
      return { type: 'buy', reason: '震荡市弱买入：RSI中性偏弱 + EMA金叉' };
    }

    // 震荡市卖出：放宽RSI条件
    if (canGoShort && rsi > 70) {
      return { type: 'sell', reason: '震荡市强卖出：RSI超买，高抛机会' };
    } else if (canGoShort && rsi > 65 && ema_short < ema_long) {
      return { type: 'sell', reason: '震荡市卖出：RSI偏高 + EMA阻力' };
    } else if (canGoShort && rsi > 50 && ema_short < ema_long) {
      return { type: 'sell', reason: '震荡市弱卖出：RSI中性偏强 + EMA死叉' };
    }

    return { type: 'hold', reason: '震荡市：等待更明确的交易机会' };
  }

  /**
   * 保守策略：不确定市场状态时使用
   */
  determineConservativeSignal(signalData, config, canGoLong, canGoShort, macdBullish, macdBearish) {
    const { rsi, ema_short, ema_long, trend_4h } = signalData;

    // 保守买入：需要多个确认信号
    if (canGoLong && macdBullish && rsi < config.rsi_strong_threshold &&
        ema_short > ema_long && trend_4h === 'up') {
      return { type: 'buy', reason: '保守买入：多重技术指标确认' };
    }

    // 保守卖出：需要多个确认信号
    if (canGoShort && macdBearish && rsi > 70 &&
        ema_short < ema_long && trend_4h === 'down') {
      return { type: 'sell', reason: '保守卖出：多重技术指标确认' };
    }

    return { type: 'hold', reason: '保守策略：技术指标确认不足，观望' };
  }

  /**
   * 动态止损止盈计算（基于ATR和市场结构）
   */
  calculateDynamicStopLossTakeProfit(currentPrice, atr, signalType, config, historicalData) {
    const feeRate = 0.001; // 0.1% 手续费
    const totalFeeRate = feeRate * 2; // 买入和卖出各0.1%

    let stopLossPrice, takeProfitPrice;

    if (signalType === 'buy') {
      // 基于ATR的动态止损
      stopLossPrice = currentPrice - (atr * config.atr_multiplier) - (currentPrice * totalFeeRate);

      // 动态止盈：寻找最近的阻力位
      const resistanceLevel = this.findNearestResistance(currentPrice, historicalData);
      const atrBasedTarget = currentPrice + (atr * config.atr_multiplier * config.risk_reward_ratio);

      // 选择更保守的目标（阻力位或ATR目标中较近的）
      if (resistanceLevel && resistanceLevel < atrBasedTarget * 1.1) {
        // 如果阻力位在ATR目标的110%范围内，使用阻力位
        takeProfitPrice = resistanceLevel - (currentPrice * totalFeeRate);
      } else {
        // 否则使用ATR基础目标
        takeProfitPrice = atrBasedTarget + (currentPrice * totalFeeRate);
      }

    } else if (signalType === 'sell') {
      // 卖出信号：相反方向
      stopLossPrice = currentPrice + (atr * config.atr_multiplier) + (currentPrice * totalFeeRate);

      // 动态止盈：寻找最近的支撑位
      const supportLevel = this.findNearestSupport(currentPrice, historicalData);
      const atrBasedTarget = currentPrice - (atr * config.atr_multiplier * config.risk_reward_ratio);

      // 选择更保守的目标
      if (supportLevel && supportLevel > atrBasedTarget * 0.9) {
        takeProfitPrice = supportLevel + (currentPrice * totalFeeRate);
      } else {
        takeProfitPrice = atrBasedTarget - (currentPrice * totalFeeRate);
      }
    }

    return {
      stopLoss: Math.max(0, stopLossPrice),
      takeProfit: Math.max(0, takeProfitPrice)
    };
  }

  /**
   * 寻找最近的阻力位
   */
  findNearestResistance(currentPrice, historicalData) {
    if (!historicalData || historicalData.length < 20) return null;

    const recentHighs = [];
    const lookback = Math.min(50, historicalData.length);

    // 寻找最近50根K线的局部高点
    for (let i = 2; i < lookback - 2; i++) {
      const high = historicalData[i][2]; // High price
      const prevHigh = historicalData[i-1][2];
      const nextHigh = historicalData[i+1][2];
      const prev2High = historicalData[i-2][2];
      const next2High = historicalData[i+2][2];

      // 如果是局部高点
      if (high > prevHigh && high > nextHigh && high > prev2High && high > next2High) {
        recentHighs.push(high);
      }
    }

    // 找到高于当前价格的最近阻力位
    const resistanceLevels = recentHighs
      .filter(high => high > currentPrice * 1.01) // 至少高于当前价格1%
      .sort((a, b) => a - b); // 从低到高排序

    return resistanceLevels.length > 0 ? resistanceLevels[0] : null;
  }

  /**
   * 寻找最近的支撑位
   */
  findNearestSupport(currentPrice, historicalData) {
    if (!historicalData || historicalData.length < 20) return null;

    const recentLows = [];
    const lookback = Math.min(50, historicalData.length);

    // 寻找最近50根K线的局部低点
    for (let i = 2; i < lookback - 2; i++) {
      const low = historicalData[i][3]; // Low price
      const prevLow = historicalData[i-1][3];
      const nextLow = historicalData[i+1][3];
      const prev2Low = historicalData[i-2][3];
      const next2Low = historicalData[i+2][3];

      // 如果是局部低点
      if (low < prevLow && low < nextLow && low < prev2Low && low < next2Low) {
        recentLows.push(low);
      }
    }

    // 找到低于当前价格的最近支撑位
    const supportLevels = recentLows
      .filter(low => low < currentPrice * 0.99) // 至少低于当前价格1%
      .sort((a, b) => b - a); // 从高到低排序

    return supportLevels.length > 0 ? supportLevels[0] : null;
  }

  /**
   * 映射市场状态到我们的策略分类
   */
  mapMarketState(rawState) {
    switch (rawState) {
      case 'uptrend':
      case 'downtrend':
        return 'trending';
      case 'ranging':
        return 'ranging';
      case 'consolidation':
        return 'ranging'; // 盘整也归类为震荡
      default:
        return 'uncertain';
    }
  }

  /**
   * 计算趋势强度
   */
  calculateTrendStrength(historicalData, marketState) {
    if (!historicalData || historicalData.length < 20) return 0;

    const prices = historicalData.slice(-20).map(d => d[4]); // 收盘价
    const currentPrice = prices[prices.length - 1];
    const firstPrice = prices[0];

    // 计算价格变化百分比
    const priceChange = (currentPrice - firstPrice) / firstPrice;

    // 根据市场状态调整强度计算
    switch (marketState) {
      case 'uptrend':
      case 'downtrend':
        return Math.abs(priceChange) * 100; // 趋势市：价格变化幅度
      case 'ranging':
        return Math.min(Math.abs(priceChange) * 50, 2); // 震荡市：较低强度
      case 'consolidation':
        return Math.min(Math.abs(priceChange) * 25, 1); // 盘整市：最低强度
      default:
        return 0;
    }
  }

  /**
   * 简化的信号类型判断（用于快速预判）
   */
  quickSignalType(rsi, emaShort, emaLong, trend4h, config) {
    if (rsi < config.rsi_medium_threshold || (emaShort > emaLong && trend4h === 'up')) {
      return 'buy';
    } else if (rsi > 65 || (emaShort < emaLong && trend4h === 'down')) {
      return 'sell';
    }
    return 'hold';
  }
}

module.exports = new SignalGenerator();
