import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { <PERSON>, Settings, ArrowLeft } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { notificationAPI } from '../../utils/api';

const Header = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [unreadCount, setUnreadCount] = useState(0);

  // 获取未读通知数量
  useEffect(() => {
    const fetchUnreadCount = async () => {
      try {
        const response = await notificationAPI.getUnreadCount();
        setUnreadCount(response.data.data.unreadCount);
      } catch (error) {
        console.error('获取未读通知数量失败:', error);
      }
    };

    if (user) {
      fetchUnreadCount();
      // 每30秒刷新一次未读数量
      const interval = setInterval(fetchUnreadCount, 30000);
      return () => clearInterval(interval);
    }
  }, [user]);

  // 获取页面标题
  const getPageTitle = () => {
    switch (location.pathname) {
      case '/':
      case '/dashboard':
        return 'ScalpAlert';
      case '/signals':
        return '交易信号';
      case '/trades':
        return '交易记录';
      case '/statistics':
        return '统计分析';
      case '/settings':
        return '设置';
      case '/notifications':
        return '通知中心';
      default:
        return 'ScalpAlert';
    }
  };

  // 是否显示返回按钮
  const showBackButton = () => {
    return !['/dashboard', '/'].includes(location.pathname);
  };

  // 处理返回
  const handleBack = () => {
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      navigate('/dashboard');
    }
  };

  return (
    <header className="mobile-header">
      <div className="flex items-center justify-between">
        {/* 左侧 - 返回按钮或Logo */}
        <div className="flex items-center">
          {showBackButton() ? (
            <button
              onClick={handleBack}
              className="p-2 -ml-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft size={20} />
            </button>
          ) : (
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gradient-to-r from-primary-600 to-primary-800 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">SA</span>
              </div>
            </div>
          )}
        </div>

        {/* 中间 - 页面标题 */}
        <div className="flex-1 text-center">
          <h1 className="text-lg font-semibold text-gray-900">
            {getPageTitle()}
          </h1>
          {user && location.pathname === '/dashboard' && (
            <p className="text-xs text-gray-500 mt-0.5">
              本金: ${user.capital?.toLocaleString()}
            </p>
          )}
        </div>

        {/* 右侧 - 通知和设置 */}
        <div className="flex items-center space-x-2">
          {/* 通知按钮 */}
          <button
            onClick={() => navigate('/notifications')}
            className="relative p-2 text-gray-600 hover:text-gray-900 transition-colors"
          >
            <Bell size={20} />
            {unreadCount > 0 && (
              <span className="absolute -top-1 -right-1 bg-danger-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                {unreadCount > 99 ? '99+' : unreadCount}
              </span>
            )}
          </button>

          {/* 设置按钮 */}
          <button
            onClick={() => navigate('/settings')}
            className="p-2 text-gray-600 hover:text-gray-900 transition-colors"
          >
            <Settings size={20} />
          </button>
        </div>
      </div>
    </header>
  );
};

export default Header;
