/**
 * 资金管理模块
 * 管理回测过程中的资金、持仓和交易记录
 */

class Portfolio {
  constructor(initialCapital = 10000, tradingFee = 0.001) {
    this.initialCapital = initialCapital;
    this.capital = initialCapital;
    this.tradingFee = tradingFee; // 0.1% 手续费 (买入和卖出各0.1%)
    
    this.position = null; // 当前持仓
    this.trades = []; // 已完成的交易记录
    this.equity = []; // 权益曲线
    
    this.peakCapital = initialCapital;
    this.maxDrawdown = 0;
    this.totalFees = 0;
  }

  /**
   * 开仓
   * @param {Object} signal - 信号对象
   * @param {number} timestamp - 时间戳
   * @param {Object} config - 配置
   */
  openPosition(signal, timestamp, config) {
    if (this.position) {
      console.warn('已有持仓，无法开新仓');
      return false;
    }

    // 根据信号强度确定风险参数
    let riskRatio;
    switch (signal.signal_strength) {
      case 'strong':
        riskRatio = config.risk_strong;
        break;
      case 'medium':
        riskRatio = config.risk_medium;
        break;
      case 'weak':
        riskRatio = config.risk_weak;
        break;
      default:
        return false;
    }

    // 计算仓位大小
    const riskAmount = this.capital * riskRatio;
    const stopLossDistance = signal.entry_price - signal.stop_loss;
    
    if (stopLossDistance <= 0) {
      console.warn('止损价格设置错误');
      return false;
    }

    const positionSize = riskAmount / stopLossDistance;
    const positionValue = positionSize * signal.entry_price;
    
    // 计算买入手续费
    const buyFee = positionValue * this.tradingFee;
    const requiredCapital = positionValue + buyFee;

    // 检查资金是否足够
    if (requiredCapital > this.capital) {
      console.warn('资金不足，无法开仓');
      return false;
    }

    // 扣除买入资金和手续费
    this.capital -= requiredCapital;
    this.totalFees += buyFee;

    // 创建持仓记录
    this.position = {
      asset: signal.asset,
      entryPrice: signal.entry_price,
      size: positionSize,
      stopLoss: signal.stop_loss,
      takeProfit: signal.take_profit,
      entryTime: timestamp,
      entryValue: positionValue,
      signal: signal
    };

    console.log(`📈 开仓: ${signal.asset} @ $${signal.entry_price.toFixed(2)}, 数量: ${positionSize.toFixed(6)}`);
    return true;
  }

  /**
   * 平仓
   * @param {number} exitPrice - 平仓价格
   * @param {number} timestamp - 时间戳
   * @param {string} reason - 平仓原因
   */
  closePosition(exitPrice, timestamp, reason) {
    if (!this.position) {
      console.warn('没有持仓，无法平仓');
      return false;
    }

    const exitValue = this.position.size * exitPrice;
    const sellFee = exitValue * this.tradingFee;
    const netExitValue = exitValue - sellFee;
    
    // 计算盈亏
    const pnl = netExitValue - this.position.entryValue;
    const pnlPercent = (pnl / this.position.entryValue) * 100;
    
    // 更新资金
    this.capital += netExitValue;
    this.totalFees += sellFee;

    // 计算总手续费（买入 + 卖出）
    const buyFee = this.position.entryValue * this.tradingFee;
    const totalFees = buyFee + sellFee;

    // 记录交易
    const trade = {
      ...this.position,
      exitPrice,
      exitTime: timestamp,
      exitValue: netExitValue,
      pnl,
      pnlPercent,
      reason,
      holdingTime: timestamp - this.position.entryTime,
      fees: totalFees
    };

    this.trades.push(trade);
    this.position = null;

    console.log(`📉 平仓: ${trade.asset} @ $${exitPrice.toFixed(2)}, 盈亏: $${pnl.toFixed(2)} (${pnlPercent.toFixed(2)}%), 原因: ${reason}`);
    
    return trade;
  }

  /**
   * 检查止损止盈
   * @param {number} currentPrice - 当前价格
   * @param {number} timestamp - 时间戳
   * @returns {Object|null} 如果触发平仓返回交易记录，否则返回null
   */
  checkStopLossTakeProfit(currentPrice, timestamp) {
    if (!this.position) return null;

    // 检查止损
    if (currentPrice <= this.position.stopLoss) {
      return this.closePosition(currentPrice, timestamp, 'Stop Loss');
    }

    // 检查止盈
    if (currentPrice >= this.position.takeProfit) {
      return this.closePosition(currentPrice, timestamp, 'Take Profit');
    }

    return null;
  }

  /**
   * 更新权益曲线
   * @param {number} timestamp - 时间戳
   * @param {number} currentPrice - 当前价格（如果有持仓）
   */
  updateEquity(timestamp, currentPrice = null) {
    let totalEquity = this.capital;
    
    // 如果有持仓，加上未实现盈亏
    if (this.position && currentPrice) {
      const unrealizedPnl = (currentPrice - this.position.entryPrice) * this.position.size;
      totalEquity += this.position.entryValue + unrealizedPnl;
    }

    this.equity.push({
      timestamp,
      capital: this.capital,
      totalEquity,
      drawdown: this.calculateCurrentDrawdown(totalEquity)
    });

    // 更新最大回撤
    if (totalEquity > this.peakCapital) {
      this.peakCapital = totalEquity;
    }

    const currentDrawdown = (this.peakCapital - totalEquity) / this.peakCapital;
    if (currentDrawdown > this.maxDrawdown) {
      this.maxDrawdown = currentDrawdown;
    }
  }

  /**
   * 计算当前回撤
   * @param {number} currentEquity - 当前权益
   * @returns {number} 回撤百分比
   */
  calculateCurrentDrawdown(currentEquity) {
    return (this.peakCapital - currentEquity) / this.peakCapital;
  }

  /**
   * 获取当前状态
   * @returns {Object} 当前状态信息
   */
  getStatus() {
    const totalEquity = this.getTotalEquity();
    
    return {
      capital: this.capital,
      totalEquity,
      position: this.position,
      totalTrades: this.trades.length,
      totalFees: this.totalFees,
      unrealizedPnl: this.getUnrealizedPnl(),
      maxDrawdown: this.maxDrawdown,
      totalReturn: (totalEquity - this.initialCapital) / this.initialCapital
    };
  }

  /**
   * 获取总权益
   * @param {number} currentPrice - 当前价格（用于计算未实现盈亏）
   * @returns {number} 总权益
   */
  getTotalEquity(currentPrice = null) {
    let totalEquity = this.capital;
    
    if (this.position && currentPrice) {
      const unrealizedPnl = (currentPrice - this.position.entryPrice) * this.position.size;
      totalEquity += this.position.entryValue + unrealizedPnl;
    }
    
    return totalEquity;
  }

  /**
   * 获取未实现盈亏
   * @param {number} currentPrice - 当前价格
   * @returns {number} 未实现盈亏
   */
  getUnrealizedPnl(currentPrice = null) {
    if (!this.position || !currentPrice) return 0;
    
    return (currentPrice - this.position.entryPrice) * this.position.size;
  }

  /**
   * 获取交易统计
   * @returns {Object} 交易统计信息
   */
  getTradeStats() {
    if (this.trades.length === 0) {
      return {
        totalTrades: 0,
        winRate: 0,
        avgWin: 0,
        avgLoss: 0,
        profitFactor: 0,
        maxWin: 0,
        maxLoss: 0
      };
    }

    const winningTrades = this.trades.filter(t => t.pnl > 0);
    const losingTrades = this.trades.filter(t => t.pnl < 0);
    
    const totalWins = winningTrades.reduce((sum, t) => sum + t.pnl, 0);
    const totalLosses = Math.abs(losingTrades.reduce((sum, t) => sum + t.pnl, 0));
    
    return {
      totalTrades: this.trades.length,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      winRate: winningTrades.length / this.trades.length,
      avgWin: winningTrades.length > 0 ? totalWins / winningTrades.length : 0,
      avgLoss: losingTrades.length > 0 ? totalLosses / losingTrades.length : 0,
      profitFactor: totalLosses > 0 ? totalWins / totalLosses : Infinity,
      maxWin: winningTrades.length > 0 ? Math.max(...winningTrades.map(t => t.pnl)) : 0,
      maxLoss: losingTrades.length > 0 ? Math.min(...losingTrades.map(t => t.pnl)) : 0,
      totalPnl: this.trades.reduce((sum, t) => sum + t.pnl, 0)
    };
  }

  /**
   * 重置组合
   */
  reset() {
    this.capital = this.initialCapital;
    this.position = null;
    this.trades = [];
    this.equity = [];
    this.peakCapital = this.initialCapital;
    this.maxDrawdown = 0;
    this.totalFees = 0;
  }

  /**
   * 导出交易记录到CSV
   * @returns {string} CSV格式的交易记录
   */
  exportTradesToCSV() {
    if (this.trades.length === 0) return '';

    const headers = [
      'Asset', 'Entry Time', 'Exit Time', 'Entry Price', 'Exit Price',
      'Size', 'PnL', 'PnL %', 'Reason', 'Holding Time (hours)', 'Fees'
    ];

    const rows = this.trades.map(trade => [
      trade.asset,
      new Date(trade.entryTime).toISOString(),
      new Date(trade.exitTime).toISOString(),
      trade.entryPrice.toFixed(6),
      trade.exitPrice.toFixed(6),
      trade.size.toFixed(6),
      trade.pnl.toFixed(2),
      trade.pnlPercent.toFixed(2),
      trade.reason,
      (trade.holdingTime / (1000 * 60 * 60)).toFixed(2),
      trade.fees.toFixed(2)
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  }
}

module.exports = Portfolio;
