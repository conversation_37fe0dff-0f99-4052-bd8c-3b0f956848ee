{"name": "scalpalert-backtest", "version": "1.0.0", "description": "ScalpAlert 交易策略回测框架", "main": "engine/backtester.js", "scripts": {"download-data": "node data/downloader.js", "download-longterm": "node data/downloadLongTerm.js", "download-validation": "node data/downloadLongTerm.js validation", "download-all": "node data/downloadLongTerm.js all", "run-backtest": "node examples/run_backtest.js", "test-single": "node -e \"require('./examples/run_backtest').runSingleBacktest()\"", "test-batch": "node -e \"require('./examples/run_backtest').runBatchBacktest()\"", "optimize": "node -e \"require('./examples/run_backtest').runOptimizationExample()\"", "analyze-periods": "node -e \"require('./examples/run_backtest').runPeriodAnalysis()\"", "optimize-strategy": "node examples/optimize_strategy.js", "test-conservative": "node examples/optimize_strategy.js conservative", "test-balanced": "node examples/optimize_strategy.js balanced", "test-quality": "node examples/optimize_strategy.js qualityFirst", "dynamic-backtest": "node examples/dynamic_backtest.js single", "dynamic-compare": "node examples/dynamic_backtest.js compare", "dynamic-vs-static": "node examples/dynamic_backtest.js vs-static", "verify-fees": "node examples/verify_fees.js", "optimize-params": "node examples/optimize_parameters.js single", "optimize-batch": "node examples/optimize_parameters.js batch", "optimize-advanced": "node examples/optimize_parameters.js advanced", "new-trend": "node examples/newStrategiesBacktest.js trend", "new-range": "node examples/newStrategiesBacktest.js range", "new-compare": "node examples/newStrategiesBacktest.js compare", "new-all": "node examples/newStrategiesBacktest.js all"}, "keywords": ["trading", "backtest", "cryptocurrency", "strategy", "scalping"], "author": "ScalpAlert Team", "license": "MIT", "dependencies": {"axios": "^1.6.0"}, "devDependencies": {}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-repo/scalpalert-backtest.git"}, "bugs": {"url": "https://github.com/your-repo/scalpalert-backtest/issues"}, "homepage": "https://github.com/your-repo/scalpalert-backtest#readme"}