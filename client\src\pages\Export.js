import React, { useState } from 'react';
import { Download, FileText, BarChart3, Calendar, Filter } from 'lucide-react';
import { exportAPI } from '../utils/api';
import toast from 'react-hot-toast';

const Export = () => {
  const [loading, setLoading] = useState({
    trades: false,
    signals: false,
    report: false,
  });

  const [filters, setFilters] = useState({
    trades: {
      startDate: '',
      endDate: '',
      status: 'all',
    },
    signals: {
      startDate: '',
      endDate: '',
      action: 'all',
    },
    report: {
      period: 'month',
    },
  });

  // 更新过滤条件
  const updateFilter = (type, field, value) => {
    setFilters(prev => ({
      ...prev,
      [type]: {
        ...prev[type],
        [field]: value,
      },
    }));
  };

  // 导出交易记录
  const handleExportTrades = async () => {
    try {
      setLoading(prev => ({ ...prev, trades: true }));
      
      const params = {};
      if (filters.trades.startDate) params.startDate = filters.trades.startDate;
      if (filters.trades.endDate) params.endDate = filters.trades.endDate;
      if (filters.trades.status !== 'all') params.status = filters.trades.status;

      await exportAPI.exportTrades(params);
      toast.success('交易记录导出成功');
    } catch (error) {
      console.error('导出交易记录失败:', error);
      toast.error('导出失败，请重试');
    } finally {
      setLoading(prev => ({ ...prev, trades: false }));
    }
  };

  // 导出信号记录
  const handleExportSignals = async () => {
    try {
      setLoading(prev => ({ ...prev, signals: true }));
      
      const params = {};
      if (filters.signals.startDate) params.startDate = filters.signals.startDate;
      if (filters.signals.endDate) params.endDate = filters.signals.endDate;
      if (filters.signals.action !== 'all') params.action = filters.signals.action;

      await exportAPI.exportSignals(params);
      toast.success('信号记录导出成功');
    } catch (error) {
      console.error('导出信号记录失败:', error);
      toast.error('导出失败，请重试');
    } finally {
      setLoading(prev => ({ ...prev, signals: false }));
    }
  };

  // 生成报告
  const handleGenerateReport = async () => {
    try {
      setLoading(prev => ({ ...prev, report: true }));
      
      const params = {
        period: filters.report.period,
      };

      await exportAPI.generateReport(params);
      toast.success('报告生成成功');
    } catch (error) {
      console.error('生成报告失败:', error);
      toast.error('生成报告失败，请重试');
    } finally {
      setLoading(prev => ({ ...prev, report: false }));
    }
  };

  return (
    <div className="mobile-content">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">数据导出</h1>
        <p className="text-gray-600">导出您的交易数据和生成分析报告</p>
      </div>

      {/* 交易记录导出 */}
      <div className="card mb-6">
        <div className="flex items-center mb-4">
          <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center mr-3">
            <FileText className="text-primary-600" size={20} />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">交易记录</h3>
            <p className="text-sm text-gray-600">导出CSV格式的交易历史</p>
          </div>
        </div>

        {/* 过滤条件 */}
        <div className="bg-gray-50 rounded-lg p-4 mb-4">
          <div className="flex items-center mb-3">
            <Filter size={16} className="mr-2 text-gray-600" />
            <span className="font-medium text-gray-900">筛选条件</span>
          </div>
          
          <div className="grid grid-cols-1 gap-4">
            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="label text-sm">开始日期</label>
                <input
                  type="date"
                  value={filters.trades.startDate}
                  onChange={(e) => updateFilter('trades', 'startDate', e.target.value)}
                  className="input text-sm"
                />
              </div>
              <div>
                <label className="label text-sm">结束日期</label>
                <input
                  type="date"
                  value={filters.trades.endDate}
                  onChange={(e) => updateFilter('trades', 'endDate', e.target.value)}
                  className="input text-sm"
                />
              </div>
            </div>
            
            <div>
              <label className="label text-sm">交易状态</label>
              <select
                value={filters.trades.status}
                onChange={(e) => updateFilter('trades', 'status', e.target.value)}
                className="input text-sm"
              >
                <option value="all">全部</option>
                <option value="open">持仓中</option>
                <option value="closed">已平仓</option>
              </select>
            </div>
          </div>
        </div>

        <button
          onClick={handleExportTrades}
          disabled={loading.trades}
          className="btn-primary w-full"
        >
          {loading.trades ? (
            <div className="loading-spinner mr-2"></div>
          ) : (
            <Download size={16} className="mr-2" />
          )}
          导出交易记录
        </button>
      </div>

      {/* 信号记录导出 */}
      <div className="card mb-6">
        <div className="flex items-center mb-4">
          <div className="w-10 h-10 bg-success-100 rounded-lg flex items-center justify-center mr-3">
            <BarChart3 className="text-success-600" size={20} />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">信号记录</h3>
            <p className="text-sm text-gray-600">导出CSV格式的信号历史</p>
          </div>
        </div>

        {/* 过滤条件 */}
        <div className="bg-gray-50 rounded-lg p-4 mb-4">
          <div className="flex items-center mb-3">
            <Filter size={16} className="mr-2 text-gray-600" />
            <span className="font-medium text-gray-900">筛选条件</span>
          </div>
          
          <div className="grid grid-cols-1 gap-4">
            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="label text-sm">开始日期</label>
                <input
                  type="date"
                  value={filters.signals.startDate}
                  onChange={(e) => updateFilter('signals', 'startDate', e.target.value)}
                  className="input text-sm"
                />
              </div>
              <div>
                <label className="label text-sm">结束日期</label>
                <input
                  type="date"
                  value={filters.signals.endDate}
                  onChange={(e) => updateFilter('signals', 'endDate', e.target.value)}
                  className="input text-sm"
                />
              </div>
            </div>
            
            <div>
              <label className="label text-sm">信号动作</label>
              <select
                value={filters.signals.action}
                onChange={(e) => updateFilter('signals', 'action', e.target.value)}
                className="input text-sm"
              >
                <option value="all">全部</option>
                <option value="buy">买入</option>
                <option value="sell">卖出</option>
              </select>
            </div>
          </div>
        </div>

        <button
          onClick={handleExportSignals}
          disabled={loading.signals}
          className="btn-primary w-full"
        >
          {loading.signals ? (
            <div className="loading-spinner mr-2"></div>
          ) : (
            <Download size={16} className="mr-2" />
          )}
          导出信号记录
        </button>
      </div>

      {/* 统计报告生成 */}
      <div className="card">
        <div className="flex items-center mb-4">
          <div className="w-10 h-10 bg-warning-100 rounded-lg flex items-center justify-center mr-3">
            <Calendar className="text-warning-600" size={20} />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">统计报告</h3>
            <p className="text-sm text-gray-600">生成文本格式的分析报告</p>
          </div>
        </div>

        {/* 报告设置 */}
        <div className="bg-gray-50 rounded-lg p-4 mb-4">
          <div className="flex items-center mb-3">
            <Calendar size={16} className="mr-2 text-gray-600" />
            <span className="font-medium text-gray-900">报告周期</span>
          </div>
          
          <select
            value={filters.report.period}
            onChange={(e) => updateFilter('report', 'period', e.target.value)}
            className="input text-sm w-full"
          >
            <option value="week">本周</option>
            <option value="month">本月</option>
            <option value="year">本年</option>
            <option value="all">全部时间</option>
          </select>
        </div>

        <button
          onClick={handleGenerateReport}
          disabled={loading.report}
          className="btn-primary w-full"
        >
          {loading.report ? (
            <div className="loading-spinner mr-2"></div>
          ) : (
            <Download size={16} className="mr-2" />
          )}
          生成统计报告
        </button>
      </div>

      {/* 使用说明 */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h4 className="font-medium text-blue-900 mb-2">使用说明</h4>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• CSV文件可以用Excel或其他表格软件打开</li>
          <li>• 导出的数据包含完整的交易和信号信息</li>
          <li>• 统计报告包含胜率、盈亏等关键指标</li>
          <li>• 建议定期导出数据进行备份</li>
        </ul>
      </div>
    </div>
  );
};

export default Export;
