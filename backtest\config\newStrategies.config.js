/**
 * 新策略配置文件
 * 包含两个全新的策略方案
 */

// 基础回测配置
const backtestConfig = {
  // 资金设置
  initialCapital: 10000,    // 初始资金
  tradingFee: 0.001,        // 交易手续费 (0.1%)
  
  // 数据设置
  startIndex: 50,           // 开始索引（确保有足够数据计算指标）
  
  // 输出设置
  outputDir: './backtest_results',
  exportResults: true,
  
  // 进度报告
  progressInterval: 1000    // 每1000根K线报告一次进度
};

// 新策略配置
const newStrategyConfigs = {
  // 方案一：趋势跟踪策略（H4/日线）
  trendFollowing: {
    name: '趋势跟踪策略',
    type: 'trend_following',
    description: '拥抱趋势，简化为王 - 追求极高盈亏比',
    
    // 均线参数
    ema_fast: 50,                    // 快速均线EMA(50)
    ema_slow: 200,                   // 慢速均线EMA(200)
    ema_entry: 20,                   // 入场确认均线EMA(20)
    
    // 风险管理
    atr_multiplier: 2.0,             // ATR止损倍数
    risk_per_trade: 0.02,            // 每笔交易风险2%
    
    // 趋势确认
    trend_strength_threshold: 0.005,  // 趋势强度阈值0.5%
    consolidation_threshold: 0.02,    // 震荡判断阈值2%
    
    // K线形态确认
    enable_candlestick_confirmation: true,
    min_candle_body_ratio: 0.6,      // 最小实体比例60%
    
    // 预期表现
    target_win_rate: 0.35,           // 目标胜率35%（低胜率高盈亏比）
    target_profit_factor: 2.5,      // 目标盈亏比2.5
    target_max_drawdown: 0.15       // 目标最大回撤15%
  },

  // 方案二：震荡交易策略（M15/M30）
  rangeTrading: {
    name: '震荡交易策略',
    type: 'range_trading',
    description: '专注震荡，高抛低吸 - 追求高胜率稳定获利',
    
    // 布林带参数
    bb_period: 20,                   // 布林带周期
    bb_std_dev: 2.0,                 // 标准差倍数
    
    // RSI参数
    rsi_period: 14,                  // RSI周期
    rsi_oversold: 30,                // 超卖阈值
    rsi_overbought: 70,              // 超买阈值
    
    // 震荡识别参数
    range_stability_period: 50,      // 震荡稳定性检查周期
    max_band_width_change: 0.1,      // 最大带宽变化10%
    min_touches: 3,                  // 最少触碰次数
    
    // 风险管理
    risk_per_trade: 0.01,            // 每笔交易风险1%
    profit_target_ratio: 1.2,       // 盈亏比1:1.2
    max_stop_distance: 0.02,         // 最大止损距离2%
    
    // 入场确认
    require_volume_confirmation: true,
    min_volume_ratio: 1.2,           // 最小成交量比例
    
    // 预期表现
    target_win_rate: 0.65,           // 目标胜率65%（高胜率低盈亏比）
    target_profit_factor: 1.5,      // 目标盈亏比1.5
    target_max_drawdown: 0.10       // 目标最大回撤10%
  },

  // 趋势跟踪 - 保守版
  trendFollowingConservative: {
    name: '趋势跟踪-保守版',
    type: 'trend_following',
    description: '更保守的趋势跟踪，降低风险',

    // 均线参数
    ema_fast: 30,                    // 更快的均线
    ema_slow: 100,                   // 更短的慢均线
    ema_entry: 20,                   // 入场确认均线EMA(20)

    // 风险管理
    atr_multiplier: 1.5,             // 更紧的止损
    risk_per_trade: 0.015,           // 降低风险到1.5%

    // 趋势确认
    trend_strength_threshold: 0.008, // 更高的趋势强度要求
    consolidation_threshold: 0.02,   // 震荡判断阈值2%

    // K线形态确认
    enable_candlestick_confirmation: true,
    min_candle_body_ratio: 0.6,      // 最小实体比例60%

    // 预期表现
    target_win_rate: 0.40,           // 稍高的胜率目标
    target_profit_factor: 2.0,      // 稍低的盈亏比目标
    target_max_drawdown: 0.12       // 更低的回撤目标
  },

  // 震荡交易 - 激进版
  rangeTradingAggressive: {
    name: '震荡交易-激进版',
    type: 'range_trading',
    description: '更激进的震荡交易，提高收益',

    // 布林带参数
    bb_period: 20,                   // 布林带周期
    bb_std_dev: 2.0,                 // 标准差倍数

    // RSI参数
    rsi_period: 14,                  // RSI周期
    rsi_oversold: 25,                // 更严格的超卖
    rsi_overbought: 75,              // 更严格的超买

    // 震荡识别参数
    range_stability_period: 50,      // 震荡稳定性检查周期
    max_band_width_change: 0.1,      // 最大带宽变化10%
    min_touches: 3,                  // 最少触碰次数

    // 风险管理
    risk_per_trade: 0.015,           // 提高风险到1.5%
    profit_target_ratio: 1.5,       // 提高盈亏比到1.5
    max_stop_distance: 0.02,         // 最大止损距离2%

    // 入场确认
    require_volume_confirmation: true,
    min_volume_ratio: 1.0,           // 降低成交量要求

    // 预期表现
    target_win_rate: 0.60,           // 稍低的胜率目标
    target_profit_factor: 1.8,      // 更高的盈亏比目标
    target_max_drawdown: 0.15       // 稍高的回撤容忍度
  }
};

// 策略优化配置
const optimizationConfigs = {
  // 趋势跟踪优化参数
  trendFollowingOptimization: {
    // EMA参数范围
    ema_fast_range: [20, 30, 50, 70],
    ema_slow_range: [100, 150, 200, 250],
    ema_entry_range: [10, 15, 20, 25],
    
    // 风险参数范围
    atr_multiplier_range: [1.5, 2.0, 2.5, 3.0],
    risk_per_trade_range: [0.01, 0.015, 0.02, 0.025],
    
    // 趋势强度范围
    trend_strength_range: [0.003, 0.005, 0.008, 0.01]
  },

  // 震荡交易优化参数
  rangeTradingOptimization: {
    // 布林带参数范围
    bb_period_range: [15, 20, 25, 30],
    bb_std_dev_range: [1.5, 2.0, 2.5],
    
    // RSI参数范围
    rsi_oversold_range: [20, 25, 30, 35],
    rsi_overbought_range: [65, 70, 75, 80],
    
    // 风险参数范围
    risk_per_trade_range: [0.005, 0.01, 0.015, 0.02],
    profit_target_ratio_range: [1.0, 1.2, 1.5, 1.8]
  }
};

// 回测场景配置
const backtestScenarios = {
  // 完整回测
  fullBacktest: {
    name: '完整历史回测',
    description: '使用全部历史数据进行回测',
    useFullData: true
  },

  // 分段回测
  segmentedBacktest: {
    name: '分段回测',
    description: '分不同时间段进行回测',
    segments: [
      { name: '牛市阶段', startDate: '2023-01-01', endDate: '2023-06-30' },
      { name: '熊市阶段', startDate: '2023-07-01', endDate: '2023-12-31' },
      { name: '震荡阶段', startDate: '2024-01-01', endDate: '2024-06-30' }
    ]
  },

  // 对比回测
  comparisonBacktest: {
    name: '策略对比回测',
    description: '同时测试多个策略并对比结果',
    strategies: ['trendFollowing', 'rangeTrading'],
    generateComparison: true
  }
};

module.exports = {
  backtestConfig,
  newStrategyConfigs,
  optimizationConfigs,
  backtestScenarios
};
