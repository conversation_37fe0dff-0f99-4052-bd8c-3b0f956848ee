import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { TrendingUp, AlertTriangle, DollarSign, Activity, Plus, RefreshCw } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { signalAPI, tradeAPI } from '../utils/api';
import { formatCurrency, formatPercentage, getSignalStrengthColor, getSignalStrengthText } from '../utils/api';
import toast from 'react-hot-toast';

const Dashboard = () => {
  const navigate = useNavigate();
  const { user, refreshUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [dashboardData, setDashboardData] = useState({
    latestSignals: [],
    openTrades: [],
    todayStats: null,
  });

  // 获取仪表盘数据
  const fetchDashboardData = async () => {
    try {
      console.log('开始获取仪表盘数据...');

      // 添加超时处理
      const timeout = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('请求超时')), 10000)
      );

      const apiCalls = Promise.all([
        signalAPI.getLatest({ limit: 5 }),
        tradeAPI.getList({ status: 'open', limit: 5 }),
        tradeAPI.getStats({ period: 'today' }),
      ]);

      const [signalsRes, tradesRes, statsRes] = await Promise.race([apiCalls, timeout]);

      console.log('仪表盘数据获取成功');

      setDashboardData({
        latestSignals: signalsRes.data.data || [],
        openTrades: tradesRes.data.data?.trades || [],
        todayStats: statsRes.data.data || null,
      });
    } catch (error) {
      console.error('获取仪表盘数据失败:', error);
      toast.error('获取数据失败');

      // 设置默认数据，避免页面卡死
      setDashboardData({
        latestSignals: [],
        openTrades: [],
        todayStats: null,
      });
    } finally {
      setLoading(false);
    }
  };

  // 刷新数据
  const handleRefresh = async () => {
    setRefreshing(true);
    await Promise.all([
      fetchDashboardData(),
      refreshUser(),
    ]);
    setRefreshing(false);
    toast.success('数据已刷新');
  };

  // 生成新信号
  const handleGenerateSignal = async () => {
    try {
      setRefreshing(true);
      const response = await signalAPI.generate({ asset: 'bitcoin' });
      
      if (response.data.data) {
        toast.success('新信号已生成');
        fetchDashboardData();
      } else {
        toast.info(response.data.message);
      }
    } catch (error) {
      console.error('生成信号失败:', error);
    } finally {
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, []); // 确保只在组件挂载时执行一次

  if (loading) {
    return (
      <div className="mobile-content">
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="card">
              <div className="skeleton h-4 w-3/4 mb-2"></div>
              <div className="skeleton h-6 w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  const { latestSignals, openTrades, todayStats } = dashboardData;

  return (
    <div className="mobile-content">
      {/* 用户信息卡片 */}
      <div className="card mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">
              你好，{user?.username}
            </h2>
            <p className="text-sm text-gray-600">
              {new Date().toLocaleDateString('zh-CN', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </p>
          </div>
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="p-2 text-gray-600 hover:text-gray-900 transition-colors"
          >
            <RefreshCw size={20} className={refreshing ? 'animate-spin' : ''} />
          </button>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="text-center">
            <p className="text-sm text-gray-600">当前本金</p>
            <p className="text-xl font-bold text-gray-900">
              {formatCurrency(user?.capital)}
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-600">今日盈亏</p>
            <p className={`text-xl font-bold ${
              todayStats?.net_profit > 0 ? 'text-success-600' : 
              todayStats?.net_profit < 0 ? 'text-danger-600' : 'text-gray-600'
            }`}>
              {formatCurrency(todayStats?.net_profit || 0)}
            </p>
          </div>
        </div>
      </div>

      {/* 今日统计 */}
      {todayStats && (
        <div className="card mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">今日统计</h3>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                <Activity className="text-primary-600" size={20} />
              </div>
              <p className="text-sm text-gray-600">交易次数</p>
              <p className="text-lg font-semibold text-gray-900">
                {todayStats.total_trades}
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-success-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                <TrendingUp className="text-success-600" size={20} />
              </div>
              <p className="text-sm text-gray-600">胜率</p>
              <p className="text-lg font-semibold text-gray-900">
                {formatPercentage(todayStats.win_rate)}
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-warning-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                <DollarSign className="text-warning-600" size={20} />
              </div>
              <p className="text-sm text-gray-600">平均收益</p>
              <p className="text-lg font-semibold text-gray-900">
                {formatCurrency(todayStats.avg_profit || 0)}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* 最新信号 */}
      <div className="card mb-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">最新信号</h3>
          <div className="flex space-x-2">
            <button
              onClick={handleGenerateSignal}
              disabled={refreshing}
              className="btn-primary text-sm px-3 py-1.5"
            >
              <Plus size={16} className="mr-1" />
              生成信号
            </button>
            <button
              onClick={() => navigate('/signals')}
              className="btn-outline text-sm px-3 py-1.5"
            >
              查看全部
            </button>
          </div>
        </div>

        {latestSignals.length > 0 ? (
          <div className="space-y-3">
            {latestSignals.map((signal) => (
              <div
                key={signal.id}
                onClick={() => navigate(`/signals/${signal.id}`)}
                className={`p-4 border rounded-lg cursor-pointer hover:shadow-md transition-shadow ${
                  signal.signal_strength === 'strong' ? 'border-success-200 bg-success-50' :
                  signal.signal_strength === 'medium' ? 'border-warning-200 bg-warning-50' :
                  'border-gray-200 bg-gray-50'
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-gray-900">
                      {signal.asset.toUpperCase()}
                    </span>
                    <span className={`badge ${getSignalStrengthColor(signal.signal_strength)}`}>
                      {getSignalStrengthText(signal.signal_strength)}
                    </span>
                  </div>
                  <span className="text-sm text-gray-600">
                    {new Date(signal.timestamp).toLocaleTimeString('zh-CN', {
                      hour: '2-digit',
                      minute: '2-digit',
                    })}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">建议仓位</p>
                    <p className="font-semibold text-primary-600">
                      {formatCurrency(signal.suggested_position)}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-600">RSI</p>
                    <p className="font-semibold text-gray-900">
                      {signal.rsi?.toFixed(1)}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <AlertTriangle className="mx-auto text-gray-400 mb-2" size={48} />
            <p className="text-gray-600">暂无信号</p>
            <button
              onClick={handleGenerateSignal}
              className="btn-primary mt-4"
            >
              生成第一个信号
            </button>
          </div>
        )}
      </div>

      {/* 持仓交易 */}
      <div className="card">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">持仓交易</h3>
          <button
            onClick={() => navigate('/trades')}
            className="btn-outline text-sm px-3 py-1.5"
          >
            查看全部
          </button>
        </div>

        {openTrades.length > 0 ? (
          <div className="space-y-3">
            {openTrades.map((trade) => (
              <div
                key={trade.id}
                onClick={() => navigate(`/trades/${trade.id}`)}
                className="p-4 border border-gray-200 rounded-lg cursor-pointer hover:shadow-md transition-shadow"
              >
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium text-gray-900">
                    {trade.asset.toUpperCase()}
                  </span>
                  <span className="badge badge-primary">持仓中</span>
                </div>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-gray-600">买入价格</p>
                    <p className="font-semibold">${trade.buy_price}</p>
                  </div>
                  <div>
                    <p className="text-gray-600">投入金额</p>
                    <p className="font-semibold">{formatCurrency(trade.buy_amount)}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <Activity className="mx-auto text-gray-400 mb-2" size={48} />
            <p className="text-gray-600">暂无持仓</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Dashboard;
