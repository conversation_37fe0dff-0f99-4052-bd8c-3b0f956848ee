/**
 * 策略参数优化示例
 * 自动优化策略参数直到达到理想的胜率和收益率
 */

const path = require('path');
const ParameterOptimizer = require('../engine/parameterOptimizer');
const { strategyConfigs } = require('../config/backtest.config');

/**
 * 优化单个策略
 */
async function optimizeSingleStrategy() {
  console.log('🎯 策略参数自动优化系统');
  console.log('='.repeat(50));
  
  try {
    const dataPath = path.join(__dirname, '../data/ETHUSDT_1h.json');
    
    // 创建优化器
    const optimizer = new ParameterOptimizer({
      targetWinRate: 0.45,        // 目标胜率45%
      targetReturn: 0.05,         // 目标收益率5%
      maxIterations: 15,          // 最大15轮优化
      improvementThreshold: 0.01  // 1%改进阈值
    });
    
    // 选择要优化的基础策略
    const baseStrategy = strategyConfigs.balanced;
    
    console.log(`📊 基础策略: ${baseStrategy.name}`);
    console.log(`📈 初始参数:`);
    console.log(`   胜率目标: 45%`);
    console.log(`   收益目标: 5%`);
    console.log(`   最大轮数: 15轮\n`);
    
    // 运行优化
    const result = await optimizer.optimizeStrategy(dataPath, baseStrategy);
    
    // 显示结果
    console.log('\n📊 优化结果对比:');
    console.log('='.repeat(60));
    console.log('指标                原始策略        优化策略        改进');
    console.log('-'.repeat(60));
    
    // 需要先运行原始策略获取基准
    console.log('🔄 运行原始策略作为基准...');
    const originalResult = await optimizer.runBacktest(dataPath, baseStrategy);
    
    const comparisons = [
      { 
        name: '胜率%', 
        original: originalResult.metrics.winRate * 100, 
        optimized: result.finalMetrics.winRate * 100 
      },
      { 
        name: '总收益%', 
        original: originalResult.metrics.totalReturn * 100, 
        optimized: result.finalMetrics.totalReturn * 100 
      },
      { 
        name: '盈亏比', 
        original: originalResult.metrics.profitFactor, 
        optimized: result.finalMetrics.profitFactor 
      },
      { 
        name: '最大回撤%', 
        original: originalResult.metrics.maxDrawdown * 100, 
        optimized: result.finalMetrics.maxDrawdown * 100 
      },
      { 
        name: '交易数', 
        original: originalResult.metrics.totalTrades, 
        optimized: result.finalMetrics.totalTrades 
      }
    ];
    
    comparisons.forEach(comp => {
      const improvement = ((comp.optimized - comp.original) / Math.abs(comp.original) * 100);
      const improvementStr = comp.name === '最大回撤%' ? 
        (comp.optimized < comp.original ? `↓${Math.abs(improvement).toFixed(1)}%` : `↑${improvement.toFixed(1)}%`) :
        (comp.optimized > comp.original ? `↑${improvement.toFixed(1)}%` : `↓${Math.abs(improvement).toFixed(1)}%`);
      
      console.log(
        `${comp.name.padEnd(15)} ` +
        `${comp.original.toFixed(2).padStart(12)} ` +
        `${comp.optimized.toFixed(2).padStart(12)} ` +
        `${improvementStr.padStart(12)}`
      );
    });
    
    // 显示详细报告
    console.log(optimizer.generateOptimizationReport());
    
    // 保存优化结果
    const fs = require('fs');
    const outputDir = './backtest_results/optimization';
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const resultFile = path.join(outputDir, `optimized_strategy_${timestamp}.json`);
    
    fs.writeFileSync(resultFile, JSON.stringify({
      originalStrategy: baseStrategy,
      optimizedStrategy: result.optimizedStrategy,
      originalMetrics: originalResult.metrics,
      optimizedMetrics: result.finalMetrics,
      optimizationHistory: result.optimizationHistory
    }, null, 2));
    
    console.log(`\n📁 优化结果已保存到: ${resultFile}`);
    
    return result;
    
  } catch (error) {
    console.error('❌ 参数优化失败:', error.message);
    throw error;
  }
}

/**
 * 批量优化多个策略
 */
async function optimizeMultipleStrategies() {
  console.log('🎯 批量策略参数优化');
  console.log('='.repeat(50));
  
  const dataPath = path.join(__dirname, '../data/ETHUSDT_1h.json');
  const strategies = [
    { name: '保守策略', config: strategyConfigs.conservative },
    { name: '平衡策略', config: strategyConfigs.balanced },
    { name: '质量优先', config: strategyConfigs.qualityFirst }
  ];
  
  const results = [];
  
  for (const strategy of strategies) {
    console.log(`\n🧪 优化策略: ${strategy.name}`);
    console.log('-'.repeat(30));
    
    try {
      const optimizer = new ParameterOptimizer({
        targetWinRate: 0.45,
        targetReturn: 0.05,
        maxIterations: 12
      });
      
      const result = await optimizer.optimizeStrategy(dataPath, strategy.config);
      
      results.push({
        name: strategy.name,
        originalStrategy: strategy.config,
        optimizedStrategy: result.optimizedStrategy,
        finalMetrics: result.finalMetrics,
        iterations: result.optimizationHistory.length
      });
      
      console.log(`✅ ${strategy.name} 优化完成`);
      console.log(`   最终胜率: ${(result.finalMetrics.winRate * 100).toFixed(1)}%`);
      console.log(`   最终收益: ${(result.finalMetrics.totalReturn * 100).toFixed(1)}%`);
      console.log(`   优化轮数: ${result.optimizationHistory.length}`);
      
    } catch (error) {
      console.error(`❌ ${strategy.name} 优化失败:`, error.message);
    }
  }
  
  // 生成对比报告
  console.log('\n📊 批量优化结果对比');
  console.log('='.repeat(80));
  console.log('策略名称           胜率%      收益%     盈亏比     回撤%     交易数     轮数');
  console.log('-'.repeat(80));
  
  results.forEach(result => {
    const metrics = result.finalMetrics;
    console.log(
      `${result.name.padEnd(15)} ` +
      `${(metrics.winRate * 100).toFixed(1).padStart(8)} ` +
      `${(metrics.totalReturn * 100).toFixed(1).padStart(8)} ` +
      `${metrics.profitFactor.toFixed(2).padStart(8)} ` +
      `${(metrics.maxDrawdown * 100).toFixed(1).padStart(8)} ` +
      `${metrics.totalTrades.toString().padStart(8)} ` +
      `${result.iterations.toString().padStart(8)}`
    );
  });
  
  // 找出最佳策略
  if (results.length > 0) {
    const bestStrategy = results.reduce((best, current) => {
      const bestScore = best.finalMetrics.winRate * 0.4 + best.finalMetrics.totalReturn * 0.6;
      const currentScore = current.finalMetrics.winRate * 0.4 + current.finalMetrics.totalReturn * 0.6;
      return currentScore > bestScore ? current : best;
    });
    
    console.log(`\n🏆 最佳优化策略: ${bestStrategy.name}`);
    console.log(`   胜率: ${(bestStrategy.finalMetrics.winRate * 100).toFixed(1)}%`);
    console.log(`   收益: ${(bestStrategy.finalMetrics.totalReturn * 100).toFixed(1)}%`);
    console.log(`   盈亏比: ${bestStrategy.finalMetrics.profitFactor.toFixed(2)}`);
    console.log(`   回撤: ${(bestStrategy.finalMetrics.maxDrawdown * 100).toFixed(1)}%`);
    
    // 显示最佳策略的关键参数
    console.log('\n🔧 最佳策略参数:');
    const params = bestStrategy.optimizedStrategy;
    console.log(`   min_quality_score: ${params.min_quality_score}`);
    console.log(`   rsi_strong_threshold: ${params.rsi_strong_threshold}`);
    console.log(`   rsi_medium_threshold: ${params.rsi_medium_threshold}`);
    console.log(`   risk_reward_ratio: ${params.risk_reward_ratio}`);
    console.log(`   volume_multiplier: ${params.volume_multiplier}`);
  }
  
  return results;
}

/**
 * 高级优化 - 更严格的目标
 */
async function advancedOptimization() {
  console.log('🚀 高级策略参数优化');
  console.log('='.repeat(50));
  
  const dataPath = path.join(__dirname, '../data/ETHUSDT_1h.json');
  
  const optimizer = new ParameterOptimizer({
    targetWinRate: 0.50,        // 更高的胜率目标50%
    targetReturn: 0.08,         // 更高的收益目标8%
    maxIterations: 25,          // 更多优化轮数
    improvementThreshold: 0.005 // 更严格的改进阈值
  });
  
  console.log('🎯 高级优化目标:');
  console.log('   胜率目标: 50%');
  console.log('   收益目标: 8%');
  console.log('   最大轮数: 25轮');
  console.log('   改进阈值: 0.5%\n');
  
  // 使用表现最好的基础策略
  const baseStrategy = strategyConfigs.qualityFirst;
  
  const result = await optimizer.optimizeStrategy(dataPath, baseStrategy);
  
  console.log(optimizer.generateOptimizationReport());
  
  return result;
}

// 主函数
async function main() {
  try {
    const mode = process.argv[2] || 'single';
    
    switch (mode) {
      case 'single':
        await optimizeSingleStrategy();
        break;
      case 'batch':
        await optimizeMultipleStrategies();
        break;
      case 'advanced':
        await advancedOptimization();
        break;
      default:
        console.log('使用方法:');
        console.log('  node optimize_parameters.js single    - 优化单个策略');
        console.log('  node optimize_parameters.js batch     - 批量优化多个策略');
        console.log('  node optimize_parameters.js advanced  - 高级优化(更严格目标)');
    }
    
  } catch (error) {
    console.error('❌ 程序执行失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main();
}

module.exports = {
  optimizeSingleStrategy,
  optimizeMultipleStrategies,
  advancedOptimization
};
