# ScalpAlert 部署指南

## 项目概述

ScalpAlert 是一个基于技术分析的智能交易信号系统，提供移动端H5应用程序。

### 技术栈
- **前端**: React 18 + TailwindCSS + PWA
- **后端**: Node.js + Express + SQLite
- **实时通信**: WebSocket
- **通知服务**: ntfy (https://home.917999.xyz:18030/)
- **图表库**: Recharts
- **认证**: JWT

## 系统要求

### 服务器要求
- **操作系统**: Linux (推荐 Ubuntu 20.04+) 或 Windows Server
- **Node.js**: 版本 16.x 或更高
- **内存**: 最少 1GB RAM (推荐 2GB+)
- **存储**: 最少 5GB 可用空间
- **网络**: 支持HTTPS (推荐使用SSL证书)

### 开发环境
- Node.js 16+
- npm 或 yarn
- Git

## 快速部署

### 1. 克隆项目
```bash
git clone <repository-url>
cd come-money
```

### 2. 安装依赖

#### 后端依赖
```bash
cd server
npm install
```

#### 前端依赖
```bash
cd ../client
npm install
```

### 3. 环境配置

#### 后端环境变量
在 `server` 目录创建 `.env` 文件：
```env
# 服务器配置
NODE_ENV=production
PORT=3001

# JWT密钥 (请更换为您自己的密钥)
JWT_SECRET=your-super-secret-jwt-key-here

# 数据库配置
DB_PATH=./data/scalpalert.db

# API配置
COINGECKO_API_URL=https://api.coingecko.com/api/v3

# ntfy通知服务
NTFY_SERVER=https://home.917999.xyz:18030
NTFY_TOPIC=scalpalert-notifications
```

#### 前端环境变量
在 `client` 目录创建 `.env` 文件：
```env
# API配置
REACT_APP_API_URL=http://localhost:3001/api

# WebSocket配置
REACT_APP_WS_URL=ws://localhost:3001/ws

# 应用配置
REACT_APP_NAME=ScalpAlert
REACT_APP_VERSION=1.0.0
```

### 4. 初始化数据库
```bash
cd server
npm run init-db
```

### 5. 构建前端
```bash
cd client
npm run build
```

### 6. 启动服务

#### 开发模式
```bash
# 后端
cd server
npm run dev

# 前端 (新终端)
cd client
npm start
```

#### 生产模式
```bash
cd server
npm start
```

## 生产环境部署

### 使用 PM2 (推荐)

#### 1. 安装 PM2
```bash
npm install -g pm2
```

#### 2. 创建 PM2 配置文件
在项目根目录创建 `ecosystem.config.js`：
```javascript
module.exports = {
  apps: [{
    name: 'scalpalert-api',
    script: './server/index.js',
    cwd: './',
    instances: 1,
    exec_mode: 'fork',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
```

#### 3. 启动应用
```bash
# 创建日志目录
mkdir logs

# 启动应用
pm2 start ecosystem.config.js

# 保存PM2配置
pm2 save

# 设置开机自启
pm2 startup
```

### 使用 Docker

#### 1. 创建 Dockerfile
```dockerfile
# 后端 Dockerfile (server/Dockerfile)
FROM node:16-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 3001

CMD ["npm", "start"]
```

#### 2. 创建 docker-compose.yml
```yaml
version: '3.8'

services:
  scalpalert-api:
    build: ./server
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - JWT_SECRET=your-jwt-secret
    volumes:
      - ./data:/app/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./client/build:/usr/share/nginx/html
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - scalpalert-api
    restart: unless-stopped
```

#### 3. 部署
```bash
docker-compose up -d
```

## Nginx 配置

### HTTP 配置
```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 前端静态文件
    location / {
        root /usr/share/nginx/html;
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # WebSocket代理
    location /ws {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### HTTPS 配置 (推荐)
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;

    # SSL配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # 其他配置同HTTP
    location / {
        root /usr/share/nginx/html;
        try_files $uri $uri/ /index.html;
    }

    location /api/ {
        proxy_pass http://localhost:3001;
        # ... 其他代理配置
    }

    location /ws {
        proxy_pass http://localhost:3001;
        # ... WebSocket代理配置
    }
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

## 数据库管理

### 备份数据库
```bash
# 创建备份
cp server/data/scalpalert.db server/data/scalpalert_backup_$(date +%Y%m%d_%H%M%S).db

# 定期备份脚本
#!/bin/bash
BACKUP_DIR="/path/to/backups"
DB_PATH="/path/to/server/data/scalpalert.db"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR
cp $DB_PATH $BACKUP_DIR/scalpalert_$DATE.db

# 保留最近30天的备份
find $BACKUP_DIR -name "scalpalert_*.db" -mtime +30 -delete
```

### 恢复数据库
```bash
# 停止应用
pm2 stop scalpalert-api

# 恢复数据库
cp /path/to/backup/scalpalert_backup.db server/data/scalpalert.db

# 重启应用
pm2 start scalpalert-api
```

## 监控和日志

### 日志管理
```bash
# 查看PM2日志
pm2 logs scalpalert-api

# 查看实时日志
pm2 logs scalpalert-api --lines 100 -f

# 清理日志
pm2 flush
```

### 性能监控
```bash
# PM2监控
pm2 monit

# 系统资源监控
htop
iostat -x 1
```

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   netstat -tlnp | grep :3001
   
   # 杀死进程
   kill -9 <PID>
   ```

2. **数据库权限问题**
   ```bash
   # 修改数据库文件权限
   chmod 664 server/data/scalpalert.db
   chown www-data:www-data server/data/scalpalert.db
   ```

3. **WebSocket连接失败**
   - 检查防火墙设置
   - 确认Nginx WebSocket代理配置
   - 验证SSL证书配置

4. **前端构建失败**
   ```bash
   # 清理缓存
   cd client
   rm -rf node_modules package-lock.json
   npm install
   npm run build
   ```

### 性能优化

1. **启用Gzip压缩**
2. **配置CDN**
3. **数据库索引优化**
4. **静态资源缓存**

## 安全建议

1. **更换默认JWT密钥**
2. **启用HTTPS**
3. **配置防火墙**
4. **定期更新依赖**
5. **限制API访问频率**
6. **数据库定期备份**

## 更新部署

```bash
# 拉取最新代码
git pull origin main

# 更新依赖
cd server && npm install
cd ../client && npm install

# 重新构建前端
cd client && npm run build

# 重启服务
pm2 restart scalpalert-api
```

## 支持

如有问题，请查看：
- 项目文档
- 日志文件
- GitHub Issues

---

**注意**: 请根据您的实际环境调整配置参数。
