/**
 * 策略参数优化器
 * 通过多轮回测自动优化策略参数，直到达到理想的胜率和收益率
 */

const Backtester = require('./backtester');
const DataAdapter = require('../adapters/dataAdapter');
const SignalAdapter = require('../adapters/signalAdapter');

class ParameterOptimizer {
  constructor(config = {}) {
    this.config = {
      targetWinRate: 0.45,        // 目标胜率45%
      targetReturn: 0.05,         // 目标收益率5%
      maxIterations: 20,          // 最大优化轮数
      improvementThreshold: 0.01, // 改进阈值1%
      ...config
    };
    
    this.optimizationHistory = [];
    this.bestResult = null;
  }

  /**
   * 优化策略参数
   * @param {string} dataPath - 数据文件路径
   * @param {Object} baseStrategy - 基础策略配置
   * @returns {Object} 优化后的策略配置和结果
   */
  async optimizeStrategy(dataPath, baseStrategy) {
    console.log(`🎯 开始优化策略: ${baseStrategy.name}`);
    console.log(`📊 目标: 胜率${(this.config.targetWinRate * 100).toFixed(0)}%, 收益${(this.config.targetReturn * 100).toFixed(0)}%`);
    
    let currentStrategy = { ...baseStrategy };
    let iteration = 0;
    let lastImprovement = 0;
    
    while (iteration < this.config.maxIterations) {
      iteration++;
      process.stdout.write(`\r🔄 第${iteration}轮优化...`);
      
      // 运行回测
      const result = await this.runBacktest(dataPath, currentStrategy);
      
      // 记录结果
      this.optimizationHistory.push({
        iteration,
        strategy: { ...currentStrategy },
        metrics: result.metrics,
        score: this.calculateScore(result.metrics)
      });
      
      // 检查是否是最佳结果
      if (!this.bestResult || this.calculateScore(result.metrics) > this.calculateScore(this.bestResult.metrics)) {
        this.bestResult = {
          strategy: { ...currentStrategy },
          metrics: result.metrics,
          iteration
        };
        lastImprovement = iteration;
      }
      
      // 检查是否达到目标
      if (this.isTargetAchieved(result.metrics)) {
        console.log(`\r✅ 第${iteration}轮达到目标! 胜率${(result.metrics.winRate * 100).toFixed(1)}%, 收益${(result.metrics.totalReturn * 100).toFixed(1)}%`);
        break;
      }
      
      // 检查是否停滞
      if (iteration - lastImprovement > 5) {
        console.log(`\r⏹️ 第${iteration}轮优化停滞，使用最佳结果`);
        break;
      }
      
      // 调整策略参数
      currentStrategy = this.adjustParameters(currentStrategy, result.metrics);
    }
    
    console.log(`\n🏆 优化完成! 最佳结果来自第${this.bestResult.iteration}轮`);
    return {
      optimizedStrategy: this.bestResult.strategy,
      finalMetrics: this.bestResult.metrics,
      optimizationHistory: this.optimizationHistory
    };
  }

  /**
   * 运行单次回测
   * @param {string} dataPath - 数据路径
   * @param {Object} strategy - 策略配置
   * @returns {Object} 回测结果
   */
  async runBacktest(dataPath, strategy) {
    const backtester = new Backtester({
      initialCapital: 10000,
      tradingFee: 0.001
    });
    
    backtester.loadData(dataPath);
    return await backtester.runBacktest(strategy);
  }

  /**
   * 计算策略评分
   * @param {Object} metrics - 回测指标
   * @returns {number} 综合评分
   */
  calculateScore(metrics) {
    const winRateScore = Math.min(metrics.winRate / this.config.targetWinRate, 1.5) * 40;
    const returnScore = Math.max(metrics.totalReturn / this.config.targetReturn, -2) * 30;
    const profitFactorScore = Math.min(metrics.profitFactor / 1.5, 2) * 20;
    const drawdownPenalty = Math.max(0, (metrics.maxDrawdown - 20) * -0.5);
    
    return winRateScore + returnScore + profitFactorScore + drawdownPenalty;
  }

  /**
   * 检查是否达到目标
   * @param {Object} metrics - 回测指标
   * @returns {boolean} 是否达到目标
   */
  isTargetAchieved(metrics) {
    return metrics.winRate >= this.config.targetWinRate && 
           metrics.totalReturn >= this.config.targetReturn &&
           metrics.profitFactor >= 1.2;
  }

  /**
   * 调整策略参数
   * @param {Object} strategy - 当前策略
   * @param {Object} metrics - 回测指标
   * @returns {Object} 调整后的策略
   */
  adjustParameters(strategy, metrics) {
    const newStrategy = { ...strategy };
    
    // 胜率过低 - 提高信号质量
    if (metrics.winRate < this.config.targetWinRate) {
      const deficit = this.config.targetWinRate - metrics.winRate;
      
      if (deficit > 0.1) {
        // 胜率严重不足，大幅调整
        newStrategy.min_quality_score = Math.min(newStrategy.min_quality_score + 15, 85);
        newStrategy.rsi_strong_threshold = Math.max(newStrategy.rsi_strong_threshold - 3, 15);
        newStrategy.volume_multiplier = Math.min(newStrategy.volume_multiplier + 0.3, 3.0);
      } else {
        // 胜率略低，小幅调整
        newStrategy.min_quality_score = Math.min(newStrategy.min_quality_score + 8, 80);
        newStrategy.rsi_strong_threshold = Math.max(newStrategy.rsi_strong_threshold - 1, 18);
      }
    }
    
    // 收益率过低 - 调整风险回报比
    if (metrics.totalReturn < this.config.targetReturn) {
      const deficit = this.config.targetReturn - metrics.totalReturn;
      
      if (deficit > 0.1) {
        // 收益严重不足
        newStrategy.risk_reward_ratio = Math.min(newStrategy.risk_reward_ratio + 0.5, 4.0);
        newStrategy.atr_multiplier = Math.min(newStrategy.atr_multiplier + 0.3, 3.0);
      } else {
        // 收益略低
        newStrategy.risk_reward_ratio = Math.min(newStrategy.risk_reward_ratio + 0.2, 3.5);
      }
    }
    
    // 盈亏比过低 - 调整止盈止损
    if (metrics.profitFactor < 1.2) {
      newStrategy.risk_reward_ratio = Math.min(newStrategy.risk_reward_ratio + 0.3, 4.0);
      newStrategy.atr_multiplier = Math.min(newStrategy.atr_multiplier + 0.2, 2.5);
    }
    
    // 回撤过大 - 降低风险
    if (metrics.maxDrawdown > 20) {
      newStrategy.risk_strong = Math.max(newStrategy.risk_strong * 0.8, 0.002);
      newStrategy.risk_medium = Math.max(newStrategy.risk_medium * 0.8, 0.001);
      newStrategy.risk_weak = Math.max(newStrategy.risk_weak * 0.8, 0.0005);
    }
    
    // 交易过少 - 适度放宽条件
    if (metrics.totalTrades < 30) {
      newStrategy.rsi_medium_threshold = Math.min(newStrategy.rsi_medium_threshold + 2, 45);
      newStrategy.min_quality_score = Math.max(newStrategy.min_quality_score - 5, 30);
    }
    
    // 交易过多 - 收紧条件
    if (metrics.totalTrades > 200) {
      newStrategy.min_quality_score = Math.min(newStrategy.min_quality_score + 10, 85);
      newStrategy.skip_weak_signals = true;
    }
    
    // 确保参数在合理范围内
    newStrategy.min_quality_score = Math.max(25, Math.min(85, newStrategy.min_quality_score));
    newStrategy.rsi_strong_threshold = Math.max(15, Math.min(35, newStrategy.rsi_strong_threshold));
    newStrategy.rsi_medium_threshold = Math.max(25, Math.min(45, newStrategy.rsi_medium_threshold));
    newStrategy.risk_reward_ratio = Math.max(1.5, Math.min(4.0, newStrategy.risk_reward_ratio));
    newStrategy.volume_multiplier = Math.max(1.0, Math.min(3.0, newStrategy.volume_multiplier));
    
    return newStrategy;
  }

  /**
   * 生成优化报告
   * @returns {string} 优化报告
   */
  generateOptimizationReport() {
    if (!this.bestResult) {
      return '❌ 没有优化结果';
    }
    
    let report = `\n📊 策略参数优化报告\n`;
    report += `${'='.repeat(60)}\n\n`;
    
    report += `🎯 优化目标:\n`;
    report += `  胜率目标: ${(this.config.targetWinRate * 100).toFixed(0)}%\n`;
    report += `  收益目标: ${(this.config.targetReturn * 100).toFixed(0)}%\n\n`;
    
    report += `🏆 最佳结果 (第${this.bestResult.iteration}轮):\n`;
    const metrics = this.bestResult.metrics;
    report += `  胜率: ${(metrics.winRate * 100).toFixed(2)}%\n`;
    report += `  总收益: ${(metrics.totalReturn * 100).toFixed(2)}%\n`;
    report += `  盈亏比: ${metrics.profitFactor.toFixed(2)}\n`;
    report += `  最大回撤: ${(metrics.maxDrawdown * 100).toFixed(2)}%\n`;
    report += `  总交易数: ${metrics.totalTrades}\n`;
    report += `  综合评分: ${this.calculateScore(metrics).toFixed(1)}\n\n`;
    
    report += `🔧 优化后参数:\n`;
    const strategy = this.bestResult.strategy;
    report += `  min_quality_score: ${strategy.min_quality_score}\n`;
    report += `  rsi_strong_threshold: ${strategy.rsi_strong_threshold}\n`;
    report += `  rsi_medium_threshold: ${strategy.rsi_medium_threshold}\n`;
    report += `  risk_reward_ratio: ${strategy.risk_reward_ratio}\n`;
    report += `  volume_multiplier: ${strategy.volume_multiplier}\n`;
    report += `  risk_strong: ${strategy.risk_strong}\n\n`;
    
    // 优化历程
    report += `📈 优化历程:\n`;
    report += `轮次  胜率%   收益%   盈亏比  评分\n`;
    report += `-`.repeat(35) + `\n`;
    
    this.optimizationHistory.slice(-10).forEach(record => {
      report += `${record.iteration.toString().padStart(3)}   `;
      report += `${(record.metrics.winRate * 100).toFixed(1).padStart(5)}  `;
      report += `${(record.metrics.totalReturn * 100).toFixed(1).padStart(6)}  `;
      report += `${record.metrics.profitFactor.toFixed(2).padStart(6)}  `;
      report += `${record.score.toFixed(1).padStart(5)}\n`;
    });
    
    // 达成状态
    report += `\n✅ 目标达成情况:\n`;
    report += `  胜率: ${metrics.winRate >= this.config.targetWinRate ? '✅' : '❌'} `;
    report += `${(metrics.winRate * 100).toFixed(1)}% / ${(this.config.targetWinRate * 100).toFixed(0)}%\n`;
    report += `  收益: ${metrics.totalReturn >= this.config.targetReturn ? '✅' : '❌'} `;
    report += `${(metrics.totalReturn * 100).toFixed(1)}% / ${(this.config.targetReturn * 100).toFixed(0)}%\n`;
    report += `  盈亏比: ${metrics.profitFactor >= 1.2 ? '✅' : '❌'} `;
    report += `${metrics.profitFactor.toFixed(2)} / 1.20\n`;
    
    return report;
  }
}

module.exports = ParameterOptimizer;
