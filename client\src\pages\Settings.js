import React, { useState } from 'react';
import { Settings as SettingsIcon, User, DollarSign, Shield, LogOut, Target } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { formatCurrency, formatPercentage } from '../utils/api';
import SignalConfig from '../components/Settings/SignalConfig';
import toast from 'react-hot-toast';

const Settings = () => {
  const { user, logout, updateSettings } = useAuth();
  const [loading, setLoading] = useState(false);
  const [showCapitalModal, setShowCapitalModal] = useState(false);
  const [showRiskModal, setShowRiskModal] = useState(false);
  const [activeTab, setActiveTab] = useState('account'); // 'account', 'signal', 'risk'

  const handleLogout = () => {
    logout();
  };

  const tabs = [
    { id: 'account', name: '账户设置', icon: User },
    { id: 'signal', name: '信号配置', icon: Target },
    { id: 'risk', name: '风险控制', icon: Shield }
  ];

  return (
    <div className="mobile-content">
      {/* 用户信息 */}
      <div className="card mb-6">
        <div className="flex items-center space-x-4 mb-4">
          <div className="w-16 h-16 bg-gradient-to-r from-primary-600 to-primary-800 rounded-full flex items-center justify-center">
            <span className="text-white font-bold text-xl">
              {user?.username?.charAt(0).toUpperCase()}
            </span>
          </div>
          <div>
            <h2 className="text-lg font-semibold text-gray-900">
              {user?.username}
            </h2>
            <p className="text-sm text-gray-600">
              ScalpAlert 用户
            </p>
          </div>
        </div>
      </div>

      {/* 标签导航 */}
      <div className="mb-6">
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex-1 flex items-center justify-center space-x-2 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <Icon size={16} />
                <span>{tab.name}</span>
              </button>
            );
          })}
        </div>
      </div>

      {/* 标签内容 */}
      <div className="space-y-6">
        {activeTab === 'account' && (
          <div className="space-y-4">
            {/* 账户信息 */}
            <div className="card">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                    <User className="text-primary-600" size={20} />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">个人信息</h3>
                    <p className="text-sm text-gray-600">用户名: {user?.username}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'signal' && (
          <SignalConfig />
        )}

        {activeTab === 'risk' && (
          <div className="space-y-4">
            {/* 资金设置 */}
            <div className="card">
              <button
                onClick={() => setShowCapitalModal(true)}
                className="w-full flex items-center justify-between text-left"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-success-100 rounded-lg flex items-center justify-center">
                    <DollarSign className="text-success-600" size={20} />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">资金管理</h3>
                    <p className="text-sm text-gray-600">
                      当前本金: {formatCurrency(user?.capital)}
                    </p>
                  </div>
                </div>
                <span className="text-gray-400">›</span>
              </button>
            </div>

            {/* 风险控制 */}
            <div className="card">
              <button
                onClick={() => setShowRiskModal(true)}
                className="w-full flex items-center justify-between text-left"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-warning-100 rounded-lg flex items-center justify-center">
                    <Shield className="text-warning-600" size={20} />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">风险控制</h3>
                    <p className="text-sm text-gray-600">
                      风险比例: {formatPercentage(user?.risk_percentage)}
                    </p>
                  </div>
                </div>
                <span className="text-gray-400">›</span>
              </button>
            </div>
          </div>
        )}

        {/* 退出登录 */}
        <div className="card">
          <button
            onClick={handleLogout}
            disabled={loading}
            className="w-full flex items-center justify-between text-left"
          >
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-danger-100 rounded-lg flex items-center justify-center">
                <LogOut className="text-danger-600" size={20} />
              </div>
              <div>
                <h3 className="font-medium text-danger-600">退出登录</h3>
                <p className="text-sm text-gray-600">安全退出当前账户</p>
              </div>
            </div>
          </button>
        </div>
      </div>

      {/* 资金管理模态框 */}
      {showCapitalModal && (
        <CapitalModal
          user={user}
          onClose={() => setShowCapitalModal(false)}
          onSuccess={(data) => {
            updateSettings(data);
            setShowCapitalModal(false);
          }}
        />
      )}

      {/* 风险控制模态框 */}
      {showRiskModal && (
        <RiskModal
          user={user}
          onClose={() => setShowRiskModal(false)}
          onSuccess={(data) => {
            updateSettings(data);
            setShowRiskModal(false);
          }}
        />
      )}
    </div>
  );
};

// 资金管理模态框
const CapitalModal = ({ user, onClose, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    capital: user?.capital || 5000,
    lossLimit: user?.lossLimit || 3000,
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: parseFloat(value) || 0,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (formData.capital <= 0) {
      toast.error('本金必须大于0');
      return;
    }

    if (formData.lossLimit <= 0) {
      toast.error('亏损限制必须大于0');
      return;
    }

    try {
      setLoading(true);
      await onSuccess(formData);
      toast.success('资金设置更新成功');
    } catch (error) {
      console.error('更新资金设置失败:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl max-w-md w-full">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">资金管理</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* 当前本金 */}
            <div>
              <label className="label">当前本金 ($)</label>
              <input
                type="number"
                name="capital"
                value={formData.capital}
                onChange={handleChange}
                className="input"
                placeholder="请输入当前本金"
                step="100"
                min="100"
                disabled={loading}
              />
              <p className="text-xs text-gray-500 mt-1">
                请根据您的实际资金情况设置
              </p>
            </div>

            {/* 亏损限制 */}
            <div>
              <label className="label">亏损限制 ($)</label>
              <input
                type="number"
                name="lossLimit"
                value={formData.lossLimit}
                onChange={handleChange}
                className="input"
                placeholder="请输入亏损限制"
                step="100"
                min="100"
                disabled={loading}
              />
              <p className="text-xs text-gray-500 mt-1">
                达到此亏损额度时系统将发出警告
              </p>
            </div>

            {/* 提交按钮 */}
            <div className="flex space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="btn-outline flex-1"
                disabled={loading}
              >
                取消
              </button>
              <button
                type="submit"
                className="btn-primary flex-1"
                disabled={loading}
              >
                {loading ? (
                  <div className="loading-spinner"></div>
                ) : (
                  '保存'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

// 风险控制模态框
const RiskModal = ({ user, onClose, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    riskPercentage: (user?.riskPercentage || 0.01) * 100, // 转换为百分比显示
    maxDailyTrades: user?.maxDailyTrades || 5,
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: parseFloat(value) || 0,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (formData.riskPercentage <= 0 || formData.riskPercentage > 10) {
      toast.error('风险百分比应在0.1%-10%之间');
      return;
    }

    if (formData.maxDailyTrades <= 0 || formData.maxDailyTrades > 20) {
      toast.error('每日交易次数应在1-20次之间');
      return;
    }

    try {
      setLoading(true);
      await onSuccess({
        riskPercentage: formData.riskPercentage / 100, // 转换回小数
        maxDailyTrades: formData.maxDailyTrades,
      });
      toast.success('风险设置更新成功');
    } catch (error) {
      console.error('更新风险设置失败:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl max-w-md w-full">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">风险控制</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* 风险百分比 */}
            <div>
              <label className="label">单笔风险百分比 (%)</label>
              <input
                type="number"
                name="riskPercentage"
                value={formData.riskPercentage}
                onChange={handleChange}
                className="input"
                placeholder="1"
                step="0.1"
                min="0.1"
                max="10"
                disabled={loading}
              />
              <p className="text-xs text-gray-500 mt-1">
                每笔交易的最大风险占本金的百分比
              </p>
            </div>

            {/* 每日交易次数限制 */}
            <div>
              <label className="label">每日最大交易次数</label>
              <input
                type="number"
                name="maxDailyTrades"
                value={formData.maxDailyTrades}
                onChange={handleChange}
                className="input"
                placeholder="5"
                min="1"
                max="20"
                disabled={loading}
              />
              <p className="text-xs text-gray-500 mt-1">
                限制每日最大交易次数，避免过度交易
              </p>
            </div>

            {/* 提交按钮 */}
            <div className="flex space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="btn-outline flex-1"
                disabled={loading}
              >
                取消
              </button>
              <button
                type="submit"
                className="btn-primary flex-1"
                disabled={loading}
              >
                {loading ? (
                  <div className="loading-spinner"></div>
                ) : (
                  '保存'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Settings;
