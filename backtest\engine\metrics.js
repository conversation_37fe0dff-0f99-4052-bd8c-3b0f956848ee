/**
 * 回测指标计算模块
 * 计算各种回测性能指标
 */

class MetricsCalculator {
  constructor() {
    this.riskFreeRate = 0; // 无风险利率，加密货币通常设为0
  }

  /**
   * 计算完整的回测指标
   * @param {Portfolio} portfolio - 投资组合对象
   * @param {number} backtestDays - 回测天数
   * @returns {Object} 完整的指标报告
   */
  calculateAllMetrics(portfolio, backtestDays) {
    const trades = portfolio.trades;
    const equity = portfolio.equity;
    const initialCapital = portfolio.initialCapital;
    const finalCapital = portfolio.getTotalEquity();

    // 基础指标
    const totalReturn = (finalCapital - initialCapital) / initialCapital;
    const annualizedReturn = this.calculateAnnualizedReturn(totalReturn, backtestDays);
    
    // 交易统计
    const tradeStats = portfolio.getTradeStats();
    
    // 风险指标
    const sharpeRatio = this.calculateSharpeRatio(equity, backtestDays);
    const maxDrawdown = portfolio.maxDrawdown;
    const calmarRatio = annualizedReturn / (maxDrawdown || 0.001);
    
    // 其他指标
    const volatility = this.calculateVolatility(equity, backtestDays);
    const sortinoRatio = this.calculateSortinoRatio(equity, backtestDays);
    
    return {
      // 收益指标
      totalReturn: totalReturn * 100,
      annualizedReturn: annualizedReturn * 100,
      
      // 风险指标
      maxDrawdown: maxDrawdown * 100,
      volatility: volatility * 100,
      sharpeRatio,
      sortinoRatio,
      calmarRatio,
      
      // 交易指标
      ...tradeStats,
      
      // 其他指标
      totalFees: portfolio.totalFees,
      tradingFrequency: trades.length / backtestDays,
      avgHoldingTime: this.calculateAvgHoldingTime(trades),
      
      // 资金曲线
      initialCapital,
      finalCapital,
      peakCapital: portfolio.peakCapital,
      
      // 时间信息
      backtestDays,
      backtestPeriod: this.getBacktestPeriod(equity)
    };
  }

  /**
   * 计算年化收益率
   * @param {number} totalReturn - 总收益率
   * @param {number} days - 天数
   * @returns {number} 年化收益率
   */
  calculateAnnualizedReturn(totalReturn, days) {
    if (days <= 0) return 0;
    return Math.pow(1 + totalReturn, 365 / days) - 1;
  }

  /**
   * 计算夏普比率
   * @param {Array} equity - 权益曲线
   * @param {number} days - 回测天数
   * @returns {number} 夏普比率
   */
  calculateSharpeRatio(equity, days) {
    if (equity.length < 2) return 0;
    
    // 计算日收益率
    const dailyReturns = this.calculateDailyReturns(equity);
    
    if (dailyReturns.length === 0) return 0;
    
    // 计算平均日收益率和标准差
    const avgDailyReturn = dailyReturns.reduce((a, b) => a + b, 0) / dailyReturns.length;
    const variance = dailyReturns.reduce((sum, ret) => sum + Math.pow(ret - avgDailyReturn, 2), 0) / dailyReturns.length;
    const dailyStd = Math.sqrt(variance);
    
    if (dailyStd === 0) return 0;
    
    // 年化夏普比率
    const annualizedReturn = avgDailyReturn * 365;
    const annualizedStd = dailyStd * Math.sqrt(365);
    
    return (annualizedReturn - this.riskFreeRate) / annualizedStd;
  }

  /**
   * 计算索提诺比率
   * @param {Array} equity - 权益曲线
   * @param {number} days - 回测天数
   * @returns {number} 索提诺比率
   */
  calculateSortinoRatio(equity, days) {
    if (equity.length < 2) return 0;
    
    const dailyReturns = this.calculateDailyReturns(equity);
    
    if (dailyReturns.length === 0) return 0;
    
    // 只考虑负收益的标准差
    const negativeReturns = dailyReturns.filter(ret => ret < 0);
    
    if (negativeReturns.length === 0) return Infinity;
    
    const avgDailyReturn = dailyReturns.reduce((a, b) => a + b, 0) / dailyReturns.length;
    const downwardVariance = negativeReturns.reduce((sum, ret) => sum + Math.pow(ret, 2), 0) / dailyReturns.length;
    const downwardStd = Math.sqrt(downwardVariance);
    
    if (downwardStd === 0) return 0;
    
    const annualizedReturn = avgDailyReturn * 365;
    const annualizedDownwardStd = downwardStd * Math.sqrt(365);
    
    return (annualizedReturn - this.riskFreeRate) / annualizedDownwardStd;
  }

  /**
   * 计算波动率
   * @param {Array} equity - 权益曲线
   * @param {number} days - 回测天数
   * @returns {number} 年化波动率
   */
  calculateVolatility(equity, days) {
    if (equity.length < 2) return 0;
    
    const dailyReturns = this.calculateDailyReturns(equity);
    
    if (dailyReturns.length === 0) return 0;
    
    const avgReturn = dailyReturns.reduce((a, b) => a + b, 0) / dailyReturns.length;
    const variance = dailyReturns.reduce((sum, ret) => sum + Math.pow(ret - avgReturn, 2), 0) / dailyReturns.length;
    const dailyStd = Math.sqrt(variance);
    
    return dailyStd * Math.sqrt(365); // 年化波动率
  }

  /**
   * 计算日收益率
   * @param {Array} equity - 权益曲线
   * @returns {Array} 日收益率数组
   */
  calculateDailyReturns(equity) {
    if (equity.length < 2) return [];
    
    const returns = [];
    for (let i = 1; i < equity.length; i++) {
      const prevEquity = equity[i - 1].totalEquity;
      const currentEquity = equity[i].totalEquity;
      
      if (prevEquity > 0) {
        returns.push((currentEquity - prevEquity) / prevEquity);
      }
    }
    
    return returns;
  }

  /**
   * 计算平均持仓时间
   * @param {Array} trades - 交易记录
   * @returns {number} 平均持仓时间（小时）
   */
  calculateAvgHoldingTime(trades) {
    if (trades.length === 0) return 0;
    
    const totalHoldingTime = trades.reduce((sum, trade) => sum + trade.holdingTime, 0);
    return totalHoldingTime / trades.length / (1000 * 60 * 60); // 转换为小时
  }

  /**
   * 获取回测周期信息
   * @param {Array} equity - 权益曲线
   * @returns {Object} 回测周期信息
   */
  getBacktestPeriod(equity) {
    if (equity.length === 0) return null;
    
    return {
      start: new Date(equity[0].timestamp),
      end: new Date(equity[equity.length - 1].timestamp),
      totalPoints: equity.length
    };
  }

  /**
   * 生成详细报告
   * @param {Object} metrics - 指标对象
   * @returns {string} 格式化的报告
   */
  generateReport(metrics) {
    return `
📊 回测报告
${'='.repeat(50)}

📈 收益指标:
  总收益率: ${metrics.totalReturn.toFixed(2)}%
  年化收益率: ${metrics.annualizedReturn.toFixed(2)}%
  初始资金: $${metrics.initialCapital.toLocaleString()}
  最终资金: $${metrics.finalCapital.toLocaleString()}
  最高资金: $${metrics.peakCapital.toLocaleString()}

⚠️ 风险指标:
  最大回撤: ${metrics.maxDrawdown.toFixed(2)}%
  年化波动率: ${metrics.volatility.toFixed(2)}%
  夏普比率: ${metrics.sharpeRatio.toFixed(3)}
  索提诺比率: ${metrics.sortinoRatio.toFixed(3)}
  卡玛比率: ${metrics.calmarRatio.toFixed(3)}

📋 交易统计:
  总交易次数: ${metrics.totalTrades}
  盈利交易: ${metrics.winningTrades}
  亏损交易: ${metrics.losingTrades}
  胜率: ${(metrics.winRate * 100).toFixed(2)}%
  平均盈利: $${metrics.avgWin.toFixed(2)}
  平均亏损: $${metrics.avgLoss.toFixed(2)}
  盈亏比: ${metrics.profitFactor.toFixed(2)}
  最大盈利: $${metrics.maxWin.toFixed(2)}
  最大亏损: $${metrics.maxLoss.toFixed(2)}

💰 成本分析:
  总手续费: $${metrics.totalFees.toFixed(2)}
  交易频率: ${metrics.tradingFrequency.toFixed(2)} 次/天
  平均持仓时间: ${metrics.avgHoldingTime.toFixed(2)} 小时

📅 回测周期:
  回测天数: ${metrics.backtestDays} 天
  开始时间: ${metrics.backtestPeriod?.start?.toLocaleDateString()}
  结束时间: ${metrics.backtestPeriod?.end?.toLocaleDateString()}

${'='.repeat(50)}
`;
  }

  /**
   * 计算月度收益
   * @param {Array} equity - 权益曲线
   * @returns {Array} 月度收益数组
   */
  calculateMonthlyReturns(equity) {
    if (equity.length === 0) return [];
    
    const monthlyData = {};
    
    equity.forEach(point => {
      const date = new Date(point.timestamp);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      
      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = {
          start: point.totalEquity,
          end: point.totalEquity,
          month: monthKey
        };
      } else {
        monthlyData[monthKey].end = point.totalEquity;
      }
    });
    
    return Object.values(monthlyData).map(month => ({
      month: month.month,
      return: (month.end - month.start) / month.start
    }));
  }
}

module.exports = MetricsCalculator;
