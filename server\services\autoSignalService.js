const database = require('../config/database');
const signalService = require('./signalService');
const signalGenerator = require('./signalGenerator');
const { detectConfigType } = require('../config/signalPresets');
const notificationService = require('./notificationService');

class AutoSignalService {
  constructor() {
    this.isRunning = false;
    this.intervalId = null;
    this.baseCheckInterval = 10 * 60 * 1000;  // 基础10分钟
    this.highFreqInterval = 5 * 60 * 1000;    // 高频5分钟
    this.lowFreqInterval = 15 * 60 * 1000;    // 低频15分钟
    this.currentInterval = this.baseCheckInterval;
    this.supportedAssets = ['bitcoin', 'ethereum', 'binancecoin', 'solana'];
    this.lastCheckTime = null;
    this.tempHighFreqMode = false;
  }

  // 启动自动信号检测
  start() {
    if (this.isRunning) {
      console.log('⚠️ 自动信号检测已在运行中');
      return;
    }

    console.log('🚀 启动自动信号检测服务');
    console.log(`📅 基础检测间隔: ${this.baseCheckInterval / 1000 / 60} 分钟`);
    console.log(`⚡ 高频检测间隔: ${this.highFreqInterval / 1000 / 60} 分钟`);
    console.log(`💰 支持币种: ${this.supportedAssets.join(', ')}`);
    
    this.isRunning = true;
    this.lastCheckTime = new Date();
    
    // 立即执行一次检测
    this.checkAllSignals();
    
    // 设置定时检测
    this.intervalId = setInterval(() => {
      this.checkAllSignals();
    }, this.currentInterval);
  }

  // 停止自动信号检测
  stop() {
    if (!this.isRunning) {
      console.log('⚠️ 自动信号检测未在运行');
      return;
    }

    console.log('🛑 停止自动信号检测服务');
    this.isRunning = false;
    
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  // 检查所有币种的信号
  async checkAllSignals() {
    const startTime = new Date();
    console.log(`\n🔍 开始信号检测 - ${startTime.toLocaleString()}`);

    // 检测K线完成度并调整检测频率
    const candleProgress = this.getCurrentCandleProgress();

    // 评估市场活跃度
    const marketActivity = await this.assessMarketActivity();

    // 综合调整检测频率
    this.adjustCheckFrequency(candleProgress, marketActivity);

    console.log('=' .repeat(50));

    try {
      // 获取所有活跃用户
      const users = await database.query(
        'SELECT id, username, capital, risk_percentage, max_daily_trades FROM users'
      );

      console.log(`👥 检测到 ${users.length} 个用户`);

      for (const user of users) {
        console.log(`\n👤 检测用户: ${user.username} (ID: ${user.id})`);
        await this.checkUserSignals(user);
      }

      const endTime = new Date();
      const duration = endTime - startTime;
      console.log(`\n✅ 信号检测完成 - 耗时: ${duration}ms`);
      console.log('=' .repeat(50));
      
      this.lastCheckTime = endTime;

    } catch (error) {
      console.error('❌ 自动信号检测失败:', error);
    }
  }

  // 检查单个用户的信号
  async checkUserSignals(user) {
    try {
      // 检查今日交易次数
      const today = new Date().toISOString().split('T')[0];
      const todayTrades = await database.get(
        'SELECT COUNT(*) as count FROM trades WHERE user_id = ? AND DATE(buy_time) = ?',
        [user.id, today]
      );

      if (todayTrades.count >= user.max_daily_trades) {
        console.log(`  ⏭️ 用户今日交易次数已达上限 (${todayTrades.count}/${user.max_daily_trades})`);
        return;
      }

      // 获取用户信号配置
      const userConfig = await database.get(
        'SELECT * FROM signal_config WHERE user_id = ?',
        [user.id]
      );

      const config = userConfig || {
        rsi_strong_threshold: 30,
        rsi_medium_threshold: 40,
        risk_strong: 0.01,           // 更新为新的风险参数
        risk_medium: 0.008,          // 更新为新的风险参数
        risk_weak: 0.005,            // 更新为新的风险参数
        atr_multiplier: 1.5,         // 新增ATR倍数
        risk_reward_ratio: 2.0,      // 新增风险回报比
        volume_multiplier: 1.5,
        enable_volume_filter: 1,
        enable_m_head_filter: 0,     // 关闭M头过滤，增加信号机会
        enable_w_bottom_bonus: 1,
        skip_weak_signals: 0,
        min_quality_score: 40        // 降低最低质量要求
      };

      // 检测配置类型并显示
      const configType = this.detectConfigType(config);
      console.log(`  ⚙️ 使用配置: ${configType}`);
      console.log(`  📊 RSI阈值: 强=${config.rsi_strong_threshold}, 中=${config.rsi_medium_threshold}`);
      console.log(`  💼 风险设置: 强=${config.risk_strong}, 中=${config.risk_medium}, 弱=${config.risk_weak}`);
      console.log(`  🔍 质量阈值: ${config.min_quality_score}分`);

      console.log(`  ⚙️ 配置: RSI阈值(${config.rsi_strong_threshold}/${config.rsi_medium_threshold}), 最低评分(${config.min_quality_score})`);

      // 检查每个支持的币种
      for (const asset of this.supportedAssets) {
        console.log(`  🪙 检测币种: ${asset.toUpperCase()}`);
        await this.checkAssetSignal(user, asset, config);
      }

    } catch (error) {
      console.error(`  ❌ 用户 ${user.username} 信号检测失败:`, error.message);
    }
  }

  // 检查单个币种的信号
  async checkAssetSignal(user, asset, config) {
    try {
      // 信号强度分级冷却机制
      const lastSignalInfo = await database.get(
        `SELECT signal_type, signal_strength, timestamp FROM signals
         WHERE user_id = ? AND asset = ? AND is_active = 1
         ORDER BY timestamp DESC LIMIT 1`,
        [user.id, asset]
      );

      // 首先获取历史数据（必须在最前面，避免后续计算崩溃）
      console.log(`    📊 获取 ${asset} 历史数据...`);
      const historicalData = await signalService.getHistoricalData(asset, 50);
      if (!historicalData || historicalData.length < 50) {
        console.log(`    ❌ ${asset} 历史数据不足，跳过检测`);
        return;
      }

      // 使用统一的信号生成器
      console.log(`    🎯 使用统一信号生成器检测 ${asset}...`);
      let generatedSignal;
      try {
        generatedSignal = await signalGenerator.generateSignal(asset, config);
      } catch (error) {
        console.log(`    ❌ ${asset} 信号生成失败: ${error.message}`);
        return;
      }
      const currentSignalType = generatedSignal.signal_type;

      if (lastSignalInfo && lastSignalInfo.signal_type === currentSignalType && currentSignalType !== 'hold') {
        // 根据信号强度确定冷却时间
        const cooldownStrategy = this.getDetectionStrategy(lastSignalInfo.signal_strength);
        const timeSinceLastSignal = Date.now() - new Date(lastSignalInfo.timestamp).getTime();

        if (timeSinceLastSignal < cooldownStrategy.cooldown) {
          const remainingMinutes = Math.ceil((cooldownStrategy.cooldown - timeSinceLastSignal) / 60000);
          console.log(`    ⏰ ${asset} ${lastSignalInfo.signal_strength}信号冷却中，还需${remainingMinutes}分钟`);
          return;
        }
      }

      // 价格变化过滤：根据上次信号强度动态调整阈值
      if (lastSignalInfo && lastSignalInfo.entry_price) {
        const currentPrice = historicalData[historicalData.length - 1][1];
        const priceChange = Math.abs((currentPrice - lastSignalInfo.entry_price) / lastSignalInfo.entry_price);

        // 根据信号强度获取最小价格变化要求
        const strategy = this.getDetectionStrategy(lastSignalInfo.signal_strength);

        if (priceChange < strategy.minPriceChange) {
          console.log(`    📊 ${asset} 价格变化不足(${(priceChange * 100).toFixed(2)}% < ${(strategy.minPriceChange * 100).toFixed(1)}%)，跳过检测`);
          return;
        }
      }
      const currentPrice = generatedSignal.price;
      const priceData = await signalService.getPriceData(asset);

      console.log(`    💰 当前价格: $${currentPrice}`);
      console.log(`    📈 24h变化: ${priceData.usd_24h_change.toFixed(2)}%`);

      // 技术指标已由统一信号生成器计算完成
      const rsi = generatedSignal.rsi;
      const emaShort = generatedSignal.ema_short;
      const emaLong = generatedSignal.ema_long;
      const trend4h = generatedSignal.trend_4h;
      const pattern = generatedSignal.pattern;
      const volumeRatio = generatedSignal.volume_ratio;

      // MACD和ATR指标已由统一信号生成器计算完成
      const macd = generatedSignal.macd;
      const atr = generatedSignal.atr;
      const mtfAnalysis = generatedSignal.mtf_analysis;

      console.log(`    📊 RSI: ${rsi}, EMA: ${emaShort}/${emaLong}, 趋势: ${trend4h}`);
      console.log(`    📈 形态: ${pattern}, 成交量比率: ${volumeRatio.toFixed(2)}`);
      console.log(`    📊 MACD: ${macd.macd.toFixed(4)}, 信号线: ${macd.signal.toFixed(4)}, 柱状线: ${macd.histogram.toFixed(4)}`);
      console.log(`    📏 ATR: ${atr.toFixed(4)} (动态止损参考)`);
      console.log(`    📊 日线: ${mtfAnalysis.daily.trend}, 4H: ${mtfAnalysis.h4.trend}, 1H: ${mtfAnalysis.h1.trend}`);
      console.log(`    🎯 综合建议: ${mtfAnalysis.recommendation}`);

      // 信号分析已由统一信号生成器完成
      const analysisResult = {
        quality_score: generatedSignal.quality_score,
        strength: generatedSignal.signal_strength,
        suggested_position: generatedSignal.suggested_position,
        analysis_details: generatedSignal.analysis_details,
        description: generatedSignal.description
      };
      console.log(`    🎯 信号质量评分: ${analysisResult.quality_score}/100`);
      console.log(`    💪 信号强度: ${analysisResult.strength}`);
      console.log(`    💼 动态仓位: ${(analysisResult.suggested_position * 100).toFixed(2)}% (基于ATR: ${atr.toFixed(4)})`);

      // 信号类型判断已由统一信号生成器完成
      const signalType = generatedSignal.signal_type;
      const signalReason = generatedSignal.signal_reason;

      // 信号类型判断已由统一信号生成器完成

      const signalTypeText = signalType === 'buy' ? '买入' : signalType === 'sell' ? '卖出' : signalType === 'hold' ? '持有' : '无信号';
      console.log(`    📊 信号类型: ${signalTypeText} (${signalReason})`);

      // 检查是否应该跳过信号
      const shouldSkip = generatedSignal.should_skip;
      const reason = generatedSignal.skip_reason;

      if (shouldSkip) {
        console.log(`    ⏭️ 信号被跳过: ${reason}，不记录到数据库`);
        return; // 跳过的信号不保存
      }

      console.log(`    ✅ 生成有效信号: ${analysisResult.strength}强度`);
      console.log(`    📝 保存有效 ${analysisResult.strength} 信号!`);
      await this.createSignal(user, asset, generatedSignal, analysisResult, signalType, currentPrice);

    } catch (error) {
      console.error(`    ❌ ${asset} 信号检测失败:`, error.message);
    }
  }

  // 创建信号记录（使用统一信号生成器结果）
  async createSignal(user, asset, generatedSignal, analysisResult, signalType, currentPrice) {
    try {
      // 动态止损止盈已由统一信号生成器计算完成
      const stopLoss = generatedSignal.stop_loss;
      const takeProfit = generatedSignal.take_profit;
      const atr = generatedSignal.atr;

      // 实际风险金额计算
      const riskAmount = user.capital * user.risk_percentage;
      const stopLossDistance = Math.abs(stopLoss - currentPrice) / currentPrice;

      console.log(`    🛡️ 动态止损: ${stopLoss.toFixed(2)} (距离: ${(stopLossDistance * 100).toFixed(2)}%)`);
      console.log(`    🎯 动态止盈: ${takeProfit.toFixed(2)}`);
      console.log(`    💰 风险金额: $${riskAmount.toFixed(2)} (${(user.risk_percentage * 100).toFixed(1)}%)`);
      console.log(`    ⚙️ ATR: ${atr.toFixed(4)}`);

      // 计算实际的美元仓位金额
      const actualPositionAmount = analysisResult.suggested_position * user.capital;

      // 保存有效信号到数据库
      const signalResult = await database.run(
        `INSERT INTO signals (
          user_id, asset, price, rsi, ema_short, ema_long,
          trend_4h, pattern, signal_type, signal_strength,
          suggested_position, risk_amount, stop_loss_distance,
          volume_ratio, quality_score, m_head_detected, w_bottom_detected,
          entry_price, stop_loss, take_profit, is_active
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          user.id, asset, currentPrice, generatedSignal.rsi, generatedSignal.ema_short, generatedSignal.ema_long,
          generatedSignal.trend_4h, generatedSignal.pattern, signalType, analysisResult.strength,
          actualPositionAmount, // 使用实际美元金额
          riskAmount, // 基于风险百分比的实际风险金额
          stopLossDistance, // 基于ATR的动态止损距离
          generatedSignal.volume_ratio,
          analysisResult.quality_score,
          generatedSignal.m_head_detected ? 1 : 0,
          generatedSignal.w_bottom_detected ? 1 : 0,
          generatedSignal.entry_price, // entry_price
          stopLoss,     // 动态止损
          takeProfit,   // 动态止盈
          1 // is_active: 所有保存的信号都是有效的
        ]
      );

      const signal = {
        id: signalResult.id,
        ...generatedSignal, // 使用统一生成器的完整结果
        suggested_position: actualPositionAmount, // 使用实际美元金额
        timestamp: new Date().toISOString()
      };

      // 发送信号通知
      await notificationService.sendSignalNotification(user.id, signal);
      console.log(`    🔔 有效信号已保存并发送通知 (ID: ${signalResult.id})`);

    } catch (error) {
      console.error(`    ❌ 创建信号失败:`, error.message);
    }
  }

  // 获取服务状态
  getStatus() {
    return {
      isRunning: this.isRunning,
      lastCheckTime: this.lastCheckTime,
      checkInterval: this.currentInterval,      // 前端期望的字段名
      currentInterval: this.currentInterval,    // 保持兼容性
      baseCheckInterval: this.baseCheckInterval,
      supportedAssets: this.supportedAssets
    };
  }

  // 检测配置类型
  detectConfigType(config) {
    return detectConfigType(config);
  }

  // 预判断信号类型（使用统一信号生成器）
  async determineSignalType(asset, config) {
    try {
      const generatedSignal = await signalGenerator.generateSignal(asset, config);
      return generatedSignal.signal_type;
    } catch (error) {
      console.error(`    ❌ 预判断信号类型失败:`, error.message);
      return 'hold';
    }
  }

  // 获取当前1小时K线的完成进度
  getCurrentCandleProgress() {
    const now = new Date();
    const minutes = now.getMinutes();
    const seconds = now.getSeconds();

    const progress = (minutes * 60 + seconds) / 3600; // 0-1之间
    return progress;
  }

  // 评估市场活跃度
  async assessMarketActivity() {
    try {
      // 获取主要币种的数据来评估整体市场活跃度
      const btcData = await signalService.getHistoricalData('bitcoin', 1);
      const ethData = await signalService.getHistoricalData('ethereum', 1);

      // 计算成交量比率和波动率
      const btcVolumeRatio = signalService.calculateVolumeRatio(btcData);
      const ethVolumeRatio = signalService.calculateVolumeRatio(ethData);

      // 计算价格波动率
      const btcVolatility = this.calculateVolatility(btcData);
      const ethVolatility = this.calculateVolatility(ethData);

      const avgVolumeRatio = (btcVolumeRatio + ethVolumeRatio) / 2;
      const avgVolatility = (btcVolatility + ethVolatility) / 2;

      // 判断市场活跃度
      if (avgVolumeRatio > 2 && avgVolatility > 0.02) {
        return 'high';    // 高活跃度
      } else if (avgVolumeRatio > 1.5 && avgVolatility > 0.01) {
        return 'medium';  // 中活跃度
      } else {
        return 'low';     // 低活跃度
      }
    } catch (error) {
      console.error('评估市场活跃度失败:', error.message);
      return 'medium'; // 默认中等活跃度
    }
  }

  // 计算价格波动率
  calculateVolatility(historicalData) {
    if (historicalData.length < 2) return 0;

    const prices = historicalData.slice(-24).map(d => d[1]); // 最近24个数据点
    const returns = [];

    for (let i = 1; i < prices.length; i++) {
      returns.push((prices[i] - prices[i-1]) / prices[i-1]);
    }

    // 计算标准差作为波动率
    const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / returns.length;

    return Math.sqrt(variance);
  }

  // 根据K线完成度和市场活跃度调整检测频率
  adjustCheckFrequency(candleProgress, marketActivity) {
    const progressPercent = (candleProgress * 100).toFixed(1);

    console.log(`📊 市场活跃度: ${marketActivity}, K线完成度: ${progressPercent}%`);

    // 综合判断检测频率
    let targetInterval = this.baseCheckInterval;

    if (candleProgress > 0.8 || marketActivity === 'high') {
      // K线接近完成或市场高活跃度 → 高频检测
      targetInterval = this.highFreqInterval;
      if (!this.tempHighFreqMode) {
        console.log(`⚡ 进入高频检测模式(5分钟) - 原因: ${candleProgress > 0.8 ? 'K线即将完成' : '市场高活跃度'}`);
        this.tempHighFreqMode = true;
      }
    } else if (marketActivity === 'low' && candleProgress < 0.5) {
      // 市场低活跃度且K线完成度较低 → 低频检测
      targetInterval = this.lowFreqInterval;
      if (this.tempHighFreqMode) {
        console.log(`🐌 切换到低频检测模式(15分钟) - 市场活跃度低`);
        this.tempHighFreqMode = false;
      }
    } else {
      // 其他情况 → 基础检测频率
      if (this.tempHighFreqMode && candleProgress < 0.2 && marketActivity !== 'high') {
        console.log(`🔄 恢复基础检测模式(10分钟)`);
        this.tempHighFreqMode = false;
      }
    }

    this.setCheckInterval(targetInterval);
  }

  // 动态设置检测间隔
  setCheckInterval(newInterval) {
    if (this.currentInterval !== newInterval) {
      this.currentInterval = newInterval;

      // 重新设置定时器
      if (this.intervalId) {
        clearInterval(this.intervalId);
        this.intervalId = setInterval(() => {
          this.checkAllSignals();
        }, this.currentInterval);
      }

      console.log(`⚙️ 检测间隔已调整为: ${newInterval / 1000 / 60} 分钟`);
    }
  }

  // 根据信号强度获取检测策略
  getDetectionStrategy(signalStrength) {
    const strategies = {
      'strong': {
        cooldown: 15 * 60 * 1000,  // 强信号15分钟冷却
        minPriceChange: 0.003      // 0.3%价格变化才重新检测
      },
      'medium': {
        cooldown: 20 * 60 * 1000,  // 中等信号20分钟冷却
        minPriceChange: 0.005      // 0.5%价格变化
      },
      'weak': {
        cooldown: 30 * 60 * 1000,  // 弱信号30分钟冷却
        minPriceChange: 0.008      // 0.8%价格变化
      }
    };

    return strategies[signalStrength] || strategies['medium'];
  }
}

module.exports = new AutoSignalService();
