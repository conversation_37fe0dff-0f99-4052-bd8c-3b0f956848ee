const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// 确保数据库目录存在
const dbDir = path.join(__dirname, '../data');
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

const dbPath = path.join(dbDir, 'scalpalert.db');

// 创建数据库连接
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('数据库连接失败:', err.message);
    return;
  }
  console.log('已连接到 SQLite 数据库');
});

// 创建表结构
const createTables = () => {
  // 用户表
  db.run(`
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      username TEXT UNIQUE NOT NULL,
      password_hash TEXT NOT NULL,
      capital REAL DEFAULT 5000.0,
      loss_limit REAL DEFAULT 3000.0,
      risk_percentage REAL DEFAULT 0.01,
      max_daily_trades INTEGER DEFAULT 5,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // 交易信号表
  db.run(`
    CREATE TABLE IF NOT EXISTS signals (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      asset TEXT NOT NULL,
      timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
      price REAL NOT NULL,
      rsi REAL,
      ema_short REAL,
      ema_long REAL,
      trend_4h TEXT,
      pattern TEXT,
      signal_type TEXT NOT NULL,
      signal_strength TEXT NOT NULL,
      suggested_position REAL NOT NULL,
      risk_amount REAL NOT NULL,
      stop_loss_distance REAL,
      volume_ratio REAL DEFAULT 1.0,
      quality_score INTEGER DEFAULT 50,
      m_head_detected BOOLEAN DEFAULT 0,
      w_bottom_detected BOOLEAN DEFAULT 0,
      entry_price REAL,
      stop_loss REAL,
      take_profit REAL,
      is_active BOOLEAN DEFAULT 1,
      FOREIGN KEY (user_id) REFERENCES users(id)
    )
  `);

  // 实际交易表
  db.run(`
    CREATE TABLE IF NOT EXISTS trades (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      signal_id INTEGER,
      user_id INTEGER NOT NULL,
      asset TEXT NOT NULL,
      buy_price REAL NOT NULL,
      buy_amount REAL NOT NULL,
      buy_time DATETIME DEFAULT CURRENT_TIMESTAMP,
      sell_price REAL,
      sell_time DATETIME,
      profit_loss REAL,
      stop_loss REAL NOT NULL,
      take_profit REAL NOT NULL,
      is_compliant BOOLEAN,
      status TEXT DEFAULT 'open',
      FOREIGN KEY (signal_id) REFERENCES signals(id),
      FOREIGN KEY (user_id) REFERENCES users(id)
    )
  `);

  // 统计数据表
  db.run(`
    CREATE TABLE IF NOT EXISTS statistics (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      period TEXT NOT NULL,
      start_date DATE NOT NULL,
      end_date DATE NOT NULL,
      win_rate REAL,
      total_profit REAL,
      total_trades INTEGER,
      avg_hold_time INTEGER,
      compliance_rate REAL,
      max_drawdown REAL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users(id)
    )
  `);

  // 用户信号配置表
  db.run(`
    CREATE TABLE IF NOT EXISTS signal_config (
      user_id INTEGER PRIMARY KEY,
      rsi_strong_threshold INTEGER DEFAULT 30,
      rsi_medium_threshold INTEGER DEFAULT 40,
      risk_strong REAL DEFAULT 0.01,           -- 强信号风险参数
      risk_medium REAL DEFAULT 0.008,          -- 中等信号风险参数
      risk_weak REAL DEFAULT 0.005,            -- 弱信号风险参数
      atr_multiplier REAL DEFAULT 1.5,         -- ATR止损倍数
      risk_reward_ratio REAL DEFAULT 2.0,      -- 风险回报比
      volume_multiplier REAL DEFAULT 1.5,
      enable_volume_filter BOOLEAN DEFAULT 1,
      enable_m_head_filter BOOLEAN DEFAULT 0,
      enable_w_bottom_bonus BOOLEAN DEFAULT 1,
      skip_weak_signals BOOLEAN DEFAULT 0,
      min_quality_score INTEGER DEFAULT 40,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users(id)
    )
  `);



  // 通知记录表
  db.run(`
    CREATE TABLE IF NOT EXISTS notifications (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      type TEXT NOT NULL,
      title TEXT NOT NULL,
      message TEXT NOT NULL,
      sent_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      is_read BOOLEAN DEFAULT 0,
      FOREIGN KEY (user_id) REFERENCES users(id)
    )
  `, (err) => {
    if (err) {
      console.error('创建表失败:', err.message);
      db.close();
      return;
    } else {
      console.log('数据库表创建成功');
    }

    // 表创建完成后，执行迁移逻辑
    performMigrations();
  });

  // 执行数据库迁移
  function performMigrations() {
    console.log('🔄 开始数据库迁移...');

    // 1. 迁移signal_config表
    console.log('🔄 检查并迁移signal_config表结构...');

    // 表创建时已包含所有字段，无需重复添加
    console.log('✅ signal_config表结构检查完成');

    // 迁移数据（如果需要）
    migrateSignalConfigData();
  }

  // 迁移signal_config数据
  function migrateSignalConfigData() {
    setTimeout(() => {
      db.run(`
        UPDATE signal_config
        SET
          risk_strong = COALESCE(position_strong, risk_strong, 0.01),
          risk_medium = COALESCE(position_medium, risk_medium, 0.008),
          risk_weak = COALESCE(position_weak, risk_weak, 0.005)
        WHERE EXISTS (
          SELECT 1 FROM pragma_table_info('signal_config')
          WHERE name IN ('position_strong', 'position_medium', 'position_weak')
        )
      `, (err) => {
        if (err) {
          console.log('ℹ️ 无需迁移旧数据或迁移完成');
        } else {
          console.log('✅ 旧数据迁移完成');
        }

        // 继续迁移signals表
        migrateSignalsTable();
      });
    }, 500);
  }

  // 迁移signals表
  function migrateSignalsTable() {
    console.log('🔄 检查并迁移signals表结构...');

    // 检查signals表是否需要添加新字段
    db.all("PRAGMA table_info(signals)", (err, columns) => {
      if (err) {
        console.log('ℹ️ signals表不存在或检查失败，跳过迁移');
        finalizeMigration();
        return;
      }

      const existingColumns = columns.map(col => col.name);
      const requiredColumns = [
        { name: 'volume_ratio', type: 'REAL', default: '1.0' },
        { name: 'quality_score', type: 'INTEGER', default: '50' },
        { name: 'm_head_detected', type: 'BOOLEAN', default: '0' },
        { name: 'w_bottom_detected', type: 'BOOLEAN', default: '0' },
        { name: 'entry_price', type: 'REAL', default: 'NULL' },
        { name: 'stop_loss', type: 'REAL', default: 'NULL' },
        { name: 'take_profit', type: 'REAL', default: 'NULL' },
        { name: 'is_active', type: 'BOOLEAN', default: '1' }
      ];

      const missingColumns = requiredColumns.filter(col =>
        !existingColumns.includes(col.name)
      );

      if (missingColumns.length === 0) {
        console.log('✅ signals表所有字段都已存在');
        finalizeMigration();
        return;
      }

      console.log(`📝 需要为signals表添加 ${missingColumns.length} 个字段`);

      // 添加缺失的字段
      let completed = 0;
      missingColumns.forEach(col => {
        const sql = `ALTER TABLE signals ADD COLUMN ${col.name} ${col.type} DEFAULT ${col.default}`;

        db.run(sql, (err) => {
          if (err && !err.message.includes('duplicate column name')) {
            console.error(`❌ 添加signals字段 ${col.name} 失败:`, err.message);
          } else if (!err) {
            console.log(`✅ 已添加signals字段: ${col.name}`);
          }

          completed++;
          if (completed === missingColumns.length) {
            console.log('✅ signals表结构检查完成');
            finalizeMigration();
          }
        });
      });
    });
  }

  // 完成迁移
  function finalizeMigration() {
    console.log('✅ 数据库迁移完成');

    // 关闭数据库连接
    db.close((err) => {
      if (err) {
        console.error('关闭数据库失败:', err.message);
      } else {
        console.log('数据库连接已关闭');
      }
    });
  }
};

// 执行创建表
createTables();
