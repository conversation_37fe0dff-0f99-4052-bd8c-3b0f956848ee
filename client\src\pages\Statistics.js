import React, { useState, useEffect } from 'react';
import { BarChart3, TrendingUp, TrendingDown, Target, Activity, Calendar, RefreshCw } from 'lucide-react';
import { LineChart, Line, BarChart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { tradeAPI } from '../utils/api';
import { formatCurrency, formatPercentage } from '../utils/api';
import toast from 'react-hot-toast';

const Statistics = () => {
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [period, setPeriod] = useState('all');
  const [stats, setStats] = useState(null);
  const [chartData, setChartData] = useState({
    profitTrend: [],
    winLossDistribution: [],
    assetPerformance: [],
  });

  // 获取统计数据
  const fetchStatistics = async () => {
    try {
      const response = await tradeAPI.getStats({ period });
      const statsData = response.data.data;
      setStats(statsData);

      // 生成图表数据
      generateChartData(statsData);
    } catch (error) {
      console.error('获取统计数据失败:', error);
      toast.error('获取统计数据失败');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // 生成图表数据
  const generateChartData = (statsData) => {
    // 模拟盈亏趋势数据
    const profitTrend = [
      { date: '1月', profit: 150 },
      { date: '2月', profit: -80 },
      { date: '3月', profit: 220 },
      { date: '4月', profit: 180 },
      { date: '5月', profit: -50 },
      { date: '6月', profit: 300 },
    ];

    // 胜负分布
    const winLossDistribution = [
      { name: '盈利交易', value: statsData.winning_trades || 0, color: '#22c55e' },
      { name: '亏损交易', value: (statsData.closed_trades || 0) - (statsData.winning_trades || 0), color: '#ef4444' },
    ];

    // 资产表现（模拟数据）
    const assetPerformance = [
      { asset: 'BTC', trades: 15, profit: 450 },
      { asset: 'ETH', trades: 8, profit: -120 },
      { asset: 'BNB', trades: 5, profit: 80 },
    ];

    setChartData({
      profitTrend,
      winLossDistribution,
      assetPerformance,
    });
  };

  // 刷新数据
  const handleRefresh = () => {
    setRefreshing(true);
    fetchStatistics();
  };

  useEffect(() => {
    fetchStatistics();
  }, [period]);

  if (loading) {
    return (
      <div className="mobile-content">
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="card">
              <div className="skeleton h-4 w-3/4 mb-2"></div>
              <div className="skeleton h-32 w-full"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="mobile-content">
        <div className="text-center py-12">
          <BarChart3 className="mx-auto text-gray-400 mb-4" size={64} />
          <h3 className="text-lg font-medium text-gray-900 mb-2">暂无统计数据</h3>
          <p className="text-gray-600 mb-6">
            完成一些交易后，这里将显示详细的统计分析
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="mobile-content">
      {/* 操作栏 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <select
            value={period}
            onChange={(e) => setPeriod(e.target.value)}
            className="input text-sm px-3 py-2"
          >
            <option value="today">今日</option>
            <option value="week">本周</option>
            <option value="month">本月</option>
            <option value="all">全部</option>
          </select>
        </div>
        <button
          onClick={handleRefresh}
          disabled={refreshing}
          className="btn-outline px-3 py-2"
        >
          <RefreshCw size={16} className={`mr-1 ${refreshing ? 'animate-spin' : ''}`} />
          刷新
        </button>
      </div>

      {/* 核心指标卡片 */}
      <div className="grid grid-cols-2 gap-4 mb-6">
        <div className="card">
          <div className="flex items-center justify-between mb-2">
            <div className="w-10 h-10 bg-success-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="text-success-600" size={20} />
            </div>
            <span className="text-2xl font-bold text-success-600">
              {formatPercentage(stats.win_rate)}
            </span>
          </div>
          <p className="text-sm text-gray-600">胜率</p>
          <p className="text-xs text-gray-500 mt-1">
            {stats.winning_trades}/{stats.closed_trades} 笔
          </p>
        </div>

        <div className="card">
          <div className="flex items-center justify-between mb-2">
            <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
              <Target className="text-primary-600" size={20} />
            </div>
            <span className={`text-2xl font-bold ${
              stats.net_profit >= 0 ? 'text-success-600' : 'text-danger-600'
            }`}>
              {formatCurrency(stats.net_profit)}
            </span>
          </div>
          <p className="text-sm text-gray-600">总盈亏</p>
          <p className="text-xs text-gray-500 mt-1">
            净收益
          </p>
        </div>

        <div className="card">
          <div className="flex items-center justify-between mb-2">
            <div className="w-10 h-10 bg-warning-100 rounded-lg flex items-center justify-center">
              <Activity className="text-warning-600" size={20} />
            </div>
            <span className="text-2xl font-bold text-gray-900">
              {stats.total_trades}
            </span>
          </div>
          <p className="text-sm text-gray-600">交易次数</p>
          <p className="text-xs text-gray-500 mt-1">
            总计
          </p>
        </div>

        <div className="card">
          <div className="flex items-center justify-between mb-2">
            <div className="w-10 h-10 bg-danger-100 rounded-lg flex items-center justify-center">
              <TrendingDown className="text-danger-600" size={20} />
            </div>
            <span className="text-2xl font-bold text-gray-900">
              {formatPercentage(stats.compliance_rate)}
            </span>
          </div>
          <p className="text-sm text-gray-600">遵守率</p>
          <p className="text-xs text-gray-500 mt-1">
            策略遵守
          </p>
        </div>
      </div>

      {/* 盈亏趋势图 */}
      <div className="card mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">盈亏趋势</h3>
        <div className="h-48">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={chartData.profitTrend}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip
                formatter={(value) => [formatCurrency(value), '盈亏']}
                labelStyle={{ color: '#374151' }}
              />
              <Line
                type="monotone"
                dataKey="profit"
                stroke="#3b82f6"
                strokeWidth={2}
                dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* 胜负分布饼图 */}
      <div className="card mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">胜负分布</h3>
        <div className="h-48">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={chartData.winLossDistribution}
                cx="50%"
                cy="50%"
                innerRadius={40}
                outerRadius={80}
                paddingAngle={5}
                dataKey="value"
              >
                {chartData.winLossDistribution.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip
                formatter={(value, name) => [value, name]}
              />
            </PieChart>
          </ResponsiveContainer>
        </div>
        <div className="flex justify-center space-x-6 mt-4">
          {chartData.winLossDistribution.map((item, index) => (
            <div key={index} className="flex items-center">
              <div
                className="w-3 h-3 rounded-full mr-2"
                style={{ backgroundColor: item.color }}
              ></div>
              <span className="text-sm text-gray-600">{item.name}: {item.value}</span>
            </div>
          ))}
        </div>
      </div>

      {/* 资产表现 */}
      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">资产表现</h3>
        <div className="h-48">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={chartData.assetPerformance}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="asset" />
              <YAxis />
              <Tooltip
                formatter={(value, name) => [
                  name === 'profit' ? formatCurrency(value) : value,
                  name === 'profit' ? '盈亏' : '交易次数'
                ]}
              />
              <Bar dataKey="trades" fill="#94a3b8" name="trades" />
              <Bar dataKey="profit" fill="#3b82f6" name="profit" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
};

export default Statistics;
