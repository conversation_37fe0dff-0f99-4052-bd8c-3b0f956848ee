/**
 * 策略优化测试脚本
 * 专门用于测试优化后的策略配置
 */

const path = require('path');
const Backtester = require('../engine/backtester');
const { backtestConfig, strategyConfigs } = require('../config/backtest.config');

/**
 * 运行策略优化测试
 */
async function runOptimizationTest() {
  console.log('🎯 策略优化测试');
  console.log('='.repeat(60));
  
  try {
    const backtester = new Backtester(backtestConfig);
    
    // 加载数据
    const dataPath = path.join(__dirname, '../data/ETHUSDT_1h.json');
    console.log(`📊 加载数据: ${dataPath}`);
    
    const validation = backtester.loadData(dataPath);
    console.log(`✅ 数据验证通过: ${validation.totalRecords} 条记录`);
    
    // 测试优化后的策略
    const optimizedConfigs = [
      strategyConfigs.conservative,  // 保守优化型
      strategyConfigs.balanced,      // 平衡优化型
      strategyConfigs.qualityFirst   // 质量优先型
    ];
    
    console.log(`\n🔄 开始测试 ${optimizedConfigs.length} 个优化策略...\n`);
    
    const results = [];
    
    for (let i = 0; i < optimizedConfigs.length; i++) {
      const config = optimizedConfigs[i];
      console.log(`📊 测试策略 ${i + 1}/${optimizedConfigs.length}: ${config.name}`);
      console.log('-'.repeat(50));
      
      try {
        const result = await backtester.runBacktest(config);
        results.push({
          configName: config.name,
          ...result
        });
        
        // 显示关键指标
        const m = result.metrics;
        console.log(`✅ ${config.name} 完成:`);
        console.log(`   总收益: ${m.totalReturn.toFixed(2)}%`);
        console.log(`   胜率: ${(m.winRate * 100).toFixed(2)}%`);
        console.log(`   盈亏比: ${m.profitFactor.toFixed(2)}`);
        console.log(`   最大回撤: ${m.maxDrawdown.toFixed(2)}%`);
        console.log(`   交易次数: ${m.totalTrades}`);
        console.log(`   手续费: $${m.totalFees.toFixed(2)}`);
        console.log(`   夏普比率: ${m.sharpeRatio.toFixed(3)}\n`);
        
      } catch (error) {
        console.error(`❌ 策略 ${config.name} 测试失败:`, error.message);
        results.push({
          configName: config.name,
          error: error.message
        });
      }
    }
    
    // 生成对比报告
    generateComparisonReport(results);
    
    return results;
    
  } catch (error) {
    console.error('❌ 优化测试失败:', error.message);
    throw error;
  }
}

/**
 * 生成策略对比报告
 */
function generateComparisonReport(results) {
  console.log('\n📊 策略优化对比报告');
  console.log('='.repeat(100));
  
  // 表头
  const headers = [
    '策略名称'.padEnd(15),
    '总收益%'.padEnd(10),
    '胜率%'.padEnd(8),
    '盈亏比'.padEnd(8),
    '回撤%'.padEnd(8),
    '交易数'.padEnd(8),
    '手续费$'.padEnd(10),
    '夏普比率'.padEnd(10)
  ];
  console.log(headers.join(''));
  console.log('-'.repeat(100));
  
  // 数据行
  const validResults = results.filter(r => !r.error);
  validResults.forEach(result => {
    if (result.metrics) {
      const m = result.metrics;
      const row = [
        result.configName.padEnd(15),
        m.totalReturn.toFixed(2).padEnd(10),
        (m.winRate * 100).toFixed(2).padEnd(8),
        m.profitFactor.toFixed(2).padEnd(8),
        m.maxDrawdown.toFixed(2).padEnd(8),
        m.totalTrades.toString().padEnd(8),
        m.totalFees.toFixed(0).padEnd(10),
        m.sharpeRatio.toFixed(3).padEnd(10)
      ];
      console.log(row.join(''));
    }
  });
  
  console.log('='.repeat(100));
  
  // 找出最佳策略
  if (validResults.length > 0) {
    const bestByReturn = validResults.reduce((best, current) => 
      (current.metrics.totalReturn > best.metrics.totalReturn) ? current : best
    );
    
    const bestBySharpe = validResults.reduce((best, current) => 
      (current.metrics.sharpeRatio > best.metrics.sharpeRatio) ? current : best
    );
    
    const bestByWinRate = validResults.reduce((best, current) => 
      (current.metrics.winRate > best.metrics.winRate) ? current : best
    );
    
    console.log('\n🏆 最佳策略排名:');
    console.log(`📈 最高收益: ${bestByReturn.configName} (${bestByReturn.metrics.totalReturn.toFixed(2)}%)`);
    console.log(`⚖️ 最佳夏普: ${bestBySharpe.configName} (${bestBySharpe.metrics.sharpeRatio.toFixed(3)})`);
    console.log(`🎯 最高胜率: ${bestByWinRate.configName} (${(bestByWinRate.metrics.winRate * 100).toFixed(2)}%)`);
  }
  
  // 优化建议
  console.log('\n💡 优化建议:');
  validResults.forEach(result => {
    const m = result.metrics;
    console.log(`\n${result.configName}:`);
    
    if (m.totalReturn < 0) {
      console.log('  ❌ 仍然亏损，需要进一步优化');
    } else {
      console.log('  ✅ 实现盈利');
    }
    
    if (m.winRate < 0.4) {
      console.log('  📉 胜率偏低，建议提高信号质量要求');
    } else if (m.winRate > 0.6) {
      console.log('  📈 胜率良好');
    }
    
    if (m.profitFactor < 1.2) {
      console.log('  ⚠️ 盈亏比偏低，建议扩大止盈目标');
    } else if (m.profitFactor > 1.5) {
      console.log('  💪 盈亏比健康');
    }
    
    if (m.maxDrawdown > 25) {
      console.log('  🔻 回撤过大，建议降低风险参数');
    } else if (m.maxDrawdown < 15) {
      console.log('  🛡️ 回撤控制良好');
    }
    
    const feeRatio = (m.totalFees / 10000) * 100;
    if (feeRatio > 10) {
      console.log('  💸 手续费过高，建议降低交易频率');
    } else if (feeRatio < 5) {
      console.log('  💰 手续费控制合理');
    }
  });
}

/**
 * 运行单个策略的详细分析
 */
async function runDetailedAnalysis(strategyName) {
  console.log(`\n🔍 ${strategyName} 详细分析`);
  console.log('='.repeat(50));
  
  try {
    const backtester = new Backtester(backtestConfig);
    
    // 加载数据
    const dataPath = path.join(__dirname, '../data/ETHUSDT_1h.json');
    backtester.loadData(dataPath);
    
    const config = strategyConfigs[strategyName];
    if (!config) {
      throw new Error(`策略 ${strategyName} 不存在`);
    }
    
    // 运行回测
    const result = await backtester.runBacktest(config);
    
    // 显示详细报告
    console.log(backtester.getReport());
    
    // 导出结果
    const outputDir = `./backtest_results/${strategyName}_${new Date().toISOString().slice(0, 10)}`;
    backtester.exportResults(outputDir);
    
    return result;
    
  } catch (error) {
    console.error(`❌ ${strategyName} 详细分析失败:`, error.message);
    throw error;
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length > 0) {
    // 运行特定策略的详细分析
    const strategyName = args[0];
    await runDetailedAnalysis(strategyName);
  } else {
    // 运行优化测试
    await runOptimizationTest();
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 程序执行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  runOptimizationTest,
  runDetailedAnalysis,
  generateComparisonReport
};
