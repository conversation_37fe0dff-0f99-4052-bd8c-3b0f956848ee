/**
 * 信号强度分析器
 * 负责计算信号质量评分、强度分级和仓位建议
 */

class SignalAnalyzer {
  constructor() {
    // 默认配置
    this.defaultConfig = {
      rsi_strong_threshold: 30,
      rsi_medium_threshold: 40,
      risk_strong: 0.01,           // 更新为新的风险参数
      risk_medium: 0.008,          // 更新为新的风险参数
      risk_weak: 0.005,            // 更新为新的风险参数
      atr_multiplier: 1.5,         // 新增ATR倍数
      risk_reward_ratio: 2.0,      // 新增风险回报比
      volume_multiplier: 1.5,
      enable_volume_filter: true,
      enable_m_head_filter: false, // 关闭M头过滤，增加信号机会
      enable_w_bottom_bonus: true,
      skip_weak_signals: false,
      min_quality_score: 40        // 降低最低质量要求
    };
  }

  /**
   * 计算信号质量评分
   * @param {Object} signalData - 信号数据
   * @param {Object} config - 用户配置
   * @returns {Object} 评分结果
   */
  calculateQualityScore(signalData, config = this.defaultConfig) {
    let score = 0;
    const details = {};

    // 1. RSI指标评分 (40分)
    const rsiScore = this.calculateRSIScore(signalData.rsi);
    score += rsiScore;
    details.rsi = { score: rsiScore, value: signalData.rsi };

    // 2. EMA交叉评分 (30分)
    const emaScore = this.calculateEMAScore(signalData.ema_short, signalData.ema_long, signalData.price);
    score += emaScore;
    details.ema = { score: emaScore, short: signalData.ema_short, long: signalData.ema_long };

    // 3. 成交量评分 (30分)
    const volumeScore = this.calculateVolumeScore(signalData.volume_ratio, config.volume_multiplier);
    score += volumeScore;
    details.volume = { score: volumeScore, ratio: signalData.volume_ratio };

    // 4. 形态加分/减分
    if (signalData.w_bottom_detected && config.enable_w_bottom_bonus) {
      score += 10;
      details.w_bottom = { bonus: 10 };
    }

    if (!signalData.m_head_detected) {
      score += 10;
      details.no_m_head = { bonus: 10 };
    } else if (config.enable_m_head_filter) {
      score -= 20;
      details.m_head = { penalty: -20 };
    }

    // 5. 趋势确认加分
    if (signalData.trend_4h === 'bullish') {
      score += 5;
      details.trend = { bonus: 5, direction: 'bullish' };
    }

    // 6. 多时间周期一致性评分 (新增)
    if (signalData.mtfAnalysis) {
      const mtfScore = this.calculateMTFScore(signalData.mtfAnalysis);
      score += mtfScore;
      details.mtf = { score: mtfScore, recommendation: signalData.mtfAnalysis.recommendation };
    }

    // 7. MACD确认评分 (新增)
    if (signalData.macd) {
      const macdScore = this.calculateMACDScore(signalData.macd);
      score += macdScore;
      details.macd = { score: macdScore, histogram: signalData.macd.histogram };
    }

    // 确保评分在0-100范围内
    score = Math.max(0, Math.min(100, score));

    return {
      score,
      details,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * RSI指标评分（优化：RSI>50时不给分，专注于低位买入机会）
   */
  calculateRSIScore(rsi) {
    if (rsi < 20) return 40; // 极度超卖
    if (rsi < 25) return 35; // 严重超卖
    if (rsi < 30) return 30; // 超卖
    if (rsi < 35) return 25; // 偏低
    if (rsi < 40) return 20; // 弱势
    if (rsi < 45) return 15; // 略弱
    if (rsi < 50) return 10; // 中性偏弱
    return 0; // RSI>50时不给分，我们寻找低位买入机会
  }

  /**
   * EMA交叉评分（优化：放宽价格条件，只要维持在长期均线之上）
   */
  calculateEMAScore(emaShort, emaLong, currentPrice) {
    if (!emaShort || !emaLong) return 10; // 如果没有EMA数据，给基础分

    const crossover = emaShort > emaLong;
    const priceAboveLongEMA = currentPrice > emaLong; // 改为长期均线
    const priceAboveShortEMA = currentPrice > emaShort;
    const emaDivergence = (emaShort - emaLong) / emaLong;

    // 只要价格维持在长期均线之上，大趋势未变
    if (crossover && priceAboveLongEMA) {
      if (priceAboveShortEMA && emaDivergence > 0.02) return 30; // 完美金叉
      if (priceAboveShortEMA && emaDivergence > 0.01) return 25; // 强金叉
      if (priceAboveShortEMA) return 20; // 标准金叉
      return 18; // 金叉 + 长期趋势完好（短期回调是买入机会）
    }

    if (crossover && !priceAboveLongEMA) return 12; // 金叉但长期趋势破坏
    if (!crossover && priceAboveLongEMA && emaDivergence > -0.005) return 15; // 即将金叉且趋势完好
    if (!crossover && priceAboveLongEMA && emaDivergence > -0.01) return 12; // 接近金叉且趋势完好

    return 5; // 给基础分，避免完全没分
  }

  /**
   * 成交量评分
   */
  calculateVolumeScore(volumeRatio, multiplier) {
    if (!volumeRatio) return 10; // 如果没有成交量数据，给基础分

    if (volumeRatio >= multiplier * 2) return 30; // 成交量大幅放大
    if (volumeRatio >= multiplier * 1.5) return 25; // 成交量显著放大
    if (volumeRatio >= multiplier) return 20; // 成交量放大
    if (volumeRatio >= multiplier * 0.8) return 15; // 成交量正常
    if (volumeRatio >= multiplier * 0.5) return 10; // 成交量偏低但可接受
    if (volumeRatio >= multiplier * 0.3) return 5; // 成交量较低
    return 0; // 成交量萎缩，但不扣分
  }

  /**
   * 信号强度分级（基于风险的版本）
   */
  classifySignalStrength(score, config = this.defaultConfig) {
    if (score >= 70) {
      return {
        strength: 'strong',
        riskPercent: config.risk_strong || 0.01,
        description: '强信号 - 多项指标确认'
      };
    }

    if (score >= 55) {
      return {
        strength: 'medium',
        riskPercent: config.risk_medium || 0.008,
        description: '中等信号 - 部分指标确认'
      };
    }

    if (score >= 40) {
      return {
        strength: 'weak',
        riskPercent: config.risk_weak || 0.005,
        description: '弱信号 - 少数指标确认'
      };
    }

    return {
      strength: 'skip',
      riskPercent: 0,
      description: '信号质量过低 - 建议跳过'
    };
  }

  /**
   * 综合分析信号
   */
  analyzeSignal(signalData, config = this.defaultConfig) {
    // 计算质量评分
    const qualityResult = this.calculateQualityScore(signalData, config);

    // 信号强度分级（基于风险的版本）
    const strengthResult = this.classifySignalStrength(qualityResult.score, config);

    // 基于风险的仓位计算
    let suggestedPosition = 0;
    let positionDescription = strengthResult.description;

    if (signalData.atr && signalData.price && strengthResult.strength !== 'skip') {
      // 计算基于风险的仓位
      const riskBasedPosition = this.calculateRiskBasedPosition(
        signalData.price,
        signalData.atr,
        strengthResult.riskPercent,
        config.atr_multiplier || 1.5
      );

      suggestedPosition = riskBasedPosition.position;
      positionDescription = `${strengthResult.description} (风险: ${(strengthResult.riskPercent * 100).toFixed(1)}%, 仓位: ${(suggestedPosition * 100).toFixed(2)}%, 止损距离: ${riskBasedPosition.stopLossPercent.toFixed(2)}%)`;
    }

    // 检查是否应该跳过弱信号
    if (config.skip_weak_signals && strengthResult.strength === 'weak') {
      strengthResult.strength = 'skip';
      suggestedPosition = 0;
      positionDescription += ' - 弱信号被跳过';
    }

    // 检查最低质量要求
    if (qualityResult.score < config.min_quality_score) {
      strengthResult.strength = 'skip';
      suggestedPosition = 0;
      positionDescription += ' - 未达到最低质量要求';
    }

    return {
      quality_score: qualityResult.score,
      strength: strengthResult.strength,
      suggested_position: suggestedPosition,
      risk_percent: strengthResult.riskPercent,
      description: positionDescription,
      analysis_details: qualityResult.details,
      analyzed_at: new Date().toISOString()
    };
  }

  /**
   * 批量分析信号
   */
  analyzeSignals(signalsData, config = this.defaultConfig) {
    return signalsData.map(signal => ({
      ...signal,
      ...this.analyzeSignal(signal, config)
    }));
  }

  /**
   * 多时间周期一致性评分
   */
  calculateMTFScore(mtfAnalysis) {
    if (!mtfAnalysis) return 0;

    const { recommendation, daily, h4, h1 } = mtfAnalysis;
    let score = 0;

    // 基于综合建议的评分
    switch (recommendation) {
      case 'long_only':
      case 'short_only':
        score = 15; // 最高分：大中周期完全一致
        break;
      case 'long_preferred':
      case 'short_preferred':
        score = 12; // 高分：大周期明确，中周期中性
        break;
      case 'long_bias':
      case 'short_bias':
        score = 8; // 中等分：多数周期一致
        break;
      case 'wait':
        score = 0; // 无分：周期不一致，建议观望
        break;
      default:
        score = 0;
    }

    // 趋势强度加分
    const totalStrength = (daily.strength || 0) + (h4.strength || 0) + (h1.strength || 0);
    const strengthBonus = Math.min(5, totalStrength / 2); // 最多5分强度加分

    return Math.round(score + strengthBonus);
  }

  /**
   * MACD确认评分
   */
  calculateMACDScore(macd) {
    if (!macd) return 0;

    let score = 0;

    // MACD线与信号线关系
    if (macd.macd > macd.signal) {
      score += 5; // 金叉状态
    }

    // 柱状线方向
    if (macd.histogram > 0) {
      score += 3; // 柱状线为正
    } else if (macd.histogram < 0) {
      score += 1; // 柱状线为负但仍有一定价值
    }

    // 柱状线强度
    const histogramAbs = Math.abs(macd.histogram);
    if (histogramAbs > 0.01) {
      score += 2; // 强势柱状线
    } else if (histogramAbs > 0.005) {
      score += 1; // 中等柱状线
    }

    return Math.min(10, score); // 最高10分
  }

  /**
   * 基于风险的仓位计算（正确的风险管理方法）
   */
  calculateRiskBasedPosition(currentPrice, atr, riskPercent, atrMultiplier = 1.5) {
    // 计算ATR止损距离
    const atrStopDistance = atr * atrMultiplier;
    const stopLossPercent = atrStopDistance / currentPrice;

    // 核心公式：仓位大小 = 风险金额 / 止损距离
    // 这里计算的是仓位占总资金的百分比
    const position = riskPercent / stopLossPercent;

    // 限制仓位在合理范围内 (最小1%, 最大30%)
    // 提高最小仓位，确保有意义的交易金额
    const finalPosition = Math.max(0.01, Math.min(0.30, position));

    return {
      position: finalPosition,
      stopLossDistance: atrStopDistance,
      stopLossPercent: stopLossPercent,
      riskPercent: riskPercent,
      actualRisk: finalPosition * stopLossPercent // 实际风险百分比
    };
  }

  /**
   * 基于ATR的动态仓位计算（保留旧接口兼容性）
   */
  calculateATRBasedPosition(currentPrice, atr, basePosition, riskPercent = 0.01) {
    // 使用新的基于风险的计算方法
    const result = this.calculateRiskBasedPosition(currentPrice, atr, riskPercent, 1.5);
    return result.position;
  }
}

module.exports = new SignalAnalyzer();
