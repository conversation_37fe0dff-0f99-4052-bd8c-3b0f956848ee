[{"name": "趋势跟踪策略", "type": "trend_following", "config": {"name": "趋势跟踪策略", "type": "trend_following", "description": "拥抱趋势，简化为王 - 追求极高盈亏比", "ema_fast": 50, "ema_slow": 200, "ema_entry": 20, "atr_multiplier": 2, "risk_per_trade": 0.02, "trend_strength_threshold": 0.005, "consolidation_threshold": 0.02, "enable_candlestick_confirmation": true, "min_candle_body_ratio": 0.6, "target_win_rate": 0.35, "target_profit_factor": 2.5, "target_max_drawdown": 0.15}, "metrics": {"totalReturn": 0, "annualizedReturn": 0, "maxDrawdown": 0, "volatility": 0, "sharpeRatio": 0, "sortinoRatio": null, "calmarRatio": 0, "totalTrades": 0, "winRate": 0, "avgWin": 0, "avgLoss": 0, "profitFactor": 0, "maxWin": 0, "maxLoss": 0, "totalFees": 0, "tradingFrequency": 0, "avgHoldingTime": 0, "initialCapital": 10000, "finalCapital": 10000, "peakCapital": 10000, "backtestDays": 2938.5, "backtestPeriod": {"start": "2017-08-25T12:00:00.000Z", "end": "2025-09-11T00:00:00.000Z", "totalPoints": 17616}}}, {"name": "趋势跟踪-保守版", "type": "trend_following", "config": {"name": "趋势跟踪-保守版", "type": "trend_following", "description": "更保守的趋势跟踪，降低风险", "ema_fast": 30, "ema_slow": 100, "ema_entry": 20, "atr_multiplier": 1.5, "risk_per_trade": 0.015, "trend_strength_threshold": 0.008, "consolidation_threshold": 0.02, "enable_candlestick_confirmation": true, "min_candle_body_ratio": 0.6, "target_win_rate": 0.4, "target_profit_factor": 2, "target_max_drawdown": 0.12}, "metrics": {"totalReturn": 0, "annualizedReturn": 0, "maxDrawdown": 0, "volatility": 0, "sharpeRatio": 0, "sortinoRatio": null, "calmarRatio": 0, "totalTrades": 0, "winRate": 0, "avgWin": 0, "avgLoss": 0, "profitFactor": 0, "maxWin": 0, "maxLoss": 0, "totalFees": 0, "tradingFrequency": 0, "avgHoldingTime": 0, "initialCapital": 10000, "finalCapital": 10000, "peakCapital": 10000, "backtestDays": 2938.5, "backtestPeriod": {"start": "2017-08-25T12:00:00.000Z", "end": "2025-09-11T00:00:00.000Z", "totalPoints": 17616}}}, {"name": "震荡交易策略", "type": "range_trading", "config": {"name": "震荡交易策略", "type": "range_trading", "description": "专注震荡，高抛低吸 - 追求高胜率稳定获利", "bb_period": 20, "bb_std_dev": 2, "rsi_period": 14, "rsi_oversold": 30, "rsi_overbought": 70, "range_stability_period": 50, "max_band_width_change": 0.1, "min_touches": 3, "risk_per_trade": 0.01, "profit_target_ratio": 1.2, "max_stop_distance": 0.02, "require_volume_confirmation": true, "min_volume_ratio": 1.2, "target_win_rate": 0.65, "target_profit_factor": 1.5, "target_max_drawdown": 0.1}, "metrics": {"totalReturn": 0, "annualizedReturn": 0, "maxDrawdown": 0, "volatility": 0, "sharpeRatio": 0, "sortinoRatio": null, "calmarRatio": 0, "totalTrades": 0, "winRate": 0, "avgWin": 0, "avgLoss": 0, "profitFactor": 0, "maxWin": 0, "maxLoss": 0, "totalFees": 0, "tradingFrequency": 0, "avgHoldingTime": 0, "initialCapital": 10000, "finalCapital": 10000, "peakCapital": 10000, "backtestDays": 2938.5, "backtestPeriod": {"start": "2017-08-25T12:00:00.000Z", "end": "2025-09-11T00:00:00.000Z", "totalPoints": 17616}}}, {"name": "震荡交易-激进版", "type": "range_trading", "config": {"name": "震荡交易-激进版", "type": "range_trading", "description": "更激进的震荡交易，提高收益", "bb_period": 20, "bb_std_dev": 2, "rsi_period": 14, "rsi_oversold": 25, "rsi_overbought": 75, "range_stability_period": 50, "max_band_width_change": 0.1, "min_touches": 3, "risk_per_trade": 0.015, "profit_target_ratio": 1.5, "max_stop_distance": 0.02, "require_volume_confirmation": true, "min_volume_ratio": 1, "target_win_rate": 0.6, "target_profit_factor": 1.8, "target_max_drawdown": 0.15}, "metrics": {"totalReturn": 0, "annualizedReturn": 0, "maxDrawdown": 0, "volatility": 0, "sharpeRatio": 0, "sortinoRatio": null, "calmarRatio": 0, "totalTrades": 0, "winRate": 0, "avgWin": 0, "avgLoss": 0, "profitFactor": 0, "maxWin": 0, "maxLoss": 0, "totalFees": 0, "tradingFrequency": 0, "avgHoldingTime": 0, "initialCapital": 10000, "finalCapital": 10000, "peakCapital": 10000, "backtestDays": 2938.5, "backtestPeriod": {"start": "2017-08-25T12:00:00.000Z", "end": "2025-09-11T00:00:00.000Z", "totalPoints": 17616}}}]