import React, { createContext, useContext, useState, useEffect } from 'react';
import { authAPI } from '../utils/api';
import websocketService from '../services/websocketService';
import toast from 'react-hot-toast';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // 安全的WebSocket连接方法
  const connectWebSocketSafely = (userId, token) => {
    // 延迟连接，避免阻塞主线程
    setTimeout(async () => {
      try {
        console.log('开始安全连接WebSocket...');

        // 设置总超时时间
        const totalTimeout = setTimeout(() => {
          console.log('WebSocket连接总超时，放弃连接');
        }, 10000); // 10秒总超时

        await websocketService.connect(userId, token);
        clearTimeout(totalTimeout);

        // 连接成功后订阅价格更新
        websocketService.subscribePrices();
        console.log('WebSocket连接和订阅成功');

        // 请求通知权限
        if ('Notification' in window) {
          try {
            await Notification.requestPermission();
          } catch (notifyError) {
            console.error('通知权限请求失败:', notifyError);
          }
        }

      } catch (error) {
        console.error('WebSocket连接失败（非阻塞）:', error);
        // 连接失败不影响应用正常使用
      }
    }, 2000); // 延迟2秒连接，确保页面已完全加载
  };

  // 初始化认证状态
  useEffect(() => {
    const initAuth = async () => {
      console.log('开始初始化认证状态...');

      try {
        const token = localStorage.getItem('token');
        const userData = localStorage.getItem('user');

        if (token && userData) {
          console.log('发现本地token，直接使用...');

          // 直接使用本地数据，不验证token（避免阻塞）
          const parsedUser = JSON.parse(userData);
          setUser(parsedUser);
          setIsAuthenticated(true);
          console.log('用户认证成功:', parsedUser.username);

          // 后台验证token（非阻塞）
          setTimeout(async () => {
            try {
              console.log('后台验证token...');
              await authAPI.verifyToken();
              console.log('Token验证成功');

              // 安全的WebSocket连接（非阻塞）
              connectWebSocketSafely(parsedUser.id, token);
            } catch (verifyError) {
              console.error('Token验证失败:', verifyError);
              // Token无效，清除状态
              localStorage.removeItem('token');
              localStorage.removeItem('user');
              setUser(null);
              setIsAuthenticated(false);
              window.location.href = '/login';
            }
          }, 100);
        }
      } catch (error) {
        console.error('认证初始化错误:', error);
      } finally {
        console.log('认证初始化完成');
        setLoading(false);
      }
    };

    initAuth();
  }, []);

  // 登录
  const login = async (credentials) => {
    try {
      setLoading(true);
      const response = await authAPI.login(credentials);
      const { user: userData, token } = response.data.data;

      // 保存到本地存储
      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify(userData));

      setUser(userData);
      setIsAuthenticated(true);

      // 安全的WebSocket连接（非阻塞）
      connectWebSocketSafely(userData.id, token);

      toast.success('登录成功');

      return { success: true };
    } catch (error) {
      const message = error.response?.data?.message || '登录失败';
      toast.error(message);
      return { success: false, message };
    } finally {
      setLoading(false);
    }
  };

  // 注册
  const register = async (userData) => {
    try {
      setLoading(true);
      const response = await authAPI.register(userData);
      const { user: newUser, token } = response.data.data;

      // 保存到本地存储
      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify(newUser));

      setUser(newUser);
      setIsAuthenticated(true);
      toast.success('注册成功');
      
      return { success: true };
    } catch (error) {
      const message = error.response?.data?.message || '注册失败';
      toast.error(message);
      return { success: false, message };
    } finally {
      setLoading(false);
    }
  };

  // 登出
  const logout = () => {
    // 断开WebSocket连接
    websocketService.disconnect();

    localStorage.removeItem('token');
    localStorage.removeItem('user');
    setUser(null);
    setIsAuthenticated(false);
    toast.success('已退出登录');
  };

  // 更新用户信息
  const updateUser = (userData) => {
    const updatedUser = { ...user, ...userData };
    setUser(updatedUser);
    localStorage.setItem('user', JSON.stringify(updatedUser));
  };

  // 刷新用户信息
  const refreshUser = async () => {
    try {
      const response = await authAPI.getProfile();
      const userData = response.data.data;
      updateUser(userData);
      return userData;
    } catch (error) {
      console.error('刷新用户信息失败:', error);
      return null;
    }
  };

  // 更新用户设置
  const updateSettings = async (settings) => {
    try {
      await authAPI.updateSettings(settings);
      updateUser(settings);
      toast.success('设置更新成功');
      return { success: true };
    } catch (error) {
      const message = error.response?.data?.message || '设置更新失败';
      toast.error(message);
      return { success: false, message };
    }
  };

  const value = {
    user,
    loading,
    isAuthenticated,
    login,
    register,
    logout,
    updateUser,
    refreshUser,
    updateSettings,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
