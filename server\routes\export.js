const express = require('express');
const router = express.Router();
const database = require('../config/database');
const { authenticateToken } = require('../middleware/auth');

// 导出交易记录为CSV
router.get('/trades/csv', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { startDate, endDate, status } = req.query;

    let query = `
      SELECT 
        t.id,
        t.asset,
        t.buy_price,
        t.buy_amount,
        t.buy_time,
        t.sell_price,
        t.sell_amount,
        t.sell_time,
        t.profit_loss,
        t.status,
        t.stop_loss,
        t.take_profit,
        t.is_compliant,
        s.action as signal_action,
        s.confidence as signal_confidence
      FROM trades t
      LEFT JOIN signals s ON t.signal_id = s.id
      WHERE t.user_id = ?
    `;
    
    const params = [userId];

    // 添加日期过滤
    if (startDate) {
      query += ' AND t.buy_time >= ?';
      params.push(startDate);
    }
    if (endDate) {
      query += ' AND t.buy_time <= ?';
      params.push(endDate);
    }

    // 添加状态过滤
    if (status && status !== 'all') {
      query += ' AND t.status = ?';
      params.push(status);
    }

    query += ' ORDER BY t.buy_time DESC';

    const trades = await database.all(query, params);

    // 生成CSV内容
    const csvHeaders = [
      '交易ID',
      '交易对',
      '买入价格',
      '买入金额',
      '买入时间',
      '卖出价格',
      '卖出金额',
      '卖出时间',
      '盈亏',
      '状态',
      '止损价',
      '止盈价',
      '是否遵守建议',
      '信号动作',
      '信号置信度'
    ];

    let csvContent = csvHeaders.join(',') + '\n';

    trades.forEach(trade => {
      const row = [
        trade.id,
        getAssetDisplayName(trade.asset),
        trade.buy_price || '',
        trade.buy_amount || '',
        formatDateTime(trade.buy_time),
        trade.sell_price || '',
        trade.sell_amount || '',
        trade.sell_time ? formatDateTime(trade.sell_time) : '',
        trade.profit_loss || '',
        trade.status === 'open' ? '持仓中' : '已平仓',
        trade.stop_loss || '',
        trade.take_profit || '',
        trade.is_compliant === null ? '' : (trade.is_compliant ? '是' : '否'),
        trade.signal_action || '',
        trade.signal_confidence || ''
      ];
      csvContent += row.map(field => `"${field}"`).join(',') + '\n';
    });

    // 设置响应头
    res.setHeader('Content-Type', 'text/csv; charset=utf-8');
    res.setHeader('Content-Disposition', `attachment; filename="trades_${new Date().toISOString().split('T')[0]}.csv"`);
    
    // 添加BOM以支持Excel中文显示
    res.write('\ufeff');
    res.end(csvContent);

  } catch (error) {
    console.error('导出交易记录失败:', error);
    res.status(500).json({
      success: false,
      message: '导出失败'
    });
  }
});

// 导出信号记录为CSV
router.get('/signals/csv', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { startDate, endDate, action } = req.query;

    let query = `
      SELECT 
        id,
        asset,
        action,
        price,
        confidence,
        rsi_value,
        ema_9,
        ema_21,
        position_size,
        stop_loss,
        take_profit,
        created_at
      FROM signals 
      WHERE user_id = ?
    `;
    
    const params = [userId];

    // 添加日期过滤
    if (startDate) {
      query += ' AND created_at >= ?';
      params.push(startDate);
    }
    if (endDate) {
      query += ' AND created_at <= ?';
      params.push(endDate);
    }

    // 添加动作过滤
    if (action && action !== 'all') {
      query += ' AND action = ?';
      params.push(action);
    }

    query += ' ORDER BY created_at DESC';

    const signals = await database.all(query, params);

    // 生成CSV内容
    const csvHeaders = [
      '信号ID',
      '交易对',
      '动作',
      '价格',
      '置信度',
      'RSI值',
      'EMA9',
      'EMA21',
      '建议仓位',
      '止损价',
      '止盈价',
      '生成时间'
    ];

    let csvContent = csvHeaders.join(',') + '\n';

    signals.forEach(signal => {
      const row = [
        signal.id,
        getAssetDisplayName(signal.asset),
        signal.action === 'buy' ? '买入' : '卖出',
        signal.price || '',
        signal.confidence || '',
        signal.rsi_value || '',
        signal.ema_9 || '',
        signal.ema_21 || '',
        signal.position_size || '',
        signal.stop_loss || '',
        signal.take_profit || '',
        formatDateTime(signal.created_at)
      ];
      csvContent += row.map(field => `"${field}"`).join(',') + '\n';
    });

    // 设置响应头
    res.setHeader('Content-Type', 'text/csv; charset=utf-8');
    res.setHeader('Content-Disposition', `attachment; filename="signals_${new Date().toISOString().split('T')[0]}.csv"`);
    
    // 添加BOM以支持Excel中文显示
    res.write('\ufeff');
    res.end(csvContent);

  } catch (error) {
    console.error('导出信号记录失败:', error);
    res.status(500).json({
      success: false,
      message: '导出失败'
    });
  }
});

// 生成统计报告
router.get('/report/pdf', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { period = 'month' } = req.query;

    // 获取统计数据
    const stats = await getStatistics(userId, period);
    
    // 生成简单的文本报告（实际项目中可以使用PDF库）
    const report = generateTextReport(stats, period);

    res.setHeader('Content-Type', 'text/plain; charset=utf-8');
    res.setHeader('Content-Disposition', `attachment; filename="report_${period}_${new Date().toISOString().split('T')[0]}.txt"`);
    
    res.end(report);

  } catch (error) {
    console.error('生成报告失败:', error);
    res.status(500).json({
      success: false,
      message: '生成报告失败'
    });
  }
});

// 辅助函数
function getAssetDisplayName(asset) {
  const assetNames = {
    'bitcoin': 'BTC/USDT',
    'ethereum': 'ETH/USDT',
    'binancecoin': 'BNB/USDT'
  };
  return assetNames[asset] || asset.toUpperCase();
}

function formatDateTime(dateString) {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    timeZone: 'Asia/Shanghai',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

async function getStatistics(userId, period) {
  // 计算日期范围
  const now = new Date();
  let startDate;
  
  switch (period) {
    case 'week':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case 'month':
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      break;
    case 'year':
      startDate = new Date(now.getFullYear(), 0, 1);
      break;
    default:
      startDate = new Date(0); // 全部时间
  }

  // 获取统计数据
  const totalTrades = await database.get(
    'SELECT COUNT(*) as count FROM trades WHERE user_id = ? AND buy_time >= ?',
    [userId, startDate.toISOString()]
  );

  const closedTrades = await database.get(
    'SELECT COUNT(*) as count FROM trades WHERE user_id = ? AND status = "closed" AND buy_time >= ?',
    [userId, startDate.toISOString()]
  );

  const winningTrades = await database.get(
    'SELECT COUNT(*) as count FROM trades WHERE user_id = ? AND status = "closed" AND profit_loss > 0 AND buy_time >= ?',
    [userId, startDate.toISOString()]
  );

  const totalProfit = await database.get(
    'SELECT SUM(profit_loss) as total FROM trades WHERE user_id = ? AND status = "closed" AND buy_time >= ?',
    [userId, startDate.toISOString()]
  );

  return {
    period,
    totalTrades: totalTrades.count,
    closedTrades: closedTrades.count,
    winningTrades: winningTrades.count,
    winRate: closedTrades.count > 0 ? (winningTrades.count / closedTrades.count * 100).toFixed(2) : 0,
    totalProfit: totalProfit.total || 0
  };
}

function generateTextReport(stats, period) {
  const periodNames = {
    week: '本周',
    month: '本月',
    year: '本年',
    all: '全部时间'
  };

  return `
ScalpAlert 交易报告 - ${periodNames[period] || period}
生成时间: ${new Date().toLocaleString('zh-CN')}

=== 交易统计 ===
总交易次数: ${stats.totalTrades}
已完成交易: ${stats.closedTrades}
盈利交易: ${stats.winningTrades}
胜率: ${stats.winRate}%
总盈亏: $${stats.totalProfit.toFixed(2)}

=== 风险分析 ===
平均每笔盈亏: $${stats.closedTrades > 0 ? (stats.totalProfit / stats.closedTrades).toFixed(2) : '0.00'}
最大回撤: 待计算
夏普比率: 待计算

=== 建议 ===
${stats.winRate < 50 ? '• 胜率偏低，建议优化交易策略' : '• 胜率良好，继续保持'}
${stats.totalProfit < 0 ? '• 总体亏损，需要重新评估风险管理' : '• 总体盈利，表现良好'}

报告结束
`;
}

module.exports = router;
