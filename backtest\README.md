# ScalpAlert 回测框架

## 概述

这是一个独立的回测框架，用于测试ScalpAlert交易策略在历史数据上的表现。框架设计为独立于后端服务运行，但可以复用后端的核心信号生成逻辑。

## 目录结构

```
backtest/
├── README.md              # 说明文档
├── data/                  # 历史数据存储
│   ├── downloader.js      # 数据下载脚本
│   └── BTCUSDT_1h.json   # 历史K线数据
├── engine/                # 回测引擎
│   ├── backtester.js      # 主回测引擎
│   ├── portfolio.js       # 资金管理
│   └── metrics.js         # 指标计算
├── adapters/              # 适配器
│   ├── signalAdapter.js   # 信号生成器适配
│   └── dataAdapter.js     # 数据适配器
├── config/                # 配置文件
│   └── backtest.config.js # 回测配置
└── examples/              # 示例
    └── run_backtest.js    # 运行示例
```

## 快速开始

### 1. 下载历史数据

```bash
cd backtest/data
node downloader.js
```

### 2. 运行回测

```bash
cd backtest/examples
node run_backtest.js
```

## 核心特性

- **独立运行**: 不依赖后端服务，可离线运行
- **复用逻辑**: 使用后端的信号生成算法
- **严防未来函数**: 确保回测的时间顺序正确性
- **完整指标**: 计算夏普比率、最大回撤等关键指标
- **多策略支持**: 支持不同的配置预设测试

## 回测指标

- 总回报率 (Total Return)
- 年化回报率 (Annualized Return)  
- 夏普比率 (Sharpe Ratio)
- 最大回撤 (Max Drawdown)
- 胜率 (Win Rate)
- 盈亏比 (Profit/Loss Ratio)
- 交易频率 (Trade Frequency)

## 注意事项

1. 确保有足够的历史数据（建议至少1-2年）
2. 考虑交易手续费和滑点
3. 避免过度拟合历史数据
4. 定期更新历史数据进行验证
