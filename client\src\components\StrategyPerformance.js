import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON>hart, Pie, Cell } from 'recharts';
import { TrendingUp, TrendingDown, Target, Award, Activity, RefreshCw } from 'lucide-react';
import { signalAPI, formatCurrency, formatPercentage } from '../utils/api';

const StrategyPerformance = () => {
  const [performance, setPerformance] = useState(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('30d');

  useEffect(() => {
    fetchPerformance();
  }, [timeRange]);

  const fetchPerformance = async () => {
    try {
      setLoading(true);
      const response = await signalAPI.getPerformance(timeRange);
      if (response.data.success) {
        setPerformance(response.data.data);
      }
    } catch (error) {
      console.error('获取策略表现失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 信号强度表现颜色
  const strengthColors = {
    'strong': '#10B981',
    'medium': '#F59E0B', 
    'weak': '#EF4444'
  };

  // 资产表现颜色
  const assetColors = ['#3B82F6', '#10B981', '#F59E0B', '#8B5CF6'];

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-center py-12">
          <RefreshCw className="animate-spin h-8 w-8 text-blue-600" />
          <span className="ml-2 text-gray-600">加载策略表现数据...</span>
        </div>
      </div>
    );
  }

  if (!performance) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-center py-12">
          <Target className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">暂无策略表现数据</h3>
          <p className="text-gray-600">请先生成一些信号并进行交易</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 时间范围选择 */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">策略表现分析</h3>
          <div className="flex space-x-2">
            {[
              { value: '7d', label: '7天' },
              { value: '30d', label: '30天' },
              { value: '90d', label: '90天' }
            ].map(option => (
              <button
                key={option.value}
                onClick={() => setTimeRange(option.value)}
                className={`px-3 py-1 rounded text-sm font-medium ${
                  timeRange === option.value
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* 关键指标卡片 */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <Target className="h-8 w-8 text-blue-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">信号总数</p>
              <p className="text-2xl font-bold text-gray-900">{performance.total_signals}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <Activity className="h-8 w-8 text-green-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">信号采用率</p>
              <p className="text-2xl font-bold text-gray-900">{performance.signal_adoption_rate}%</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <Award className="h-8 w-8 text-yellow-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">胜率</p>
              <p className="text-2xl font-bold text-gray-900">{performance.win_rate}%</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            {performance.net_profit >= 0 ? (
              <TrendingUp className="h-8 w-8 text-green-600" />
            ) : (
              <TrendingDown className="h-8 w-8 text-red-600" />
            )}
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">净盈利</p>
              <p className={`text-2xl font-bold ${
                performance.net_profit >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {formatCurrency(performance.net_profit)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 详细指标 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* 风险指标 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h4 className="text-lg font-medium text-gray-900 mb-4">风险指标</h4>
          <div className="space-y-4">
            <div className="flex justify-between">
              <span className="text-gray-600">盈亏比</span>
              <span className="font-medium">{performance.profit_loss_ratio}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">平均盈利</span>
              <span className="font-medium">{formatCurrency(performance.avg_profit)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">最大回撤</span>
              <span className="font-medium text-red-600">{formatCurrency(performance.max_drawdown)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">已完成交易</span>
              <span className="font-medium">{performance.closed_trades}</span>
            </div>
          </div>
        </div>

        {/* 信号强度表现 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h4 className="text-lg font-medium text-gray-900 mb-4">信号强度表现</h4>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={performance.strength_performance}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="strength" 
                  tickFormatter={(value) => ({
                    'strong': '强',
                    'medium': '中',
                    'weak': '弱'
                  }[value] || value)}
                />
                <YAxis />
                <Tooltip 
                  formatter={(value, name) => [
                    `${value}%`,
                    name === 'win_rate' ? '胜率' : name
                  ]}
                  labelFormatter={(label) => ({
                    'strong': '强信号',
                    'medium': '中等信号',
                    'weak': '弱信号'
                  }[label] || label)}
                />
                <Bar 
                  dataKey="win_rate" 
                  fill={(entry) => strengthColors[entry.strength] || '#6B7280'}
                  name="胜率"
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* 资产表现 */}
      <div className="bg-white rounded-lg shadow p-6">
        <h4 className="text-lg font-medium text-gray-900 mb-4">资产表现分析</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* 资产胜率饼图 */}
          <div>
            <h5 className="text-sm font-medium text-gray-700 mb-2">各资产胜率分布</h5>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={performance.asset_performance.filter(item => item.closed_trades > 0)}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    dataKey="win_rate"
                    nameKey="asset"
                  >
                    {performance.asset_performance.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={assetColors[index % assetColors.length]} />
                    ))}
                  </Pie>
                  <Tooltip 
                    formatter={(value, name) => [`${value}%`, '胜率']}
                    labelFormatter={(label) => ({
                      'bitcoin': 'BTC',
                      'ethereum': 'ETH',
                      'binancecoin': 'BNB',
                      'solana': 'SOL'
                    }[label] || label)}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* 资产详细表现 */}
          <div>
            <h5 className="text-sm font-medium text-gray-700 mb-2">详细表现</h5>
            <div className="space-y-3">
              {performance.asset_performance.map((asset, index) => (
                <div key={asset.asset} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                  <div className="flex items-center">
                    <div 
                      className="w-3 h-3 rounded-full mr-3"
                      style={{ backgroundColor: assetColors[index % assetColors.length] }}
                    />
                    <span className="font-medium">
                      {{
                        'bitcoin': 'BTC',
                        'ethereum': 'ETH',
                        'binancecoin': 'BNB',
                        'solana': 'SOL'
                      }[asset.asset] || asset.asset}
                    </span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">{asset.win_rate}% 胜率</div>
                    <div className="text-xs text-gray-500">
                      {asset.closed_trades} 笔交易
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StrategyPerformance;
