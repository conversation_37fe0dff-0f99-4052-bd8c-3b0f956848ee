import React from 'react';
import { useLocation } from 'react-router-dom';
import BottomNavigation from './BottomNavigation';
import Header from './Header';

const MobileLayout = ({ children }) => {
  const location = useLocation();
  
  // 不显示底部导航的页面
  const hideBottomNav = ['/login', '/register'].includes(location.pathname);
  
  // 不显示头部的页面
  const hideHeader = ['/login', '/register'].includes(location.pathname);

  return (
    <div className="mobile-container">
      {!hideHeader && <Header />}
      
      <main className={`${hideHeader ? 'pt-0' : 'pt-16'} ${hideBottomNav ? 'pb-0' : 'pb-20'}`}>
        {children}
      </main>
      
      {!hideBottomNav && <BottomNavigation />}
    </div>
  );
};

export default MobileLayout;
