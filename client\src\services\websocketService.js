class WebSocketService {
  constructor() {
    this.ws = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 5000;
    this.listeners = new Map();
    this.messageQueue = [];
    this.userId = null;
    this.token = null;
  }

  // 连接WebSocket（改进版，带超时和错误处理）
  connect(userId, token) {
    // 如果已经连接，直接返回
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      console.log('WebSocket已连接，跳过重复连接');
      return Promise.resolve();
    }

    // 如果正在连接，等待当前连接完成
    if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
      console.log('WebSocket正在连接中，等待完成');
      return new Promise((resolve, reject) => {
        const checkConnection = () => {
          if (this.ws.readyState === WebSocket.OPEN) {
            resolve();
          } else if (this.ws.readyState === WebSocket.CLOSED) {
            reject(new Error('连接失败'));
          } else {
            setTimeout(checkConnection, 100);
          }
        };
        setTimeout(checkConnection, 100);
      });
    }

    this.userId = userId;
    this.token = token;

    return new Promise((resolve, reject) => {
      // 设置连接超时
      const connectionTimeout = setTimeout(() => {
        if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
          this.ws.close();
          reject(new Error('WebSocket连接超时'));
        }
      }, 5000); // 5秒超时

      try {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const host = window.location.host;
        const wsUrl = `${protocol}//${host}/ws`;

        console.log('尝试连接WebSocket:', wsUrl);
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
          clearTimeout(connectionTimeout);
          console.log('WebSocket连接已建立');
          this.isConnected = true;
          this.reconnectAttempts = 0;

          // 发送认证消息
          this.send({
            type: 'auth',
            token: this.token,
            userId: this.userId
          });

          // 发送队列中的消息
          this.flushMessageQueue();

          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            this.handleMessage(data);
          } catch (error) {
            console.error('解析WebSocket消息失败:', error);
          }
        };

        this.ws.onclose = (event) => {
          clearTimeout(connectionTimeout);
          console.log('WebSocket连接已关闭:', event.code, event.reason);
          this.isConnected = false;

          // 如果不是主动关闭，尝试重连
          if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
          }
        };

        this.ws.onerror = (error) => {
          clearTimeout(connectionTimeout);
          console.error('WebSocket错误:', error);
          this.isConnected = false;
          reject(error);
        };

      } catch (error) {
        clearTimeout(connectionTimeout);
        console.error('创建WebSocket连接失败:', error);
        reject(error);
      }
    });
  }

  // 处理接收到的消息
  handleMessage(data) {
    const { type } = data;
    
    // 触发对应类型的监听器
    if (this.listeners.has(type)) {
      const callbacks = this.listeners.get(type);
      callbacks.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`处理WebSocket消息失败 (${type}):`, error);
        }
      });
    }

    // 处理特定消息类型
    switch (type) {
      case 'auth_success':
        console.log('WebSocket认证成功');
        break;
      case 'auth_error':
        console.error('WebSocket认证失败:', data.message);
        break;
      case 'price_update':
        this.handlePriceUpdate(data.data);
        break;
      case 'new_signal':
        this.handleNewSignal(data.data);
        break;
      case 'trade_update':
        this.handleTradeUpdate(data.data);
        break;
      default:
        console.log('收到未知类型的WebSocket消息:', type, data);
    }
  }

  // 处理价格更新
  handlePriceUpdate(priceData) {
    // 触发价格更新事件
    this.emit('priceUpdate', priceData);
  }

  // 处理新信号
  handleNewSignal(signal) {
    // 触发新信号事件
    this.emit('newSignal', signal);
    
    // 显示通知
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification('新的交易信号', {
        body: `${signal.asset} - ${signal.action}`,
        icon: '/favicon.ico'
      });
    }
  }

  // 处理交易更新
  handleTradeUpdate(trade) {
    // 触发交易更新事件
    this.emit('tradeUpdate', trade);
  }

  // 发送消息
  send(message) {
    if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      // 如果未连接，将消息加入队列
      this.messageQueue.push(message);
    }
  }

  // 发送队列中的消息
  flushMessageQueue() {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      this.send(message);
    }
  }

  // 订阅价格更新
  subscribePrices(assets = ['bitcoin', 'ethereum', 'binancecoin']) {
    this.send({
      type: 'subscribe_prices',
      assets: assets
    });
  }

  // 取消订阅价格更新
  unsubscribePrices() {
    this.send({
      type: 'unsubscribe_prices'
    });
  }

  // 添加事件监听器
  on(eventType, callback) {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, []);
    }
    this.listeners.get(eventType).push(callback);
  }

  // 移除事件监听器
  off(eventType, callback) {
    if (this.listeners.has(eventType)) {
      const callbacks = this.listeners.get(eventType);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  // 触发事件
  emit(eventType, data) {
    if (this.listeners.has(eventType)) {
      const callbacks = this.listeners.get(eventType);
      callbacks.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`事件处理失败 (${eventType}):`, error);
        }
      });
    }
  }

  // 计划重连
  scheduleReconnect() {
    this.reconnectAttempts++;
    console.log(`计划重连 WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
    
    setTimeout(() => {
      if (this.userId && this.token) {
        this.connect(this.userId, this.token).catch(error => {
          console.error('重连失败:', error);
        });
      }
    }, this.reconnectInterval);
  }

  // 断开连接
  disconnect() {
    if (this.ws) {
      this.isConnected = false;
      this.ws.close(1000, '主动断开连接');
      this.ws = null;
    }
    this.messageQueue = [];
    this.listeners.clear();
  }

  // 获取连接状态
  getConnectionState() {
    if (!this.ws) return 'DISCONNECTED';
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return 'CONNECTING';
      case WebSocket.OPEN:
        return 'CONNECTED';
      case WebSocket.CLOSING:
        return 'CLOSING';
      case WebSocket.CLOSED:
        return 'DISCONNECTED';
      default:
        return 'UNKNOWN';
    }
  }

  // 请求通知权限
  static async requestNotificationPermission() {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    }
    return false;
  }
}

// 创建单例实例
const websocketService = new WebSocketService();

export default websocketService;
