/**
 * 新策略回测脚本
 * 测试趋势跟踪和震荡交易两个全新策略
 */

const path = require('path');
const fs = require('fs');
const Backtester = require('../engine/backtester');
const DataAdapter = require('../adapters/dataAdapter');
const TrendFollowingStrategy = require('../strategies/trendFollowing');
const RangeTradingStrategy = require('../strategies/rangeTrading');
const { newStrategyConfigs, backtestConfig } = require('../config/newStrategies.config');

class NewStrategiesBacktest {
  constructor() {
    this.results = [];
    this.dataAdapter = new DataAdapter();
  }

  /**
   * 运行单个策略回测
   */
  async runSingleStrategy(strategyName) {
    console.log(`🎯 新策略回测系统`);
    console.log('='.repeat(50));
    
    try {
      const dataPath = path.join(__dirname, '../data/ETHUSDT_4h.json');
      
      // 加载数据
      console.log('📊 加载数据...');
      await this.dataAdapter.loadFromFile(dataPath);
      const dataLength = this.dataAdapter.getData().length;
      console.log(`✅ 数据加载完成: ${dataLength} 条K线数据\n`);
      
      // 获取策略配置
      const strategyConfig = newStrategyConfigs[strategyName];
      if (!strategyConfig) {
        throw new Error(`策略配置不存在: ${strategyName}`);
      }
      
      console.log(`🚀 开始回测: ${strategyConfig.name}`);
      console.log(`📝 策略描述: ${strategyConfig.description}`);
      console.log(`🎯 预期胜率: ${(strategyConfig.target_win_rate * 100).toFixed(0)}%`);
      console.log(`📈 预期盈亏比: ${strategyConfig.target_profit_factor}`);
      console.log(`⚠️ 预期回撤: ${(strategyConfig.target_max_drawdown * 100).toFixed(0)}%\n`);
      
      // 创建策略实例
      const strategy = this.createStrategyInstance(strategyConfig);
      
      // 创建回测引擎
      const backtester = new Backtester({
        initialCapital: backtestConfig.initialCapital,
        tradingFee: backtestConfig.tradingFee
      });
      
      backtester.setDataAdapter(this.dataAdapter);
      backtester.setSignalAdapter({
        generateSignal: (_symbol, _config, index) => {
          const allData = this.dataAdapter.getData();
          const historicalData = allData.slice(0, index + 1);
          const currentCandle = allData[index];

          // 转换当前K线格式为策略期望的格式
          const formattedCandle = {
            timestamp: currentCandle[0],
            open: currentCandle[1],
            high: currentCandle[2],
            low: currentCandle[3],
            close: currentCandle[4],
            volume: currentCandle[5]
          };

          return strategy.generateSignal(historicalData, formattedCandle);
        }
      });
      
      // 运行回测
      const result = await backtester.runBacktest(strategyConfig);
      
      // 显示结果
      this.displaySingleResult(result, strategyConfig);
      
      return result;
      
    } catch (error) {
      console.error('❌ 回测失败:', error.message);
      throw error;
    }
  }

  /**
   * 运行策略对比回测
   */
  async runComparisonBacktest() {
    console.log(`🎯 新策略对比回测`);
    console.log('='.repeat(50));
    
    const strategies = ['trendFollowing', 'rangeTrading'];
    const results = [];
    
    try {
      const dataPath = path.join(__dirname, '../data/ETHUSDT_4h.json');
      
      // 加载数据
      console.log('📊 加载数据...');
      await this.dataAdapter.loadFromFile(dataPath);
      console.log(`✅ 数据加载完成: ${this.dataAdapter.getData().length} 条K线数据\n`);
      
      // 逐个测试策略
      for (const strategyName of strategies) {
        console.log(`🧪 测试策略: ${strategyName}...`);
        
        try {
          const strategyConfig = newStrategyConfigs[strategyName];
          const strategy = this.createStrategyInstance(strategyConfig);
          
          const backtester = new Backtester({
            initialCapital: backtestConfig.initialCapital,
            tradingFee: backtestConfig.tradingFee
          });
          
          backtester.setDataAdapter(this.dataAdapter);
          backtester.setSignalAdapter({
            generateSignal: (_symbol, _config, index) => {
              const allData = this.dataAdapter.getData();
              const historicalData = allData.slice(0, index + 1);
              const currentCandle = allData[index];

              // 转换当前K线格式为策略期望的格式
              const formattedCandle = {
                timestamp: currentCandle[0],
                open: currentCandle[1],
                high: currentCandle[2],
                low: currentCandle[3],
                close: currentCandle[4],
                volume: currentCandle[5]
              };

              return strategy.generateSignal(historicalData, formattedCandle);
            }
          });
          
          const result = await backtester.runBacktest(strategyConfig);
          
          results.push({
            name: strategyConfig.name,
            config: strategyConfig,
            metrics: result.metrics,
            trades: result.trades
          });
          
          console.log(`✅ ${strategyConfig.name} 完成`);
          
        } catch (error) {
          console.log(`❌ ${strategyName} 失败: ${error.message}`);
        }
      }
      
      // 显示对比结果
      this.displayComparisonResults(results);
      
      return results;
      
    } catch (error) {
      console.error('❌ 对比回测失败:', error.message);
      throw error;
    }
  }

  /**
   * 运行所有变种策略
   */
  async runAllVariants() {
    console.log(`🎯 全策略变种回测`);
    console.log('='.repeat(50));
    
    const allStrategies = [
      'trendFollowing',
      'trendFollowingConservative', 
      'rangeTrading',
      'rangeTradingAggressive'
    ];
    
    const results = [];
    
    try {
      const dataPath = path.join(__dirname, '../data/ETHUSDT_4h.json');
      
      // 加载数据
      console.log('📊 加载数据...');
      await this.dataAdapter.loadFromFile(dataPath);
      console.log(`✅ 数据加载完成: ${this.dataAdapter.getData().length} 条K线数据\n`);
      
      // 逐个测试策略
      for (const strategyName of allStrategies) {
        console.log(`🧪 测试: ${strategyName}...`);
        
        try {
          const strategyConfig = newStrategyConfigs[strategyName];
          const strategy = this.createStrategyInstance(strategyConfig);
          
          const backtester = new Backtester({
            initialCapital: backtestConfig.initialCapital,
            tradingFee: backtestConfig.tradingFee
          });
          
          backtester.setDataAdapter(this.dataAdapter);
          backtester.setSignalAdapter({
            generateSignal: (_symbol, _config, index) => {
              const allData = this.dataAdapter.getData();
              const historicalData = allData.slice(0, index + 1);
              const currentCandle = allData[index];

              // 转换当前K线格式为策略期望的格式
              const formattedCandle = {
                timestamp: currentCandle[0],
                open: currentCandle[1],
                high: currentCandle[2],
                low: currentCandle[3],
                close: currentCandle[4],
                volume: currentCandle[5]
              };

              return strategy.generateSignal(historicalData, formattedCandle);
            }
          });
          
          const result = await backtester.runBacktest(strategyConfig);
          
          results.push({
            name: strategyConfig.name,
            type: strategyConfig.type,
            config: strategyConfig,
            metrics: result.metrics,
            trades: result.trades
          });
          
          console.log(`✅ ${strategyConfig.name} 完成`);
          
        } catch (error) {
          console.log(`❌ ${strategyName} 失败: ${error.message}`);
        }
      }
      
      // 显示全面对比结果
      this.displayAllVariantsResults(results);
      
      // 保存结果
      this.saveResults(results, 'all_variants');
      
      return results;
      
    } catch (error) {
      console.error('❌ 全策略回测失败:', error.message);
      throw error;
    }
  }

  /**
   * 创建策略实例
   */
  createStrategyInstance(config) {
    switch (config.type) {
      case 'trend_following':
        return new TrendFollowingStrategy(config);
      case 'range_trading':
        return new RangeTradingStrategy(config);
      default:
        throw new Error(`未知策略类型: ${config.type}`);
    }
  }

  /**
   * 显示单个策略结果
   */
  displaySingleResult(result, config) {
    const metrics = result.metrics;
    
    console.log('\n📊 回测结果');
    console.log('='.repeat(40));
    console.log(`策略名称: ${config.name}`);
    console.log(`总收益率: ${(metrics.totalReturn * 100).toFixed(2)}%`);
    console.log(`胜率: ${(metrics.winRate * 100).toFixed(2)}%`);
    console.log(`盈亏比: ${metrics.profitFactor.toFixed(2)}`);
    console.log(`最大回撤: ${(metrics.maxDrawdown * 100).toFixed(2)}%`);
    console.log(`夏普比率: ${metrics.sharpeRatio.toFixed(2)}`);
    console.log(`总交易数: ${metrics.totalTrades}`);
    console.log(`盈利交易: ${metrics.winningTrades}`);
    console.log(`亏损交易: ${metrics.losingTrades}`);
    
    // 与预期对比
    console.log('\n🎯 目标达成情况:');
    console.log(`胜率: ${metrics.winRate >= config.target_win_rate ? '✅' : '❌'} ${(metrics.winRate * 100).toFixed(1)}% / ${(config.target_win_rate * 100).toFixed(0)}%`);
    console.log(`盈亏比: ${metrics.profitFactor >= config.target_profit_factor ? '✅' : '❌'} ${metrics.profitFactor.toFixed(2)} / ${config.target_profit_factor}`);
    console.log(`回撤: ${metrics.maxDrawdown <= config.target_max_drawdown ? '✅' : '❌'} ${(metrics.maxDrawdown * 100).toFixed(1)}% / ${(config.target_max_drawdown * 100).toFixed(0)}%`);
  }

  /**
   * 显示对比结果
   */
  displayComparisonResults(results) {
    console.log('\n📊 策略对比结果');
    console.log('='.repeat(80));
    console.log('策略名称           收益%      胜率%     盈亏比     回撤%     交易数     夏普比');
    console.log('-'.repeat(80));
    
    results.forEach(result => {
      const metrics = result.metrics;
      console.log(
        `${result.name.padEnd(15)} ` +
        `${(metrics.totalReturn * 100).toFixed(1).padStart(8)} ` +
        `${(metrics.winRate * 100).toFixed(1).padStart(8)} ` +
        `${metrics.profitFactor.toFixed(2).padStart(8)} ` +
        `${(metrics.maxDrawdown * 100).toFixed(1).padStart(8)} ` +
        `${metrics.totalTrades.toString().padStart(8)} ` +
        `${metrics.sharpeRatio.toFixed(2).padStart(8)}`
      );
    });
    
    // 找出最佳策略
    if (results.length > 0) {
      const bestStrategy = results.reduce((best, current) => {
        const bestScore = this.calculateStrategyScore(best.metrics);
        const currentScore = this.calculateStrategyScore(current.metrics);
        return currentScore > bestScore ? current : best;
      });
      
      console.log(`\n🏆 最佳策略: ${bestStrategy.name}`);
      console.log(`   综合评分: ${this.calculateStrategyScore(bestStrategy.metrics).toFixed(1)}`);
    }
  }

  /**
   * 显示全变种结果
   */
  displayAllVariantsResults(results) {
    console.log('\n📊 全策略变种对比结果');
    console.log('='.repeat(90));
    console.log('策略名称                收益%      胜率%     盈亏比     回撤%     交易数     类型');
    console.log('-'.repeat(90));
    
    results.forEach(result => {
      const metrics = result.metrics;
      console.log(
        `${result.name.padEnd(20)} ` +
        `${(metrics.totalReturn * 100).toFixed(1).padStart(8)} ` +
        `${(metrics.winRate * 100).toFixed(1).padStart(8)} ` +
        `${metrics.profitFactor.toFixed(2).padStart(8)} ` +
        `${(metrics.maxDrawdown * 100).toFixed(1).padStart(8)} ` +
        `${metrics.totalTrades.toString().padStart(8)} ` +
        `${result.type.padStart(12)}`
      );
    });
    
    // 分类最佳策略
    const trendStrategies = results.filter(r => r.type === 'trend_following');
    const rangeStrategies = results.filter(r => r.type === 'range_trading');
    
    if (trendStrategies.length > 0) {
      const bestTrend = trendStrategies.reduce((best, current) => {
        return this.calculateStrategyScore(current.metrics) > this.calculateStrategyScore(best.metrics) ? current : best;
      });
      console.log(`\n🏆 最佳趋势策略: ${bestTrend.name}`);
    }
    
    if (rangeStrategies.length > 0) {
      const bestRange = rangeStrategies.reduce((best, current) => {
        return this.calculateStrategyScore(current.metrics) > this.calculateStrategyScore(best.metrics) ? current : best;
      });
      console.log(`🏆 最佳震荡策略: ${bestRange.name}`);
    }
  }

  /**
   * 计算策略综合评分
   */
  calculateStrategyScore(metrics) {
    const returnScore = metrics.totalReturn * 100; // 收益率权重
    const winRateScore = metrics.winRate * 50;     // 胜率权重
    const profitFactorScore = metrics.profitFactor * 20; // 盈亏比权重
    const drawdownPenalty = metrics.maxDrawdown * -100;  // 回撤惩罚
    
    return returnScore + winRateScore + profitFactorScore + drawdownPenalty;
  }

  /**
   * 保存结果到文件
   */
  saveResults(results, suffix = '') {
    try {
      const outputDir = './backtest_results';
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `new_strategies_${suffix}_${timestamp}.json`;
      const filepath = path.join(outputDir, filename);
      
      fs.writeFileSync(filepath, JSON.stringify(results, null, 2));
      console.log(`\n📁 结果已保存到: ${filepath}`);
    } catch (error) {
      console.error('❌ 保存结果失败:', error.message);
    }
  }
}

// 主函数
async function main() {
  try {
    const mode = process.argv[2] || 'compare';
    const backtester = new NewStrategiesBacktest();
    
    switch (mode) {
      case 'trend':
        await backtester.runSingleStrategy('trendFollowing');
        break;
      case 'range':
        await backtester.runSingleStrategy('rangeTrading');
        break;
      case 'compare':
        await backtester.runComparisonBacktest();
        break;
      case 'all':
        await backtester.runAllVariants();
        break;
      default:
        console.log('使用方法:');
        console.log('  node newStrategiesBacktest.js trend    - 测试趋势跟踪策略');
        console.log('  node newStrategiesBacktest.js range    - 测试震荡交易策略');
        console.log('  node newStrategiesBacktest.js compare  - 对比两个主要策略');
        console.log('  node newStrategiesBacktest.js all      - 测试所有策略变种');
    }
    
  } catch (error) {
    console.error('❌ 程序执行失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main();
}

module.exports = NewStrategiesBacktest;
