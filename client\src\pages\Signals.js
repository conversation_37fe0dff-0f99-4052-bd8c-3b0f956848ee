import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, RefreshCw, TrendingUp, AlertTriangle, Filter, BarChart3 } from 'lucide-react';
import { signalAPI } from '../utils/api';
import { formatCurrency, formatDate, getSignalStrengthColor, getSignalStrengthText, getAssetDisplayName } from '../utils/api';
import AutoSignalStatus from '../components/AutoSignalStatus';
import toast from 'react-hot-toast';

const Signals = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [signals, setSignals] = useState([]);
  const [filter, setFilter] = useState({
    asset: 'all',
    strength: 'all',
  });

  // 获取信号列表
  const fetchSignals = async () => {
    try {
      const params = {
        limit: 5,
        ...(filter.asset !== 'all' && { asset: filter.asset }),
      };
      
      const response = await signalAPI.getLatest(params);
      let signalData = response.data.data;

      // 客户端过滤信号强度
      if (filter.strength !== 'all') {
        signalData = signalData.filter(signal => signal.signal_strength === filter.strength);
      }

      setSignals(signalData);
    } catch (error) {
      console.error('获取信号失败:', error);
      toast.error('获取信号失败');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // 生成新信号
  const handleGenerateSignal = async () => {
    try {
      setRefreshing(true);
      const response = await signalAPI.generate({ 
        asset: filter.asset !== 'all' ? filter.asset : 'bitcoin' 
      });
      
      if (response.data.data) {
        toast.success('新信号已生成');
        fetchSignals();
      } else {
        toast(response.data.message, { icon: 'ℹ️' });
      }
    } catch (error) {
      console.error('生成信号失败:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // 刷新信号
  const handleRefresh = () => {
    setRefreshing(true);
    fetchSignals();
  };

  // 处理过滤器变化
  const handleFilterChange = (key, value) => {
    setFilter(prev => ({
      ...prev,
      [key]: value
    }));
  };

  useEffect(() => {
    fetchSignals();
  }, [filter]);

  if (loading) {
    return (
      <div className="mobile-content">
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="card">
              <div className="skeleton h-4 w-3/4 mb-2"></div>
              <div className="skeleton h-6 w-1/2 mb-2"></div>
              <div className="skeleton h-4 w-full"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="mobile-content">
      {/* 自动信号检测状态 */}
      <div className="mb-6">
        <AutoSignalStatus />
      </div>

      {/* 操作栏 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <button
            onClick={() => navigate('/analytics')}
            className="btn-outline px-3 py-2"
            title="查看详细的信号分析"
          >
            <BarChart3 size={16} className="mr-1" />
            查看分析
          </button>
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="btn-outline px-3 py-2"
          >
            <RefreshCw size={16} className={`mr-1 ${refreshing ? 'animate-spin' : ''}`} />
            刷新
          </button>
        </div>
        <button
          onClick={handleGenerateSignal}
          disabled={refreshing}
          className="btn-primary px-4 py-2"
          title="手动立即检测一次信号"
        >
          <Plus size={16} className="mr-1" />
          手动检测
        </button>
      </div>

      {/* 过滤器 */}
      <div className="card mb-6">
        <div className="flex items-center mb-4">
          <Filter size={16} className="mr-2 text-gray-600" />
          <span className="font-medium text-gray-900">筛选条件</span>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          {/* 资产筛选 */}
          <div>
            <label className="label text-sm">交易对</label>
            <select
              value={filter.asset}
              onChange={(e) => handleFilterChange('asset', e.target.value)}
              className="input text-sm"
            >
              <option value="all">全部</option>
              <option value="bitcoin">BTC</option>
              <option value="ethereum">ETH</option>
              <option value="binancecoin">BNB</option>
              <option value="solana">SOL</option>
            </select>
          </div>

          {/* 信号强度筛选 */}
          <div>
            <label className="label text-sm">信号强度</label>
            <select
              value={filter.strength}
              onChange={(e) => handleFilterChange('strength', e.target.value)}
              className="input text-sm"
            >
              <option value="all">全部</option>
              <option value="strong">强</option>
              <option value="medium">中</option>
              <option value="weak">弱</option>
            </select>
          </div>
        </div>
      </div>

      {/* 信号列表 */}
      {signals.length > 0 ? (
        <div className="space-y-4">
          {signals.map((signal) => (
            <div
              key={signal.id}
              onClick={() => navigate(`/signals/${signal.id}`)}
              className={`signal-card ${
                signal.signal_strength === 'strong' ? 'signal-strength-high' :
                signal.signal_strength === 'medium' ? 'signal-strength-medium' :
                'signal-strength-low'
              }`}
            >
              {/* 信号头部 */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                    <TrendingUp className="text-primary-600" size={20} />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">
                      {getAssetDisplayName(signal.asset)}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {signal.signal_type === 'buy' ? '买入信号' : '卖出信号'}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <span className={`badge ${getSignalStrengthColor(signal.signal_strength)}`}>
                    {getSignalStrengthText(signal.signal_strength)}
                  </span>
                  <p className="text-xs text-gray-500 mt-1">
                    {formatDate(signal.timestamp)}
                  </p>
                </div>
              </div>

              {/* 信号详情 */}
              <div className="grid grid-cols-2 gap-4 mb-3">
                <div>
                  <p className="text-sm text-gray-600">当前价格</p>
                  <p className="font-semibold text-gray-900">
                    ${signal.price?.toLocaleString()}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">建议仓位</p>
                  <p className="font-semibold text-primary-600">
                    {formatCurrency(signal.suggested_position)}
                  </p>
                </div>
              </div>

              {/* 技术指标 */}
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>
                  <p className="text-gray-600">RSI</p>
                  <p className="font-medium text-gray-900">
                    {signal.rsi?.toFixed(1)}
                  </p>
                </div>
                <div>
                  <p className="text-gray-600">4H趋势</p>
                  <p className={`font-medium ${
                    signal.trend_4h === 'up' ? 'text-success-600' :
                    signal.trend_4h === 'down' ? 'text-danger-600' :
                    'text-gray-600'
                  }`}>
                    {signal.trend_4h === 'up' ? '上涨' :
                     signal.trend_4h === 'down' ? '下跌' : '横盘'}
                  </p>
                </div>
                <div>
                  <p className="text-gray-600">形态</p>
                  <p className="font-medium text-gray-900">
                    {signal.pattern === 'MHead' ? 'M头' :
                     signal.pattern === 'WBottom' ? 'W底' : '无'}
                  </p>
                </div>
              </div>

              {/* 风险提示 */}
              {signal.pattern !== 'None' && (
                <div className="mt-3 p-2 bg-warning-50 border border-warning-200 rounded-lg">
                  <div className="flex items-center">
                    <AlertTriangle className="text-warning-600 mr-2" size={16} />
                    <p className="text-sm text-warning-800">
                      检测到{signal.pattern === 'MHead' ? 'M头' : 'W底'}形态，建议谨慎操作
                    </p>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <TrendingUp className="mx-auto text-gray-400 mb-4" size={64} />
          <h3 className="text-lg font-medium text-gray-900 mb-2">暂无信号</h3>
          <p className="text-gray-600 mb-6">
            {filter.asset !== 'all' || filter.strength !== 'all' 
              ? '当前筛选条件下没有找到信号' 
              : '还没有生成任何交易信号'}
          </p>
          <button
            onClick={handleGenerateSignal}
            disabled={refreshing}
            className="btn-primary"
          >
            <Plus size={20} className="mr-2" />
            生成第一个信号
          </button>
        </div>
      )}
    </div>
  );
};

export default Signals;
