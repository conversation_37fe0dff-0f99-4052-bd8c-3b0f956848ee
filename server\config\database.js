const sqlite3 = require('sqlite3').verbose();
const path = require('path');
require('dotenv').config();

class Database {
  constructor() {
    this.db = null;
    this.connect();
  }

  connect() {
    this.dbPath = process.env.DB_PATH || path.join(__dirname, '../data/scalpalert.db');

    this.db = new sqlite3.Database(this.dbPath, (err) => {
      if (err) {
        console.error('数据库连接失败:', err.message);
      } else {
        console.log('已连接到 SQLite 数据库');

        // 优化SQLite配置
        this.db.serialize(() => {
          // 启用外键约束
          this.db.run('PRAGMA foreign_keys = ON');

          // 启用WAL模式，提高并发性能
          this.db.run('PRAGMA journal_mode = WAL');

          // 设置同步模式为NORMAL，平衡性能和安全性
          this.db.run('PRAGMA synchronous = NORMAL');

          // 设置缓存大小（2MB）
          this.db.run('PRAGMA cache_size = -2000');

          // 设置临时存储为内存
          this.db.run('PRAGMA temp_store = MEMORY');

          // 设置忙等待超时（5秒）
          this.db.run('PRAGMA busy_timeout = 5000');
        });
      }
    });
  }

  getDatabasePath() {
    return this.dbPath;
  }

  // 执行查询
  query(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // 执行单条查询
  get(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.get(sql, params, (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  // 执行插入/更新/删除
  run(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({
            id: this.lastID,
            changes: this.changes
          });
        }
      });
    });
  }

  // 开始事务
  beginTransaction() {
    return this.run('BEGIN TRANSACTION');
  }

  // 提交事务
  commit() {
    return this.run('COMMIT');
  }

  // 回滚事务
  rollback() {
    return this.run('ROLLBACK');
  }

  // 关闭数据库连接
  close() {
    return new Promise((resolve, reject) => {
      this.db.close((err) => {
        if (err) {
          reject(err);
        } else {
          console.log('数据库连接已关闭');
          resolve();
        }
      });
    });
  }
}

// 创建单例实例
const database = new Database();

module.exports = database;
