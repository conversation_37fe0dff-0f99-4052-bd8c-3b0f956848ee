const axios = require('axios');
const database = require('../config/database');
const signalAnalyzer = require('./signalAnalyzer');
require('dotenv').config();

class SignalService {
  constructor() {
    this.binanceUrl = process.env.BINANCE_API_URL || 'https://api.binance.com/api/v3';
    this.requestDelay = 100; // Binance API更宽松，100ms间隔
    this.lastRequestTime = 0;

    // 币种映射：CoinGecko ID -> Binance Symbol
    this.symbolMap = {
      'bitcoin': 'BTCUSDT',
      'ethereum': 'ETHUSDT',
      'binancecoin': 'BNBUSDT',
      'solana': 'SOLUSDT'
    };
  }

  // 等待请求间隔
  async waitForRateLimit() {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    if (timeSinceLastRequest < this.requestDelay) {
      await new Promise(resolve => setTimeout(resolve, this.requestDelay - timeSinceLastRequest));
    }
    this.lastRequestTime = Date.now();
  }

  // 获取价格数据 (使用Binance API)
  async getPriceData(coinId = 'bitcoin') {
    try {
      await this.waitForRateLimit();

      const symbol = this.symbolMap[coinId];
      if (!symbol) {
        throw new Error(`不支持的币种: ${coinId}`);
      }

      // 获取24小时价格统计
      const response = await axios.get(`${this.binanceUrl}/ticker/24hr`, {
        params: { symbol },
        timeout: 10000,
        headers: {
          'User-Agent': 'ScalpAlert/1.0'
        }
      });

      const data = response.data;

      return {
        usd: parseFloat(data.lastPrice),
        usd_24h_change: parseFloat(data.priceChangePercent),
        usd_24h_vol: parseFloat(data.volume) * parseFloat(data.lastPrice) // 转换为USD交易量
      };
    } catch (error) {
      console.error('获取价格数据失败:', error.message);

      // 如果是429错误（频率限制），等待后重试
      if (error.response && error.response.status === 429) {
        console.log('API频率限制，等待5秒后重试...');
        await new Promise(resolve => setTimeout(resolve, 5000));
        return this.getPriceData(coinId);
      }

      throw error;
    }
  }



  // 获取历史价格数据用于技术分析 (使用Binance API)
  async getHistoricalData(coinId = 'bitcoin', days = 7) {
    try {
      await this.waitForRateLimit();

      const symbol = this.symbolMap[coinId];
      if (!symbol) {
        throw new Error(`不支持的币种: ${coinId}`);
      }

      // 计算K线间隔和数量
      let interval = '1h'; // 1小时K线
      let limit = days * 24; // 每天24根K线

      if (days <= 1) {
        interval = '15m'; // 15分钟K线
        limit = days * 24 * 4; // 每小时4根K线
      } else if (days > 30) {
        interval = '1d'; // 日K线
        limit = days;
      }

      // 限制最大数量
      limit = Math.min(limit, 1000);

      const response = await axios.get(`${this.binanceUrl}/klines`, {
        params: {
          symbol,
          interval,
          limit
        },
        timeout: 10000,
        headers: {
          'User-Agent': 'ScalpAlert/1.0'
        }
      });

      // 转换Binance K线数据格式为完整的OHLCV格式
      // [timestamp, open, high, low, close, volume]
      return response.data.map(kline => [
        kline[0],             // 0: 开盘时间 (timestamp)
        parseFloat(kline[1]), // 1: 开盘价 (open)
        parseFloat(kline[2]), // 2: 最高价 (high)
        parseFloat(kline[3]), // 3: 最低价 (low)
        parseFloat(kline[4]), // 4: 收盘价 (close)
        parseFloat(kline[5])  // 5: 成交量 (volume)
      ]);
    } catch (error) {
      console.error('获取历史数据失败:', error.message);

      // 如果是429错误（频率限制），等待后重试
      if (error.response && error.response.status === 429) {
        console.log('API频率限制，等待5秒后重试...');
        await new Promise(resolve => setTimeout(resolve, 5000));
        return this.getHistoricalData(coinId, days);
      }

      throw error;
    }
  }



  // 计算RSI指标
  calculateRSI(prices, period = 14) {
    if (prices.length < period + 1) {
      return null;
    }

    let gains = 0;
    let losses = 0;

    // 计算初始平均收益和损失
    for (let i = 1; i <= period; i++) {
      const change = prices[i][4] - prices[i - 1][4]; // 使用收盘价 [4]
      if (change > 0) {
        gains += change;
      } else {
        losses += Math.abs(change);
      }
    }

    let avgGain = gains / period;
    let avgLoss = losses / period;

    // 计算后续的平均收益和损失
    for (let i = period + 1; i < prices.length; i++) {
      const change = prices[i][4] - prices[i - 1][4]; // 使用收盘价 [4]
      if (change > 0) {
        avgGain = (avgGain * (period - 1) + change) / period;
        avgLoss = (avgLoss * (period - 1)) / period;
      } else {
        avgGain = (avgGain * (period - 1)) / period;
        avgLoss = (avgLoss * (period - 1) + Math.abs(change)) / period;
      }
    }

    if (avgLoss === 0) return 100;
    
    const rs = avgGain / avgLoss;
    const rsi = 100 - (100 / (1 + rs));
    
    return Math.round(rsi * 100) / 100;
  }

  // 计算EMA指标
  calculateEMA(prices, period) {
    if (prices.length < period) {
      return null;
    }

    const multiplier = 2 / (period + 1);
    let ema = prices[0][4]; // 初始值为第一个收盘价 [4]

    for (let i = 1; i < prices.length; i++) {
      ema = (prices[i][4] * multiplier) + (ema * (1 - multiplier)); // 使用收盘价 [4]
    }

    return Math.round(ema * 100) / 100;
  }

  // 检测M头/W底形态
  detectPattern(prices) {
    if (prices.length < 20) {
      return 'None';
    }

    const recentPrices = prices.slice(-20).map(p => p[4]); // 使用收盘价 [4]
    const maxPrice = Math.max(...recentPrices);
    const minPrice = Math.min(...recentPrices);
    const priceRange = maxPrice - minPrice;
    
    // 简化的形态识别逻辑
    const lastPrice = recentPrices[recentPrices.length - 1];
    const midPrice = (maxPrice + minPrice) / 2;
    
    // 检测双顶(M头)
    if (lastPrice > midPrice && priceRange > lastPrice * 0.02) {
      const peaks = this.findPeaks(recentPrices);
      if (peaks.length >= 2) {
        return 'MHead';
      }
    }
    
    // 检测双底(W底)
    if (lastPrice < midPrice && priceRange > lastPrice * 0.02) {
      const valleys = this.findValleys(recentPrices);
      if (valleys.length >= 2) {
        return 'WBottom';
      }
    }
    
    return 'None';
  }

  // 寻找峰值
  findPeaks(prices) {
    const peaks = [];
    for (let i = 1; i < prices.length - 1; i++) {
      if (prices[i] > prices[i - 1] && prices[i] > prices[i + 1]) {
        peaks.push(i);
      }
    }
    return peaks;
  }

  // 寻找谷值
  findValleys(prices) {
    const valleys = [];
    for (let i = 1; i < prices.length - 1; i++) {
      if (prices[i] < prices[i - 1] && prices[i] < prices[i + 1]) {
        valleys.push(i);
      }
    }
    return valleys;
  }

  // 确定4小时趋势
  determine4hTrend(prices) {
    if (prices.length < 4) {
      return 'sideways';
    }

    const recent4h = prices.slice(-4);
    const firstPrice = recent4h[0][4]; // 使用收盘价 [4]
    const lastPrice = recent4h[recent4h.length - 1][4]; // 使用收盘价 [4]
    const changePercent = ((lastPrice - firstPrice) / firstPrice) * 100;

    if (changePercent > 1) {
      return 'up';
    } else if (changePercent < -1) {
      return 'down';
    } else {
      return 'sideways';
    }
  }

  // 计算信号强度
  calculateSignalStrength(rsi, pattern, trend4h) {
    let strength = 'medium';

    // 基于RSI的强度判断
    if (rsi < 25 || rsi > 75) {
      strength = 'high';
    } else if (rsi < 30 || rsi > 70) {
      strength = 'medium';
    } else {
      strength = 'low';
    }

    // 形态调整
    if (pattern === 'MHead' || pattern === 'WBottom') {
      if (strength === 'high') strength = 'medium';
      if (strength === 'medium') strength = 'low';
    }

    return strength;
  }

  // 计算成交量比率
  calculateVolumeRatio(historicalData) {
    if (historicalData.length < 20) {
      return 1.0; // 数据不足，返回默认值
    }

    // 获取最近20期的成交量数据
    const recentVolumes = historicalData.slice(-20).map(item => item[5]); // 成交量在第6个位置 [5]
    const currentVolume = recentVolumes[recentVolumes.length - 1];

    // 计算平均成交量（排除当前期）
    const avgVolume = recentVolumes.slice(0, -1).reduce((sum, vol) => sum + vol, 0) / (recentVolumes.length - 1);

    // 返回成交量比率
    return avgVolume > 0 ? currentVolume / avgVolume : 1.0;
  }

  // 计算MACD指标
  calculateMACD(historicalData, fastPeriod = 12, slowPeriod = 26, signalPeriod = 9) {
    if (historicalData.length < slowPeriod + signalPeriod) {
      return { macd: 0, signal: 0, histogram: 0 };
    }

    const prices = historicalData.map(d => d[4]); // 收盘价

    // 计算快速和慢速EMA
    const fastEMA = this.calculateEMAFromPrices(prices, fastPeriod);
    const slowEMA = this.calculateEMAFromPrices(prices, slowPeriod);

    // 计算MACD线
    const macdLine = fastEMA - slowEMA;

    // 计算信号线（MACD的EMA）
    const macdHistory = [];
    for (let i = slowPeriod - 1; i < prices.length; i++) {
      const fastEMAValue = this.calculateEMAFromPrices(prices.slice(0, i + 1), fastPeriod);
      const slowEMAValue = this.calculateEMAFromPrices(prices.slice(0, i + 1), slowPeriod);
      macdHistory.push(fastEMAValue - slowEMAValue);
    }

    const signalLine = this.calculateEMAFromPrices(macdHistory, signalPeriod);

    // 计算柱状线
    const histogram = macdLine - signalLine;

    return {
      macd: macdLine,
      signal: signalLine,
      histogram: histogram
    };
  }

  // 从价格数组计算EMA
  calculateEMAFromPrices(prices, period) {
    if (prices.length < period) return prices[prices.length - 1] || 0;

    const multiplier = 2 / (period + 1);
    let ema = prices.slice(0, period).reduce((sum, price) => sum + price, 0) / period;

    for (let i = period; i < prices.length; i++) {
      ema = (prices[i] * multiplier) + (ema * (1 - multiplier));
    }

    return ema;
  }

  // 计算ATR (Average True Range) 用于动态止损
  calculateATR(historicalData, period = 14) {
    if (historicalData.length < period + 1) return 0;

    const trueRanges = [];

    for (let i = 1; i < historicalData.length; i++) {
      const high = historicalData[i][2];
      const low = historicalData[i][3];
      const prevClose = historicalData[i-1][4];

      const tr1 = high - low;
      const tr2 = Math.abs(high - prevClose);
      const tr3 = Math.abs(low - prevClose);

      trueRanges.push(Math.max(tr1, tr2, tr3));
    }

    // 计算ATR（简单移动平均）
    const recentTR = trueRanges.slice(-period);
    return recentTR.reduce((sum, tr) => sum + tr, 0) / recentTR.length;
  }

  // 识别市场状态（趋势市 vs 震荡市）
  identifyMarketState(historicalData, period = 20) {
    if (historicalData.length < period) return 'unknown';

    const prices = historicalData.slice(-period).map(d => d[4]); // 收盘价
    const atr = this.calculateATR(historicalData, 14);
    const currentPrice = prices[prices.length - 1];

    // 计算价格通道
    const highest = Math.max(...prices);
    const lowest = Math.min(...prices);
    const priceRange = highest - lowest;
    const rangePercent = priceRange / currentPrice;

    // 计算趋势强度
    const ema20 = this.calculateEMAFromPrices(prices, 20);
    const pricePosition = (currentPrice - lowest) / priceRange;

    // 判断市场状态
    if (rangePercent < 0.05) {
      return 'consolidation'; // 盘整市
    } else if (pricePosition > 0.7 && currentPrice > ema20 * 1.02) {
      return 'uptrend'; // 上升趋势
    } else if (pricePosition < 0.3 && currentPrice < ema20 * 0.98) {
      return 'downtrend'; // 下降趋势
    } else {
      return 'ranging'; // 震荡市
    }
  }

  // 计算波动率指标
  calculateVolatility(historicalData, period = 20) {
    if (historicalData.length < period) return 0;

    const prices = historicalData.slice(-period).map(d => d[4]);
    const returns = [];

    for (let i = 1; i < prices.length; i++) {
      returns.push((prices[i] - prices[i-1]) / prices[i-1]);
    }

    // 计算标准差
    const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / returns.length;

    return Math.sqrt(variance) * Math.sqrt(252); // 年化波动率
  }

  // 自适应参数调整
  adaptiveParameters(historicalData, baseConfig) {
    const marketState = this.identifyMarketState(historicalData);
    const volatility = this.calculateVolatility(historicalData);

    const adaptedConfig = { ...baseConfig };

    // 根据市场状态调整参数
    switch (marketState) {
      case 'uptrend':
        // 上升趋势：放宽买入条件，严格卖出条件
        adaptedConfig.rsi_medium_threshold = baseConfig.rsi_medium_threshold + 5;
        adaptedConfig.min_quality_score = baseConfig.min_quality_score - 10;
        break;

      case 'downtrend':
        // 下降趋势：严格买入条件，放宽卖出条件
        adaptedConfig.rsi_medium_threshold = baseConfig.rsi_medium_threshold - 5;
        adaptedConfig.min_quality_score = baseConfig.min_quality_score + 10;
        break;

      case 'ranging':
        // 震荡市：使用标准参数但增加成交量要求
        adaptedConfig.volume_multiplier = baseConfig.volume_multiplier * 1.2;
        break;

      case 'consolidation':
        // 盘整市：提高质量要求，减少交易频率
        adaptedConfig.min_quality_score = baseConfig.min_quality_score + 15;
        adaptedConfig.volume_multiplier = baseConfig.volume_multiplier * 1.5;
        break;
    }

    // 根据波动率调整止损距离
    if (volatility > 0.5) {
      // 高波动率：增加止损距离
      adaptedConfig.atr_multiplier = 2.0;
    } else if (volatility < 0.2) {
      // 低波动率：减少止损距离
      adaptedConfig.atr_multiplier = 1.0;
    } else {
      // 正常波动率
      adaptedConfig.atr_multiplier = 1.5;
    }

    return {
      config: adaptedConfig,
      marketState,
      volatility: volatility.toFixed(4)
    };
  }

  // 多时间周期分析 (Multi-Timeframe Analysis)
  async analyzeMultiTimeframe(asset) {
    try {
      // 获取日线数据（用于判断大趋势）
      const dailyData = await this.getHistoricalData(asset, 30, '1d'); // 30天日线数据
      const hourlyData = await this.getHistoricalData(asset, 7); // 7天小时数据

      // 日线级别趋势分析
      const dailyTrend = this.analyzeDailyTrend(dailyData);

      // 4小时级别趋势分析
      const h4Trend = this.analyze4hTrend(hourlyData);

      // 1小时级别当前状态
      const h1State = this.analyzeHourlyState(hourlyData);

      return {
        daily: dailyTrend,
        h4: h4Trend,
        h1: h1State,
        recommendation: this.getMultiTimeframeRecommendation(dailyTrend, h4Trend, h1State)
      };
    } catch (error) {
      console.error('多时间周期分析失败:', error.message);
      return {
        daily: { trend: 'neutral', strength: 0 },
        h4: { trend: 'neutral', strength: 0 },
        h1: { trend: 'neutral', strength: 0 },
        recommendation: 'wait'
      };
    }
  }

  // 分析日线趋势
  analyzeDailyTrend(dailyData) {
    if (dailyData.length < 50) {
      return { trend: 'neutral', strength: 0, description: '数据不足' };
    }

    const prices = dailyData.map(d => d[4]); // 收盘价
    const currentPrice = prices[prices.length - 1];

    // 计算日线EMA
    const ema20 = this.calculateEMAFromPrices(prices, 20);
    const ema50 = this.calculateEMAFromPrices(prices, 50);

    // 趋势判断
    let trend = 'neutral';
    let strength = 0;
    let description = '';

    if (currentPrice > ema20 && ema20 > ema50) {
      trend = 'bullish';
      strength = Math.min(((currentPrice - ema20) / ema20) * 100, 10); // 最大10分
      description = '日线多头趋势，价格在EMA20和EMA50之上';
    } else if (currentPrice < ema20 && ema20 < ema50) {
      trend = 'bearish';
      strength = Math.min(((ema20 - currentPrice) / ema20) * 100, 10);
      description = '日线空头趋势，价格在EMA20和EMA50之下';
    } else {
      trend = 'neutral';
      strength = 0;
      description = '日线震荡趋势，价格在EMA之间';
    }

    return { trend, strength, description, ema20, ema50, currentPrice };
  }

  // 分析4小时趋势
  analyze4hTrend(hourlyData) {
    if (hourlyData.length < 20) {
      return { trend: 'neutral', strength: 0, description: '数据不足' };
    }

    // 每4个小时数据取一个点，构建4小时数据
    const h4Data = [];
    for (let i = 3; i < hourlyData.length; i += 4) {
      h4Data.push(hourlyData[i]);
    }

    if (h4Data.length < 12) {
      return { trend: 'neutral', strength: 0, description: '4小时数据不足' };
    }

    const prices = h4Data.map(d => d[4]);
    const currentPrice = prices[prices.length - 1];

    // 计算4小时EMA
    const ema12 = this.calculateEMAFromPrices(prices, 12);
    const ema26 = this.calculateEMAFromPrices(prices, 26);

    let trend = 'neutral';
    let strength = 0;
    let description = '';

    if (currentPrice > ema12 && ema12 > ema26) {
      trend = 'bullish';
      strength = Math.min(((currentPrice - ema12) / ema12) * 100, 5);
      description = '4小时多头趋势';
    } else if (currentPrice < ema12 && ema12 < ema26) {
      trend = 'bearish';
      strength = Math.min(((ema12 - currentPrice) / ema12) * 100, 5);
      description = '4小时空头趋势';
    } else {
      trend = 'neutral';
      strength = 0;
      description = '4小时震荡趋势';
    }

    return { trend, strength, description, ema12, ema26, currentPrice };
  }

  // 分析1小时状态
  analyzeHourlyState(hourlyData) {
    if (hourlyData.length < 9) {
      return { trend: 'neutral', strength: 0, description: '数据不足' };
    }

    const prices = hourlyData.map(d => d[4]);
    const currentPrice = prices[prices.length - 1];

    // 计算1小时EMA
    const ema9 = this.calculateEMAFromPrices(prices, 9);
    const ema21 = this.calculateEMAFromPrices(prices, 21);

    let trend = 'neutral';
    let strength = 0;
    let description = '';

    if (currentPrice > ema9 && ema9 > ema21) {
      trend = 'bullish';
      strength = Math.min(((currentPrice - ema9) / ema9) * 100, 3);
      description = '1小时多头排列';
    } else if (currentPrice < ema9 && ema9 < ema21) {
      trend = 'bearish';
      strength = Math.min(((ema9 - currentPrice) / ema9) * 100, 3);
      description = '1小时空头排列';
    } else {
      trend = 'neutral';
      strength = 0;
      description = '1小时震荡状态';
    }

    return { trend, strength, description, ema9, ema21, currentPrice };
  }

  // 获取多时间周期综合建议
  getMultiTimeframeRecommendation(daily, h4, h1) {
    // 多时间周期一致性检查
    const trends = [daily.trend, h4.trend, h1.trend];
    const bullishCount = trends.filter(t => t === 'bullish').length;
    const bearishCount = trends.filter(t => t === 'bearish').length;

    // 综合建议逻辑
    if (daily.trend === 'bullish' && h4.trend === 'bullish') {
      return 'long_only'; // 只做多
    } else if (daily.trend === 'bearish' && h4.trend === 'bearish') {
      return 'short_only'; // 只做空
    } else if (daily.trend === 'bullish' && h4.trend === 'neutral' && h1.trend === 'bullish') {
      return 'long_preferred'; // 偏向做多
    } else if (daily.trend === 'bearish' && h4.trend === 'neutral' && h1.trend === 'bearish') {
      return 'short_preferred'; // 偏向做空
    } else if (bullishCount >= 2) {
      return 'long_bias'; // 多头倾向
    } else if (bearishCount >= 2) {
      return 'short_bias'; // 空头倾向
    } else {
      return 'wait'; // 观望
    }
  }

  // 基于ATR的动态仓位计算
  calculateDynamicPosition(userCapital, currentPrice, atr, riskPercentage = 0.01, atrMultiplier = 1.5) {
    // 最大风险金额
    const maxRiskAmount = userCapital * riskPercentage;

    // 基于ATR的止损距离
    const stopLossDistance = atr * atrMultiplier;
    const stopLossPercent = stopLossDistance / currentPrice;

    // 根据止损距离计算仓位大小
    const positionSize = maxRiskAmount / stopLossDistance;
    const positionPercent = (positionSize * currentPrice) / userCapital;

    // 限制最大仓位（不超过20%）
    const maxPositionPercent = 0.20;
    const finalPositionPercent = Math.min(positionPercent, maxPositionPercent);

    return {
      positionPercent: finalPositionPercent,
      positionAmount: userCapital * finalPositionPercent,
      stopLossDistance: stopLossDistance,
      stopLossPercent: stopLossPercent,
      riskAmount: finalPositionPercent * stopLossPercent * userCapital
    };
  }

  // 计算建议仓位（保留原有接口）
  calculateSuggestedPosition(userCapital, rsi, signalStrength, pattern, riskPercentage = 0.01) {
    const maxRisk = userCapital * riskPercentage; // 最大风险金额
    const stopLossDistance = 0.02; // 假设2%止损距离

    // 基础仓位计算
    let basePosition = Math.min(
      maxRisk / stopLossDistance,
      userCapital * 0.2, // 最大20%本金
      userCapital * 0.1  // 最小10%本金（如果计算结果太小）
    );

    // 根据信号强度调整
    let strengthMultiplier = 1;
    switch (signalStrength) {
      case 'high':
        strengthMultiplier = 1.0;
        break;
      case 'medium':
        strengthMultiplier = 0.8;
        break;
      case 'low':
        strengthMultiplier = 0.6;
        break;
    }

    // 形态调整
    if (pattern === 'MHead' || pattern === 'WBottom') {
      strengthMultiplier *= 0.5;
    }

    const suggestedPosition = basePosition * strengthMultiplier;
    
    return {
      suggested_position: Math.round(suggestedPosition * 100) / 100,
      risk_amount: maxRisk,
      stop_loss_distance: stopLossDistance
    };
  }
}

module.exports = new SignalService();
