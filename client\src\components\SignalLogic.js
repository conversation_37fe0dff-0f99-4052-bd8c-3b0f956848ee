import React, { useState, useEffect } from 'react';
import { Info, TrendingUp, TrendingDown, Activity, Volume2, Target } from 'lucide-react';
import { signalAPI, signalConfigAPI } from '../utils/api';

const SignalLogic = ({ data = null }) => {
  const [currentData, setCurrentData] = useState({
    bitcoin: null,
    ethereum: null,
    binancecoin: null,
    solana: null
  });
  const [loading, setLoading] = useState(true);
  const [userConfig, setUserConfig] = useState(null);

  useEffect(() => {
    // 获取用户配置
    fetchUserConfig();

    // 如果传入了data prop，使用传入的数据，否则获取所有数据
    if (data) {
      setLoading(false);
      return;
    }

    fetchCurrentData();
    // 每30秒更新一次数据
    const interval = setInterval(fetchCurrentData, 30000);
    return () => clearInterval(interval);
  }, [data]);

  const fetchUserConfig = async () => {
    try {
      const response = await signalConfigAPI.getConfig();
      if (response.data.success) {
        setUserConfig(response.data.data);
      }
    } catch (error) {
      console.error('获取用户配置失败:', error);
      // 使用默认配置
      setUserConfig({
        rsi_strong_threshold: 30,
        rsi_medium_threshold: 40,
        risk_strong: 0.01,
        risk_medium: 0.008,
        risk_weak: 0.005,
        atr_multiplier: 1.5,
        risk_reward_ratio: 2.0,
        volume_multiplier: 1.5,
        enable_volume_filter: 1,
        enable_m_head_filter: 0,
        enable_w_bottom_bonus: 1,
        skip_weak_signals: 0,
        min_quality_score: 40
      });
    }
  };

  const fetchCurrentData = async () => {
    try {
      setLoading(true);
      const assets = ['bitcoin', 'ethereum', 'binancecoin', 'solana'];
      const promises = assets.map(asset => 
        signalAPI.getCurrentAnalysis(asset).catch(err => ({ asset, error: err.message }))
      );
      
      const results = await Promise.all(promises);
      const data = {};
      
      results.forEach(result => {
        if (result.data && result.data.success) {
          data[result.data.data.asset] = result.data.data;
        } else if (result.asset) {
          data[result.asset] = { error: result.error };
        }
      });
      
      setCurrentData(data);
    } catch (error) {
      console.error('获取当前分析数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const getSignalStrengthColor = (strength) => {
    switch (strength) {
      case 'strong': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'weak': return 'text-orange-600 bg-orange-100';
      case 'skip': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getSignalStrengthText = (strength) => {
    switch (strength) {
      case 'strong': return '强信号';
      case 'medium': return '中等信号';
      case 'weak': return '弱信号';
      case 'skip': return '跳过';
      default: return '未知';
    }
  };

  const getAssetDisplayName = (asset) => {
    const names = {
      'bitcoin': 'BTC',
      'ethereum': 'ETH',
      'binancecoin': 'BNB',
      'solana': 'SOL'
    };
    return names[asset] || asset.toUpperCase();
  };

  // 获取质量评分详细分解 (基于用户配置)
  const getQualityScoreBreakdown = (data) => {
    const breakdown = [];

    if (!data || !data.rsi || !userConfig) {
      breakdown.push(userConfig ? '数据不完整，无法计算详细评分' : '配置加载中...');
      return breakdown;
    }

    // RSI评分 (最高40分) - 基于用户配置的阈值
    let rsiScore = 0;
    const strongThreshold = userConfig.rsi_strong_threshold;
    const mediumThreshold = userConfig.rsi_medium_threshold;

    if (data.rsi < strongThreshold - 10) rsiScore = 40;
    else if (data.rsi < strongThreshold - 5) rsiScore = 35;
    else if (data.rsi < strongThreshold) rsiScore = 30;
    else if (data.rsi < strongThreshold + 5) rsiScore = 25;
    else if (data.rsi < mediumThreshold) rsiScore = 20;
    else if (data.rsi < mediumThreshold + 5) rsiScore = 15;
    else if (data.rsi < 50) rsiScore = 10;
    else rsiScore = 0;

    breakdown.push(`RSI评分: ${rsiScore}/40分 (RSI=${data.rsi.toFixed(1)}, 强信号阈值<${strongThreshold})`);

    // EMA评分 (最高30分)
    let emaScore = 10; // 基础分
    if (data.ema_short && data.ema_long) {
      if (data.ema_short > data.ema_long) {
        const divergence = (data.ema_short - data.ema_long) / data.ema_long;
        if (data.price > data.ema_short && divergence > 0.02) emaScore = 30;
        else if (data.price > data.ema_short && divergence > 0.01) emaScore = 25;
        else if (data.price > data.ema_short) emaScore = 20;
        else emaScore = 18;
      } else {
        emaScore = 5;
      }
    }

    breakdown.push(`EMA评分: ${emaScore}/30分 (${data.ema_short > data.ema_long ? '金叉' : '死叉'})`);

    // 趋势评分 (最高15分)
    let trendScore = 5; // 基础分
    if (data.trend_4h === 'up') trendScore = 15;
    else if (data.trend_4h === 'sideways') trendScore = 10;
    else trendScore = 0;

    breakdown.push(`趋势评分: ${trendScore}/15分 (4H趋势${data.trend_4h === 'up' ? '向上' : data.trend_4h === 'down' ? '向下' : '横盘'})`);

    // 成交量评分 (最高10分) - 基于用户配置的倍数
    let volumeScore = 5; // 基础分
    const volumeThreshold = userConfig.volume_multiplier;
    if (data.volume_ratio) {
      if (data.volume_ratio > volumeThreshold * 1.3) volumeScore = 10;
      else if (data.volume_ratio > volumeThreshold) volumeScore = 8;
      else if (data.volume_ratio > volumeThreshold * 0.8) volumeScore = 6;
      else if (data.volume_ratio < 0.8) volumeScore = 2;
    }

    breakdown.push(`成交量评分: ${volumeScore}/10分 (${data.volume_ratio?.toFixed(1) || 'N/A'}倍, 阈值>${volumeThreshold})`);

    // 形态评分 (最高5分) - 基于用户配置
    let patternScore = 0;
    if (userConfig.enable_w_bottom_bonus && data.pattern === 'WBottom') patternScore = 5;
    else if (userConfig.enable_m_head_filter && data.pattern === 'MHead') patternScore = -5;

    if (patternScore !== 0) {
      breakdown.push(`形态评分: ${patternScore > 0 ? '+' : ''}${patternScore}分 (${data.pattern === 'WBottom' ? 'W底加分' : 'M头减分'})`);
    } else {
      breakdown.push(`形态评分: 0分 (${data.pattern === 'None' ? '无特殊形态' : '形态过滤已禁用'})`);
    }

    // 显示配置影响
    breakdown.push(`配置影响: 最低质量要求${userConfig.min_quality_score}分`);

    // 计算总分验证
    const calculatedTotal = rsiScore + emaScore + trendScore + volumeScore + patternScore;
    const actualTotal = data.quality_score;

    if (actualTotal && Math.abs(calculatedTotal - actualTotal) > 5) {
      breakdown.push(`注: 实际评分${actualTotal}分，可能包含其他调整因子`);
    }

    return breakdown;
  };

  const renderAssetAnalysis = (asset, data) => {
    if (!data) {
      return (
        <div className="text-center py-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
        </div>
      );
    }

    if (data.error) {
      return (
        <div className="text-center py-4 text-red-600">
          <p className="text-sm">数据获取失败</p>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {/* 当前价格和信号强度 */}
        <div className="flex items-center justify-between">
          <div>
            <p className="text-lg font-semibold text-gray-900">
              ${data.price?.toFixed(2) || 'N/A'}
            </p>
            <p className={`text-sm ${data.price_change_24h >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {data.price_change_24h >= 0 ? '+' : ''}{data.price_change_24h?.toFixed(2) || 0}%
            </p>
          </div>
          <div className={`px-3 py-1 rounded-full text-sm font-medium ${getSignalStrengthColor(data.signal_strength)}`}>
            {getSignalStrengthText(data.signal_strength)}
          </div>
        </div>

        {/* 技术指标 */}
        <div className="grid grid-cols-2 gap-4">
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="flex items-center space-x-2 mb-1">
              <Activity className="text-blue-600" size={14} />
              <span className="text-xs font-medium text-gray-700">RSI</span>
            </div>
            <p className="text-lg font-semibold text-gray-900">
              {data.rsi?.toFixed(1) || 'N/A'}
            </p>
            <p className="text-xs text-gray-600">
              {data.rsi < 30 ? '超卖' : data.rsi > 70 ? '超买' : '正常'}
            </p>
          </div>

          <div className="bg-gray-50 rounded-lg p-3">
            <div className="flex items-center space-x-2 mb-1">
              <TrendingUp className="text-green-600" size={14} />
              <span className="text-xs font-medium text-gray-700">EMA趋势</span>
            </div>
            <p className="text-lg font-semibold text-gray-900">
              {data.ema_trend || 'N/A'}
            </p>
            <p className="text-xs text-gray-600">
              {data.ema_short > data.ema_long ? '多头排列' : '空头排列'}
            </p>
          </div>

          <div className="bg-gray-50 rounded-lg p-3">
            <div className="flex items-center space-x-2 mb-1">
              <Volume2 className="text-purple-600" size={14} />
              <span className="text-xs font-medium text-gray-700">成交量</span>
            </div>
            <p className="text-lg font-semibold text-gray-900">
              {data.volume_ratio?.toFixed(2) || 'N/A'}x
            </p>
            <p className="text-xs text-gray-600">
              {data.volume_ratio > 1.5 ? '放量' : '缩量'}
            </p>
          </div>

          <div className="bg-gray-50 rounded-lg p-3">
            <div className="flex items-center space-x-2 mb-1">
              <Target className="text-orange-600" size={14} />
              <span className="text-xs font-medium text-gray-700">质量评分</span>
            </div>
            <p className="text-lg font-semibold text-gray-900">
              {data.quality_score || 'N/A'}分
            </p>
            <p className="text-xs text-gray-600">
              {data.quality_score >= 80 ? '优秀' : data.quality_score >= 60 ? '良好' : '一般'}
            </p>
          </div>
        </div>

        {/* 质量评分详细计算 */}
        <div className="bg-orange-50 rounded-lg p-3 mt-3">
          <h5 className="text-sm font-medium text-orange-900 mb-2">评分计算详情</h5>
          <div className="space-y-1 text-xs text-orange-800">
            {getQualityScoreBreakdown(data).map((item, index) => (
              <p key={index}>• {item}</p>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 gap-3 mt-3">
        </div>

        {/* 信号逻辑说明 */}
        <div className="bg-blue-50 rounded-lg p-3">
          <h5 className="text-sm font-medium text-blue-900 mb-2">
            当前策略: {data.marketState?.state === 'ranging' ? '震荡市策略' :
                      data.marketState?.state === 'trending' ? '趋势市策略' : '保守策略'}
          </h5>
          <div className="space-y-1 text-xs text-blue-800">
            {data.marketState?.state === 'ranging' ? (
              <>
                <p>• 强买入: RSI &lt; 30 (超卖抄底)</p>
                <p>• 标准买入: RSI &lt; 40 + EMA金叉</p>
                <p>• 弱买入: RSI &lt; 50 + EMA金叉</p>
                <p>• 卖出: RSI &gt; 70 (超买高抛)</p>
              </>
            ) : data.marketState?.state === 'trending' ? (
              <>
                <p>• 顺势买入: 价格回调 + RSI非超买</p>
                <p>• MACD确认: 金叉信号加强</p>
                <p>• 趋势过滤: 多时间周期一致</p>
              </>
            ) : (
              <>
                <p>• 保守买入: 多重技术指标确认</p>
                <p>• 严格过滤: RSI + EMA + MACD + 趋势</p>
                <p>• 质量要求: 评分 ≥ 60分</p>
              </>
            )}
            <p>• 成交量确认: 放大 &gt; 1.5倍加强信号</p>
            <p>• 形态识别: W底加分，M头减分</p>
          </div>
        </div>

        {/* 建议操作或跳过原因 */}
        {data.signal_strength !== 'skip' && data.signal_type !== 'hold' && (
          <div className="bg-green-50 rounded-lg p-3">
            <h5 className="text-sm font-medium text-green-900 mb-2">建议操作</h5>
            <div className="space-y-1 text-xs text-green-800">
              <p>• 建议仓位: ${(data.suggested_position || 0).toLocaleString()}</p>
              <p>• 入场价格: ${data.entry_price?.toFixed(2) || 'N/A'}</p>
              <p>• 止损价格: ${data.stop_loss?.toFixed(2) || 'N/A'}</p>
              <p>• 止盈价格: ${data.take_profit?.toFixed(2) || 'N/A'}</p>
            </div>
          </div>
        )}

        {/* 跳过原因说明 */}
        {(data.signal_strength === 'skip' || data.signal_type === 'hold') && (
          <div className="bg-yellow-50 rounded-lg p-3">
            <h5 className="text-sm font-medium text-yellow-900 mb-2">
              {data.signal_strength === 'skip' ? '⚠️ 信号质量不足' : '⏸️ 暂不建议交易'}
            </h5>
            <div className="space-y-1 text-xs text-yellow-800">
              {data.signal_strength === 'skip' && (
                <>
                  <p>• 质量评分: {data.quality_score}分 (需要≥40分)</p>
                  <p>• 技术指标确认不足，建议等待更好机会</p>
                </>
              )}
              {data.signal_type === 'hold' && data.signal_reason && (
                <>
                  <p>• 策略判断: {data.signal_reason}</p>
                  <p>• 当前RSI: {data.rsi?.toFixed(1)} (震荡市需要更极端水平)</p>
                  <p>• 建议等待: RSI &lt; 50 或 RSI &gt; 70 的明确机会</p>
                </>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      {/* 标题 */}
      <div className="flex items-center space-x-3 mb-6">
        <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
          <Info className="text-blue-600" size={20} />
        </div>
        <div>
          <h3 className="font-semibold text-gray-900">当前市场分析</h3>
          <p className="text-sm text-gray-600">实时技术指标和信号检测逻辑</p>
        </div>
      </div>

      {/* 币种分析 */}
      {data ? (
        // 单个资产详细分析
        <div className="space-y-6">
          {renderAssetAnalysis('single', data)}
        </div>
      ) : (
        // 多个资产概览
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {Object.entries(currentData).map(([asset, assetData]) => (
            <div key={asset} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                  <span className="text-orange-600 font-bold text-sm">
                    {getAssetDisplayName(asset)}
                  </span>
                </div>
                <h4 className="font-medium text-gray-900">
                  {getAssetDisplayName(asset)}
                </h4>
              </div>
              {renderAssetAnalysis(asset, assetData)}
            </div>
          ))}
        </div>
      )}

      {/* 检测说明 */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h4 className="font-medium text-gray-900 mb-3">信号检测说明</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
          <div>
            <h5 className="font-medium text-gray-800 mb-2">技术指标权重</h5>
            <ul className="space-y-1">
              <li>• RSI指标: 40分 (超卖区域加分)</li>
              <li>• EMA交叉: 30分 (金叉加分)</li>
              <li>• 成交量: 30分 (放量加分)</li>
            </ul>
          </div>
          <div>
            <h5 className="font-medium text-gray-800 mb-2">形态识别</h5>
            <ul className="space-y-1">
              <li>• W底形态: +10分 (支撑强劲)</li>
              <li>• M头形态: -20分 (阻力较大)</li>
              <li>• 无明显形态: 0分</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignalLogic;
