const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const database = require('../config/database');
const notificationService = require('../services/notificationService');

const router = express.Router();

// 创建新交易（买入）
router.post('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { signalId, asset, buyPrice, buyAmount, stopLoss, takeProfit } = req.body;

    // 验证输入
    if (!asset || !buyPrice || !buyAmount || !stopLoss || !takeProfit) {
      return res.status(400).json({
        success: false,
        message: '缺少必要的交易参数'
      });
    }

    if (buyPrice <= 0 || buyAmount <= 0) {
      return res.status(400).json({
        success: false,
        message: '价格和金额必须大于0'
      });
    }

    // 获取用户信息
    const user = await database.get(
      'SELECT capital, max_daily_trades FROM users WHERE id = ?',
      [userId]
    );

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 检查今日交易次数
    const today = new Date().toISOString().split('T')[0];
    const todayTrades = await database.get(
      'SELECT COUNT(*) as count FROM trades WHERE user_id = ? AND DATE(buy_time) = ?',
      [userId, today]
    );

    if (todayTrades.count >= user.max_daily_trades) {
      return res.status(400).json({
        success: false,
        message: `今日交易次数已达上限 (${todayTrades.count}/${user.max_daily_trades})`
      });
    }

    // 检查资金是否充足
    if (buyAmount > user.capital) {
      return res.status(400).json({
        success: false,
        message: '资金不足'
      });
    }

    // 如果有信号ID，检查是否遵守建议仓位
    let isCompliant = null;
    if (signalId) {
      const signal = await database.get(
        'SELECT suggested_position FROM signals WHERE id = ? AND user_id = ?',
        [signalId, userId]
      );

      if (signal) {
        const deviation = Math.abs(buyAmount - signal.suggested_position) / signal.suggested_position;
        isCompliant = deviation <= 0.2; // 允许20%偏差

        // 如果偏差过大，发送警告
        if (!isCompliant) {
          await notificationService.sendRiskWarning(userId, 'position_deviation', {
            actual: buyAmount,
            suggested: signal.suggested_position
          });
        }
      }
    }

    // 创建交易记录
    const result = await database.run(
      `INSERT INTO trades (
        signal_id, user_id, asset, buy_price, buy_amount,
        stop_loss, take_profit, is_compliant
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [signalId, userId, asset, buyPrice, buyAmount, stopLoss, takeProfit, isCompliant]
    );

    // 更新用户本金（减去投入金额）
    await database.run(
      'UPDATE users SET capital = capital - ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [buyAmount, userId]
    );

    const trade = {
      id: result.id,
      signalId,
      asset,
      buyPrice,
      buyAmount,
      stopLoss,
      takeProfit,
      isCompliant,
      status: 'open',
      buyTime: new Date().toISOString()
    };

    res.status(201).json({
      success: true,
      message: '交易创建成功',
      data: trade
    });

  } catch (error) {
    console.error('创建交易失败:', error.message);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 更新交易（卖出）
router.put('/:id/sell', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const tradeId = req.params.id;
    const { sellPrice } = req.body;

    if (!sellPrice || sellPrice <= 0) {
      return res.status(400).json({
        success: false,
        message: '卖出价格必须大于0'
      });
    }

    // 获取交易信息
    const trade = await database.get(
      'SELECT * FROM trades WHERE id = ? AND user_id = ? AND status = "open"',
      [tradeId, userId]
    );

    if (!trade) {
      return res.status(404).json({
        success: false,
        message: '交易不存在或已关闭'
      });
    }

    // 计算盈亏
    const profitLoss = (sellPrice - trade.buy_price) * (trade.buy_amount / trade.buy_price);
    const sellTime = new Date().toISOString();

    // 更新交易记录
    await database.run(
      `UPDATE trades SET 
        sell_price = ?, sell_time = ?, profit_loss = ?, status = 'closed'
       WHERE id = ?`,
      [sellPrice, sellTime, profitLoss, tradeId]
    );

    // 更新用户本金（加上卖出金额）
    const sellAmount = trade.buy_amount + profitLoss;
    await database.run(
      'UPDATE users SET capital = capital + ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [sellAmount, userId]
    );

    // 检查累计亏损
    const totalLoss = await database.get(
      `SELECT SUM(CASE WHEN profit_loss < 0 THEN ABS(profit_loss) ELSE 0 END) as total_loss
       FROM trades WHERE user_id = ? AND status = 'closed'`,
      [userId]
    );

    const user = await database.get('SELECT loss_limit FROM users WHERE id = ?', [userId]);
    
    if (totalLoss.total_loss && totalLoss.total_loss > user.loss_limit * 0.8) {
      await notificationService.sendRiskWarning(userId, 'loss_limit', {
        current_loss: totalLoss.total_loss,
        loss_limit: user.loss_limit
      });
    }

    const updatedTrade = {
      ...trade,
      sellPrice,
      sellTime,
      profitLoss,
      status: 'closed'
    };

    res.json({
      success: true,
      message: '交易更新成功',
      data: updatedTrade
    });

  } catch (error) {
    console.error('更新交易失败:', error.message);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 获取交易列表
router.get('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { status, asset, page = 1, limit = 20 } = req.query;

    let whereClause = 'WHERE user_id = ?';
    const params = [userId];

    if (status) {
      whereClause += ' AND status = ?';
      params.push(status);
    }

    if (asset) {
      whereClause += ' AND asset = ?';
      params.push(asset);
    }

    const offset = (parseInt(page) - 1) * parseInt(limit);

    const trades = await database.query(
      `SELECT * FROM trades ${whereClause} 
       ORDER BY buy_time DESC 
       LIMIT ? OFFSET ?`,
      [...params, parseInt(limit), offset]
    );

    // 获取总数
    const totalResult = await database.get(
      `SELECT COUNT(*) as total FROM trades ${whereClause}`,
      params
    );

    res.json({
      success: true,
      data: {
        trades,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalResult.total,
          pages: Math.ceil(totalResult.total / parseInt(limit))
        }
      }
    });

  } catch (error) {
    console.error('获取交易列表失败:', error.message);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 获取交易详情
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const tradeId = req.params.id;

    const trade = await database.get(
      'SELECT * FROM trades WHERE id = ? AND user_id = ?',
      [tradeId, userId]
    );

    if (!trade) {
      return res.status(404).json({
        success: false,
        message: '交易不存在'
      });
    }

    // 如果有关联信号，获取信号信息
    if (trade.signal_id) {
      const signal = await database.get(
        'SELECT * FROM signals WHERE id = ?',
        [trade.signal_id]
      );
      trade.signal = signal;
    }

    res.json({
      success: true,
      data: trade
    });

  } catch (error) {
    console.error('获取交易详情失败:', error.message);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 获取交易统计
router.get('/stats/summary', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { period = 'all' } = req.query;

    console.log(`获取交易统计，用户ID: ${userId}, 周期: ${period}`);

    // 检查数据库连接
    if (!database.db) {
      console.error('数据库连接不存在');
      return res.json({
        success: true,
        data: {
          total_trades: 0,
          open_trades: 0,
          closed_trades: 0,
          winning_trades: 0,
          total_profit: 0,
          total_loss: 0,
          net_profit: 0,
          avg_profit: 0,
          compliant_trades: 0,
          win_rate: 0,
          compliance_rate: 0
        }
      });
    }

    let dateFilter = '';
    if (period === 'today') {
      dateFilter = "AND DATE(buy_time) = DATE('now')";
    } else if (period === 'week') {
      dateFilter = "AND buy_time >= DATE('now', '-7 days')";
    } else if (period === 'month') {
      dateFilter = "AND buy_time >= DATE('now', '-30 days')";
    }

    // 简化查询，分步执行避免复杂聚合导致锁定
    try {
      // 先检查表是否存在
      await database.get("SELECT name FROM sqlite_master WHERE type='table' AND name='trades'");

      // 分别查询各项统计，避免复杂的单一查询
      const totalTrades = await database.get(
        `SELECT COUNT(*) as count FROM trades WHERE user_id = ? ${dateFilter}`,
        [userId]
      );

      const openTrades = await database.get(
        `SELECT COUNT(*) as count FROM trades WHERE user_id = ? AND status = 'open' ${dateFilter}`,
        [userId]
      );

      const closedTrades = await database.get(
        `SELECT COUNT(*) as count FROM trades WHERE user_id = ? AND status = 'closed' ${dateFilter}`,
        [userId]
      );

      const winningTrades = await database.get(
        `SELECT COUNT(*) as count FROM trades WHERE user_id = ? AND status = 'closed' AND profit_loss > 0 ${dateFilter}`,
        [userId]
      );

      const profitStats = await database.get(
        `SELECT
          COALESCE(SUM(CASE WHEN profit_loss > 0 THEN profit_loss END), 0) as total_profit,
          COALESCE(SUM(CASE WHEN profit_loss < 0 THEN profit_loss END), 0) as total_loss,
          COALESCE(SUM(profit_loss), 0) as net_profit,
          COALESCE(AVG(profit_loss), 0) as avg_profit
         FROM trades
         WHERE user_id = ? AND status = 'closed' ${dateFilter}`,
        [userId]
      );

      const compliantTrades = await database.get(
        `SELECT COUNT(*) as count FROM trades WHERE user_id = ? AND status = 'closed' AND is_compliant = 1 ${dateFilter}`,
        [userId]
      );

      const stats = {
        total_trades: totalTrades.count || 0,
        open_trades: openTrades.count || 0,
        closed_trades: closedTrades.count || 0,
        winning_trades: winningTrades.count || 0,
        total_profit: profitStats.total_profit || 0,
        total_loss: profitStats.total_loss || 0,
        net_profit: profitStats.net_profit || 0,
        avg_profit: profitStats.avg_profit || 0,
        compliant_trades: compliantTrades.count || 0
      };

      console.log('交易统计查询结果:', stats);

      // 计算胜率和遵守率
      const winRate = stats.closed_trades > 0 ? stats.winning_trades / stats.closed_trades : 0;
      const complianceRate = stats.closed_trades > 0 ? stats.compliant_trades / stats.closed_trades : 0;

      res.json({
        success: true,
        data: {
          ...stats,
          win_rate: Math.round(winRate * 10000) / 100, // 保留2位小数的百分比
          compliance_rate: Math.round(complianceRate * 10000) / 100
        }
      });

    } catch (queryError) {
      console.error('数据库查询错误:', queryError);
      throw queryError;
    }

  } catch (error) {
    console.error('获取交易统计失败:', error.message);

    // 返回默认数据而不是错误，避免阻塞前端
    res.json({
      success: true,
      data: {
        total_trades: 0,
        open_trades: 0,
        closed_trades: 0,
        winning_trades: 0,
        total_profit: 0,
        total_loss: 0,
        net_profit: 0,
        avg_profit: 0,
        compliant_trades: 0,
        win_rate: 0,
        compliance_rate: 0
      }
    });
  }
});

module.exports = router;
