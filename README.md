# ScalpAlert - 智能交易信号H5应用

ScalpAlert 是一个专业的移动端H5交易信号应用，提供智能仓位建议和风险管理功能。

## 🚀 功能特性

### 核心功能
- **智能信号生成**: 基于RSI、EMA、M头/W底等技术指标生成交易信号
- **仓位建议**: 根据风险控制策略自动计算建议投资金额
- **风险管理**: 1%风险限制，智能止损止盈计算
- **交易记录**: 完整的交易历史和盈亏统计
- **实时通知**: 集成ntfy服务，及时推送重要信息
- **统计分析**: 胜率、收益率、策略遵守率等多维度分析

### 技术特性
- **移动端优化**: 专为移动设备设计的H5应用
- **响应式设计**: 适配各种屏幕尺寸
- **PWA支持**: 可安装到桌面，离线使用
- **实时数据**: WebSocket连接，实时更新市场数据
- **安全认证**: JWT token认证，保护用户数据

## 🏗️ 技术架构

### 前端
- **React 18**: 现代化的前端框架
- **TailwindCSS**: 实用优先的CSS框架
- **React Router**: 单页应用路由管理
- **Axios**: HTTP客户端
- **Recharts**: 数据可视化图表
- **Lucide React**: 现代化图标库

### 后端
- **Node.js**: JavaScript运行时
- **Express**: Web应用框架
- **SQLite**: 轻量级数据库
- **JWT**: 用户认证
- **bcryptjs**: 密码加密
- **node-cron**: 定时任务

### 外部服务
- **CoinGecko API**: 加密货币价格数据
- **ntfy**: 推送通知服务
- **zhi MCP**: 用户反馈收集

## 📦 安装和运行

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖
```bash
# 安装所有依赖
npm run install:all

# 或者分别安装
npm install
cd server && npm install
cd ../client && npm install
```

### 配置环境变量
```bash
# 复制环境变量模板
cp server/.env.example server/.env

# 编辑环境变量
nano server/.env
```

### 初始化数据库
```bash
cd server
npm run init-db
```

### 启动应用
```bash
# 开发模式（同时启动前后端）
npm run dev

# 或者分别启动
npm run server:dev  # 启动后端服务
npm run client:dev  # 启动前端应用
```

### 生产部署
```bash
# 构建前端
npm run build

# 启动生产服务器
npm start
```

## 🔧 配置说明

### 服务器配置
- `PORT`: 服务器端口（默认3001）
- `JWT_SECRET`: JWT密钥
- `DB_PATH`: SQLite数据库路径

### 交易配置
- `DEFAULT_CAPITAL`: 默认本金（5000元）
- `DEFAULT_LOSS_LIMIT`: 默认亏损限制（3000元）
- `DEFAULT_RISK_PERCENTAGE`: 默认风险百分比（1%）
- `MAX_DAILY_TRADES`: 每日最大交易次数（5次）

### 通知配置
- `NTFY_SERVER_URL`: ntfy服务器地址
- `NTFY_TOPIC`: 通知主题

## 📱 使用指南

### 用户注册
1. 打开应用，点击"立即注册"
2. 填写用户名、密码
3. 设置初始本金和亏损限制
4. 完成注册并自动登录

### 生成交易信号
1. 在首页点击"生成信号"
2. 系统分析市场数据生成信号
3. 查看信号详情和建议仓位
4. 根据建议进行交易决策

### 记录交易
1. 根据信号在交易所执行交易
2. 在应用中记录买入价格和实际仓位
3. 系统自动计算止损止盈位置
4. 交易完成后记录卖出价格

### 查看统计
1. 在统计页面查看交易表现
2. 分析胜率、收益率等指标
3. 根据数据优化交易策略

## 🔒 安全特性

- **密码加密**: 使用bcrypt加密存储密码
- **JWT认证**: 安全的用户会话管理
- **HTTPS支持**: 生产环境强制HTTPS
- **输入验证**: 严格的数据验证和清理
- **权限控制**: 用户只能访问自己的数据

## 📊 交易策略

### 信号生成逻辑
1. **RSI指标**: 
   - RSI < 30: 超卖，买入信号
   - RSI > 70: 超买，卖出信号

2. **EMA交叉**:
   - 短期EMA上穿长期EMA: 买入信号
   - 短期EMA下穿长期EMA: 卖出信号

3. **趋势确认**:
   - 4小时线趋势与1小时信号一致时增强信号强度

4. **形态识别**:
   - M头形态: 降低仓位建议
   - W底形态: 降低仓位建议

### 仓位管理
- **基础风险**: 每笔交易风险不超过本金1%
- **仓位上限**: 单笔投资不超过本金20%
- **仓位下限**: 单笔投资不低于本金10%
- **信号强度调整**: 根据信号强度动态调整仓位

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目。

### 开发流程
1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 📄 许可证

MIT License

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 使用应用内反馈功能

---

**免责声明**: 本应用仅供学习和研究使用，不构成投资建议。交易有风险，投资需谨慎。
