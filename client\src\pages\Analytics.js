import React, { useState } from 'react';
import { BarChart3, TrendingUp } from 'lucide-react';
import SignalStats from '../components/SignalStats';
import StrategyPerformance from '../components/StrategyPerformance';

const Analytics = () => {
  const [activeTab, setActiveTab] = useState('stats');

  const tabs = [
    {
      id: 'stats',
      name: '信号统计',
      icon: BarChart3,
      description: '信号强度分布、质量评分和历史趋势分析'
    },
    {
      id: 'performance',
      name: '策略表现',
      icon: TrendingUp,
      description: '信号准确率、盈亏比分析和风险指标评估'
    }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'stats':
        return <SignalStats />;
      case 'performance':
        return <StrategyPerformance />;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">数据分析</h1>
          <p className="text-gray-600 mt-1">
            信号统计分析和策略表现评估
          </p>
        </div>
      </div>

      {/* 标签页导航 */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center space-x-2 ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon size={16} />
                  <span>{tab.name}</span>
                </button>
              );
            })}
          </nav>
        </div>

        {/* 标签页描述 */}
        <div className="px-6 py-3 bg-gray-50 border-b border-gray-200">
          <p className="text-sm text-gray-600">
            {tabs.find(tab => tab.id === activeTab)?.description}
          </p>
        </div>
      </div>

      {/* 标签页内容 */}
      <div>
        {renderTabContent()}
      </div>
    </div>
  );
};

export default Analytics;
