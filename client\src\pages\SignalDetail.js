import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, TrendingUp, TrendingDown, Target, AlertTriangle, BarChart3, Clock, DollarSign } from 'lucide-react';
import { signalAPI } from '../utils/api';
import { formatCurrency, formatDate, formatPercentage, getSignalStrengthColor, getSignalStrengthText, getAssetDisplayName } from '../utils/api';
import toast from 'react-hot-toast';

const SignalDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [signal, setSignal] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchSignalDetail();
  }, [id]);

  const fetchSignalDetail = async () => {
    try {
      setLoading(true);
      const response = await signalAPI.getById(id);
      if (response.data.success) {
        setSignal(response.data.data);
      } else {
        toast.error('获取信号详情失败');
        navigate('/signals');
      }
    } catch (error) {
      console.error('获取信号详情失败:', error);
      toast.error('获取信号详情失败');
      navigate('/signals');
    } finally {
      setLoading(false);
    }
  };

  const getSignalTypeIcon = (type) => {
    return type === 'buy' ? TrendingUp : TrendingDown;
  };

  const getSignalTypeColor = (type) => {
    return type === 'buy' ? 'text-success-600' : 'text-danger-600';
  };

  const calculatePotentialProfit = () => {
    if (!signal || !signal.entry_price || !signal.take_profit) return null;
    const profit = signal.take_profit - signal.entry_price;
    const profitPercent = (profit / signal.entry_price) * 100;
    return {
      amount: profit * (signal.suggested_position / signal.entry_price),
      percent: profitPercent
    };
  };

  // 获取买入支撑项目
  const getBuyingSupportItems = (signal) => {
    const items = [];

    // RSI分析
    if (signal.rsi < 30) {
      items.push(`RSI严重超卖 (${signal.rsi.toFixed(1)}) - 强烈买入信号`);
    } else if (signal.rsi < 40) {
      items.push(`RSI偏低 (${signal.rsi.toFixed(1)}) - 买入机会`);
    } else if (signal.rsi < 50) {
      items.push(`RSI中性偏弱 (${signal.rsi.toFixed(1)}) - 适度买入`);
    }

    // EMA分析
    if (signal.ema_short > signal.ema_long) {
      const divergence = ((signal.ema_short - signal.ema_long) / signal.ema_long * 100).toFixed(2);
      items.push(`EMA金叉确认 (短线高出${divergence}%) - 技术面支撑`);
    }

    // 价格位置分析
    if (signal.price > signal.ema_short && signal.ema_short > signal.ema_long) {
      items.push(`价格站上均线系统 - 多头排列确认`);
    }

    // 4H趋势分析
    if (signal.trend_4h === 'up') {
      items.push(`4小时趋势向上 - 中期趋势支撑`);
    } else if (signal.trend_4h === 'sideways') {
      items.push(`4小时横盘整理 - 等待方向突破`);
    }

    // 形态分析
    if (signal.pattern === 'WBottom') {
      items.push(`W底形态确认 - 经典反转信号`);
    }

    // 成交量分析
    if (signal.volume_ratio > 1.5) {
      items.push(`成交量放大 (${signal.volume_ratio.toFixed(1)}倍) - 资金关注度高`);
    } else if (signal.volume_ratio > 1.2) {
      items.push(`成交量适度放大 (${signal.volume_ratio.toFixed(1)}倍) - 有资金参与`);
    }

    // 质量评分
    if (signal.quality_score >= 70) {
      items.push(`信号质量优秀 (${signal.quality_score}分) - 多项指标确认`);
    } else if (signal.quality_score >= 55) {
      items.push(`信号质量良好 (${signal.quality_score}分) - 部分指标确认`);
    }

    return items.length > 0 ? items : ['当前技术指标支撑有限，建议谨慎操作'];
  };

  // 获取主要否定项目
  const getNegativeItems = (signal) => {
    const items = [];

    // RSI分析
    if (signal.rsi > 70) {
      items.push(`RSI超买 (${signal.rsi.toFixed(1)}) - 存在回调风险`);
    } else if (signal.rsi > 60) {
      items.push(`RSI偏高 (${signal.rsi.toFixed(1)}) - 短期上涨空间有限`);
    }

    // EMA分析
    if (signal.ema_short < signal.ema_long) {
      const divergence = ((signal.ema_long - signal.ema_short) / signal.ema_long * 100).toFixed(2);
      items.push(`EMA死叉 (短线低于长线${divergence}%) - 技术面偏弱`);
    }

    // 价格位置分析
    if (signal.price < signal.ema_short) {
      items.push(`价格跌破短期均线 - 短期趋势转弱`);
    }

    // 4H趋势分析
    if (signal.trend_4h === 'down') {
      items.push(`4小时趋势向下 - 中期趋势不利`);
    }

    // 形态分析
    if (signal.pattern === 'MHead') {
      items.push(`M头形态 - 潜在顶部反转风险`);
    }

    // 成交量分析
    if (signal.volume_ratio < 0.8) {
      items.push(`成交量萎缩 (${signal.volume_ratio.toFixed(1)}倍) - 市场关注度不足`);
    }

    // 质量评分
    if (signal.quality_score < 40) {
      items.push(`信号质量偏低 (${signal.quality_score}分) - 技术指标确认不足`);
    } else if (signal.quality_score < 55) {
      items.push(`信号质量一般 (${signal.quality_score}分) - 需要更多确认`);
    }

    // 止损距离分析
    if (signal.stop_loss_distance > 0.05) {
      items.push(`止损距离较大 (${(signal.stop_loss_distance * 100).toFixed(1)}%) - 风险相对较高`);
    }

    return items.length > 0 ? items : ['当前未发现明显否定因素'];
  };

  // 获取综合评估
  const getOverallAssessment = (signal) => {
    const buyingItems = getBuyingSupportItems(signal);
    const negativeItems = getNegativeItems(signal);

    const buyingCount = buyingItems.filter(item => !item.includes('支撑有限')).length;
    const negativeCount = negativeItems.filter(item => !item.includes('未发现明显')).length;

    if (signal.signal_strength === 'strong') {
      return `强信号确认：${buyingCount}项支撑因素 vs ${negativeCount}项否定因素。技术指标多重确认，建议按计划执行交易策略。`;
    } else if (signal.signal_strength === 'medium') {
      return `中等信号：${buyingCount}项支撑因素 vs ${negativeCount}项否定因素。部分技术指标确认，建议适度参与并严格止损。`;
    } else if (signal.signal_strength === 'weak') {
      return `弱信号提示：${buyingCount}项支撑因素 vs ${negativeCount}项否定因素。技术指标确认有限，建议小仓位试探或等待更好机会。`;
    } else {
      return `信号质量不足：否定因素较多或支撑不够充分，建议暂时观望，等待更明确的交易机会。`;
    }
  };

  // 获取质量等级
  const getQualityGrade = (score) => {
    if (score >= 80) return '优秀';
    if (score >= 70) return '良好';
    if (score >= 60) return '中等';
    if (score >= 40) return '一般';
    return '偏低';
  };

  // 获取质量评分详细分解
  const getQualityScoreBreakdown = (signal) => {
    const breakdown = [];

    // RSI评分 (最高40分)
    let rsiScore = 0;
    if (signal.rsi < 20) rsiScore = 40;
    else if (signal.rsi < 25) rsiScore = 35;
    else if (signal.rsi < 30) rsiScore = 30;
    else if (signal.rsi < 35) rsiScore = 25;
    else if (signal.rsi < 40) rsiScore = 20;
    else if (signal.rsi < 45) rsiScore = 15;
    else if (signal.rsi < 50) rsiScore = 10;
    else rsiScore = 0;

    breakdown.push(`RSI评分: ${rsiScore}/40分 (RSI=${signal.rsi.toFixed(1)})`);

    // EMA评分 (最高30分)
    let emaScore = 10; // 基础分
    if (signal.ema_short > signal.ema_long) {
      const divergence = (signal.ema_short - signal.ema_long) / signal.ema_long;
      if (signal.price > signal.ema_short && divergence > 0.02) emaScore = 30;
      else if (signal.price > signal.ema_short && divergence > 0.01) emaScore = 25;
      else if (signal.price > signal.ema_short) emaScore = 20;
      else emaScore = 18;
    } else {
      emaScore = 5;
    }

    breakdown.push(`EMA评分: ${emaScore}/30分 (${signal.ema_short > signal.ema_long ? '金叉' : '死叉'})`);

    // 趋势评分 (最高15分)
    let trendScore = 5; // 基础分
    if (signal.trend_4h === 'up') trendScore = 15;
    else if (signal.trend_4h === 'sideways') trendScore = 10;
    else trendScore = 0;

    breakdown.push(`趋势评分: ${trendScore}/15分 (4H趋势${signal.trend_4h === 'up' ? '向上' : signal.trend_4h === 'down' ? '向下' : '横盘'})`);

    // 成交量评分 (最高10分)
    let volumeScore = 5; // 基础分
    if (signal.volume_ratio > 2) volumeScore = 10;
    else if (signal.volume_ratio > 1.5) volumeScore = 8;
    else if (signal.volume_ratio > 1.2) volumeScore = 6;
    else if (signal.volume_ratio < 0.8) volumeScore = 2;

    breakdown.push(`成交量评分: ${volumeScore}/10分 (${signal.volume_ratio.toFixed(1)}倍)`);

    // 形态评分 (最高5分)
    let patternScore = 0;
    if (signal.pattern === 'WBottom') patternScore = 5;
    else if (signal.pattern === 'MHead') patternScore = -5; // 负分

    if (patternScore !== 0) {
      breakdown.push(`形态评分: ${patternScore > 0 ? '+' : ''}${patternScore}分 (${signal.pattern === 'WBottom' ? 'W底加分' : 'M头减分'})`);
    } else {
      breakdown.push(`形态评分: 0分 (无特殊形态)`);
    }

    // 计算总分验证
    const calculatedTotal = rsiScore + emaScore + trendScore + volumeScore + patternScore;
    const actualTotal = signal.quality_score;

    if (Math.abs(calculatedTotal - actualTotal) > 5) {
      breakdown.push(`注: 实际评分可能包含其他调整因子`);
    }

    return breakdown;
  };

  const calculatePotentialLoss = () => {
    if (!signal || !signal.entry_price || !signal.stop_loss) return null;
    const loss = signal.entry_price - signal.stop_loss;
    const lossPercent = (loss / signal.entry_price) * 100;
    return {
      amount: loss * (signal.suggested_position / signal.entry_price),
      percent: lossPercent
    };
  };

  if (loading) {
    return (
      <div className="mobile-content">
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  if (!signal) {
    return (
      <div className="mobile-content">
        <div className="text-center py-12">
          <AlertTriangle className="mx-auto text-gray-400 mb-4" size={64} />
          <h3 className="text-lg font-medium text-gray-900 mb-2">信号不存在</h3>
          <p className="text-gray-600 mb-6">请检查信号ID是否正确</p>
          <button
            onClick={() => navigate('/signals')}
            className="btn-primary"
          >
            返回信号列表
          </button>
        </div>
      </div>
    );
  }

  const SignalIcon = getSignalTypeIcon(signal.signal_type);
  const potentialProfit = calculatePotentialProfit();
  const potentialLoss = calculatePotentialLoss();

  return (
    <div className="mobile-content">
      {/* 头部导航 */}
      <div className="flex items-center justify-between mb-6">
        <button
          onClick={() => navigate('/signals')}
          className="flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeft size={20} className="mr-2" />
          返回
        </button>
        <h1 className="text-lg font-semibold text-gray-900">信号详情</h1>
        <div className="w-16"></div> {/* 占位符保持居中 */}
      </div>

      {/* 信号概览卡片 */}
      <div className={`card mb-6 ${
        signal.signal_strength === 'strong' ? 'border-success-200 bg-success-50' :
        signal.signal_strength === 'medium' ? 'border-warning-200 bg-warning-50' :
        'border-gray-200 bg-gray-50'
      }`}>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
              signal.signal_type === 'buy' ? 'bg-success-100' : 'bg-danger-100'
            }`}>
              <SignalIcon className={getSignalTypeColor(signal.signal_type)} size={24} />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">
                {getAssetDisplayName(signal.asset)}
              </h2>
              <p className={`text-sm font-medium ${getSignalTypeColor(signal.signal_type)}`}>
                {signal.signal_type === 'buy' ? '买入信号' : '卖出信号'}
              </p>
            </div>
          </div>
          <div className="text-right">
            <span className={`badge ${getSignalStrengthColor(signal.signal_strength)}`}>
              {getSignalStrengthText(signal.signal_strength)}
            </span>
            <p className="text-xs text-gray-500 mt-1">
              {formatDate(signal.timestamp)}
            </p>
          </div>
        </div>

        {/* 价格和仓位信息 */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-gray-600">当前价格</p>
            <p className="text-2xl font-bold text-gray-900">
              ${signal.price?.toLocaleString()}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-600">建议仓位</p>
            <p className="text-2xl font-bold text-primary-600">
              {formatCurrency(signal.suggested_position)}
            </p>
          </div>
        </div>
      </div>

      {/* 技术指标 */}
      <div className="card mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <BarChart3 className="mr-2" size={20} />
          技术指标分析
        </h3>
        
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="bg-gray-50 rounded-lg p-3">
            <p className="text-sm text-gray-600">RSI (14)</p>
            <p className="text-lg font-semibold text-gray-900">
              {signal.rsi?.toFixed(1)}
            </p>
            <p className={`text-xs ${
              signal.rsi < 30 ? 'text-success-600' : 
              signal.rsi > 70 ? 'text-danger-600' : 'text-gray-600'
            }`}>
              {signal.rsi < 30 ? '超卖区域' : 
               signal.rsi > 70 ? '超买区域' : '正常区域'}
            </p>
          </div>
          
          <div className="bg-gray-50 rounded-lg p-3">
            <p className="text-sm text-gray-600">质量评分</p>
            <p className="text-lg font-semibold text-gray-900">
              {signal.quality_score}/100
            </p>
            <p className={`text-xs ${
              signal.quality_score >= 70 ? 'text-success-600' : 
              signal.quality_score >= 40 ? 'text-warning-600' : 'text-danger-600'
            }`}>
              {signal.quality_score >= 70 ? '高质量' : 
               signal.quality_score >= 40 ? '中等质量' : '低质量'}
            </p>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="bg-gray-50 rounded-lg p-3">
            <p className="text-sm text-gray-600">EMA短线 (9)</p>
            <p className="text-lg font-semibold text-gray-900">
              ${signal.ema_short?.toFixed(2)}
            </p>
          </div>
          
          <div className="bg-gray-50 rounded-lg p-3">
            <p className="text-sm text-gray-600">EMA长线 (21)</p>
            <p className="text-lg font-semibold text-gray-900">
              ${signal.ema_long?.toFixed(2)}
            </p>
          </div>
        </div>

        <div className="mt-4 grid grid-cols-2 gap-4">
          <div className="bg-gray-50 rounded-lg p-3">
            <p className="text-sm text-gray-600">4H趋势</p>
            <p className={`text-lg font-semibold ${
              signal.trend_4h === 'up' ? 'text-success-600' :
              signal.trend_4h === 'down' ? 'text-danger-600' :
              'text-gray-600'
            }`}>
              {signal.trend_4h === 'up' ? '上涨' :
               signal.trend_4h === 'down' ? '下跌' : '横盘'}
            </p>
          </div>
          
          <div className="bg-gray-50 rounded-lg p-3">
            <p className="text-sm text-gray-600">形态识别</p>
            <p className="text-lg font-semibold text-gray-900">
              {signal.pattern === 'MHead' ? 'M头' :
               signal.pattern === 'WBottom' ? 'W底' : '无特殊形态'}
            </p>
          </div>
        </div>
      </div>

      {/* 风险管理 */}
      <div className="card mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Target className="mr-2" size={20} />
          风险管理
        </h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
            <div>
              <p className="text-sm text-blue-600">入场价格</p>
              <p className="text-lg font-semibold text-blue-900">
                ${signal.entry_price?.toFixed(2)}
              </p>
            </div>
            <DollarSign className="text-blue-600" size={20} />
          </div>
          
          <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
            <div>
              <p className="text-sm text-red-600">止损价格</p>
              <p className="text-lg font-semibold text-red-900">
                ${signal.stop_loss?.toFixed(2)}
              </p>
              {potentialLoss && (
                <p className="text-xs text-red-600">
                  最大亏损: {formatCurrency(potentialLoss.amount)} ({potentialLoss.percent.toFixed(2)}%)
                </p>
              )}
            </div>
            <TrendingDown className="text-red-600" size={20} />
          </div>
          
          <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
            <div>
              <p className="text-sm text-green-600">止盈价格</p>
              <p className="text-lg font-semibold text-green-900">
                ${signal.take_profit?.toFixed(2)}
              </p>
              {potentialProfit && (
                <p className="text-xs text-green-600">
                  预期盈利: {formatCurrency(potentialProfit.amount)} ({potentialProfit.percent.toFixed(2)}%)
                </p>
              )}
            </div>
            <TrendingUp className="text-green-600" size={20} />
          </div>
        </div>
      </div>

      {/* 信号生成逻辑 */}
      <div className="card mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">信号生成逻辑</h3>
        
        <div className="space-y-3">
          <div className="bg-blue-50 rounded-lg p-3">
            <h4 className="text-sm font-medium text-blue-900 mb-2">检测条件</h4>
            <div className="space-y-1 text-xs text-blue-800">
              <p>• RSI指标: {signal.rsi?.toFixed(1)} {signal.rsi < 30 ? '(超卖信号)' : signal.rsi > 70 ? '(超买信号)' : '(中性)'}</p>
              <p>• EMA交叉: {signal.ema_short > signal.ema_long ? '金叉(看涨)' : '死叉(看跌)'}</p>
              <p>• 4H趋势: {signal.trend_4h === 'up' ? '上涨趋势' : signal.trend_4h === 'down' ? '下跌趋势' : '横盘整理'}</p>
              <p>• 成交量: {signal.volume_ratio?.toFixed(2)}倍平均成交量</p>
            </div>
          </div>
          
          <div className="bg-yellow-50 rounded-lg p-3">
            <h4 className="text-sm font-medium text-yellow-900 mb-2">质量评分详情</h4>
            <div className="space-y-1 text-xs text-yellow-800">
              <p className="font-medium">• 总评分: {signal.quality_score}/100 分 ({getQualityGrade(signal.quality_score)})</p>
              <div className="ml-2 space-y-1 text-xs">
                {getQualityScoreBreakdown(signal).map((item, index) => (
                  <p key={index}>• {item}</p>
                ))}
              </div>
              <p className="mt-2 pt-2 border-t border-yellow-200">
                • 信号强度: {getSignalStrengthText(signal.signal_strength)}
              </p>
              <p>• 形态影响: {signal.pattern !== 'None' ? `检测到${signal.pattern === 'MHead' ? 'M头' : 'W底'}形态` : '无特殊形态'}</p>
            </div>
          </div>
          
          {signal.description && (
            <div className="bg-gray-50 rounded-lg p-3">
              <h4 className="text-sm font-medium text-gray-900 mb-2">分析说明</h4>
              <p className="text-xs text-gray-700">{signal.description}</p>
            </div>
          )}
        </div>
      </div>

      {/* 信号分析详情 */}
      <div className="card mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <BarChart3 className="mr-2" size={20} />
          信号分析详情
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* 买入支撑项目 */}
          <div className="bg-green-50 rounded-lg p-4">
            <h4 className="text-sm font-medium text-green-900 mb-3 flex items-center">
              <TrendingUp className="mr-1" size={16} />
              买入支撑项目
            </h4>
            <div className="space-y-2 text-xs text-green-800">
              {getBuyingSupportItems(signal).map((item, index) => (
                <div key={index} className="flex items-start space-x-2">
                  <div className="w-1.5 h-1.5 bg-green-600 rounded-full mt-1.5 flex-shrink-0"></div>
                  <p>{item}</p>
                </div>
              ))}
            </div>
          </div>

          {/* 主要否定项目 */}
          <div className="bg-red-50 rounded-lg p-4">
            <h4 className="text-sm font-medium text-red-900 mb-3 flex items-center">
              <TrendingDown className="mr-1" size={16} />
              主要否定项目
            </h4>
            <div className="space-y-2 text-xs text-red-800">
              {getNegativeItems(signal).map((item, index) => (
                <div key={index} className="flex items-start space-x-2">
                  <div className="w-1.5 h-1.5 bg-red-600 rounded-full mt-1.5 flex-shrink-0"></div>
                  <p>{item}</p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 综合评估 */}
        <div className="mt-4 p-3 bg-blue-50 rounded-lg">
          <h4 className="text-sm font-medium text-blue-900 mb-2">综合评估</h4>
          <p className="text-xs text-blue-800">
            {getOverallAssessment(signal)}
          </p>
        </div>
      </div>

      {/* 操作建议 */}
      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">操作建议</h3>
        
        <div className="space-y-3">
          <div className="p-3 border border-primary-200 rounded-lg bg-primary-50">
            <h4 className="text-sm font-medium text-primary-900 mb-2">推荐操作</h4>
            <div className="space-y-1 text-sm text-primary-800">
              <p>• 建议{signal.signal_type === 'buy' ? '买入' : '卖出'} {getAssetDisplayName(signal.asset)}</p>
              <p>• 投入资金: {formatCurrency(signal.suggested_position)}</p>
              <p>• 入场价格: ${signal.entry_price?.toFixed(2)}</p>
              <p>• 设置止损: ${signal.stop_loss?.toFixed(2)}</p>
              <p>• 设置止盈: ${signal.take_profit?.toFixed(2)}</p>
            </div>
          </div>
          
          <div className="p-3 border border-gray-200 rounded-lg bg-gray-50">
            <h4 className="text-sm font-medium text-gray-900 mb-2">风险提示</h4>
            <div className="space-y-1 text-xs text-gray-700">
              <p>• 请严格按照止损止盈执行，控制风险</p>
              <p>• 建议仓位不超过总资金的20%</p>
              <p>• 市场有风险，投资需谨慎</p>
              <p>• 本信号仅供参考，不构成投资建议</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignalDetail;
