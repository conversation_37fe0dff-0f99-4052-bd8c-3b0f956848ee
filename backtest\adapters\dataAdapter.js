/**
 * 数据适配器
 * 将历史K线数据转换为后端服务期望的格式
 */

class DataAdapter {
  constructor(historicalData = null) {
    this.rawData = historicalData;
    this.currentIndex = 0;
  }

  /**
   * 从文件加载数据
   * @param {string} filePath - 数据文件路径
   */
  async loadFromFile(filePath) {
    const fs = require('fs').promises;
    try {
      const fileContent = await fs.readFile(filePath, 'utf8');
      const jsonData = JSON.parse(fileContent);

      // 处理嵌套数据格式
      if (jsonData.data && Array.isArray(jsonData.data)) {
        this.rawData = jsonData.data;
        console.log(`✅ 数据加载成功: ${jsonData.symbol} ${jsonData.interval}`);
        console.log(`📊 K线数据: ${this.rawData.length} 条`);
        console.log(`📅 时间范围: ${jsonData.startDate} - ${jsonData.endDate}`);
      } else if (Array.isArray(jsonData)) {
        this.rawData = jsonData;
        console.log(`✅ 数据加载成功: ${this.rawData.length} 条K线数据`);
      } else {
        throw new Error('数据格式不正确，需要数组格式的K线数据');
      }

      this.currentIndex = 0;

      // 验证数据
      if (this.rawData.length === 0) {
        throw new Error('数据文件为空');
      }

      // 验证第一条数据格式
      const firstCandle = this.rawData[0];
      if (!Array.isArray(firstCandle) || firstCandle.length < 6) {
        throw new Error('K线数据格式错误，需要[timestamp, open, high, low, close, volume]格式');
      }

    } catch (error) {
      throw new Error(`数据加载失败: ${error.message}`);
    }
  }

  /**
   * 获取所有数据
   * @returns {Array} 原始数据数组
   */
  getData() {
    return this.rawData || [];
  }

  /**
   * 获取数据长度
   * @returns {number} 数据条数
   */
  getDataLength() {
    return this.rawData ? this.rawData.length : 0;
  }

  /**
   * 获取指定时间点的历史数据切片
   * @param {number} endIndex - 结束索引（包含）
   * @param {number} lookback - 回看期数（默认200）
   * @returns {Array} OHLCV格式的数据数组
   */
  getHistoricalSlice(endIndex, lookback = 200) {
    const startIndex = Math.max(0, endIndex - lookback + 1);
    const slice = this.rawData.slice(startIndex, endIndex + 1);

    // 转换为OHLCV格式: [timestamp, open, high, low, close, volume]
    return slice.map(kline => [
      parseInt(kline[0]),      // timestamp
      parseFloat(kline[1]),    // open
      parseFloat(kline[2]),    // high
      parseFloat(kline[3]),    // low
      parseFloat(kline[4]),    // close
      parseFloat(kline[5])     // volume
    ]);
  }

  /**
   * 获取当前K线数据
   * @param {number} index - K线索引
   * @returns {Object} 当前K线信息
   */
  getCurrentCandle(index) {
    if (index >= this.rawData.length) {
      return null;
    }

    const kline = this.rawData[index];
    return {
      timestamp: parseInt(kline[0]),
      open: parseFloat(kline[1]),
      high: parseFloat(kline[2]),
      low: parseFloat(kline[3]),
      close: parseFloat(kline[4]),
      volume: parseFloat(kline[5]),
      closeTime: parseInt(kline[6]),
      quoteVolume: parseFloat(kline[7]),
      trades: parseInt(kline[8]),
      buyBaseVolume: parseFloat(kline[9]),
      buyQuoteVolume: parseFloat(kline[10])
    };
  }

  /**
   * 获取价格数据（模拟实时API）
   * @param {string} asset - 资产名称
   * @param {number} index - 当前索引
   * @returns {Object} 价格信息
   */
  getPriceData(asset, index) {
    const candle = this.getCurrentCandle(index);
    if (!candle) return null;

    return {
      price: candle.close,
      price_change_24h: this.calculate24hChange(index),
      volume_24h: this.calculate24hVolume(index),
      timestamp: candle.timestamp
    };
  }

  /**
   * 计算24小时价格变化
   * @param {number} index - 当前索引
   * @returns {number} 24小时价格变化百分比
   */
  calculate24hChange(index) {
    if (index < 24) return 0; // 数据不足24小时

    const currentPrice = parseFloat(this.rawData[index][4]);
    const price24hAgo = parseFloat(this.rawData[index - 24][4]);
    
    return ((currentPrice - price24hAgo) / price24hAgo) * 100;
  }

  /**
   * 计算24小时成交量
   * @param {number} index - 当前索引
   * @returns {number} 24小时成交量
   */
  calculate24hVolume(index) {
    const start = Math.max(0, index - 23);
    let totalVolume = 0;
    
    for (let i = start; i <= index; i++) {
      totalVolume += parseFloat(this.rawData[i][5]);
    }
    
    return totalVolume;
  }

  /**
   * 获取数据总长度
   * @returns {number} 数据总条数
   */
  getLength() {
    return this.rawData.length;
  }

  /**
   * 获取时间范围
   * @returns {Object} 开始和结束时间
   */
  getTimeRange() {
    if (this.rawData.length === 0) {
      return { start: null, end: null };
    }

    return {
      start: new Date(parseInt(this.rawData[0][0])),
      end: new Date(parseInt(this.rawData[this.rawData.length - 1][0]))
    };
  }

  /**
   * 验证数据完整性
   * @returns {Object} 验证结果
   */
  validateData() {
    const issues = [];
    
    if (this.rawData.length === 0) {
      issues.push('数据为空');
      return { valid: false, issues };
    }

    // 检查数据格式
    for (let i = 0; i < Math.min(100, this.rawData.length); i++) {
      const kline = this.rawData[i];
      if (!Array.isArray(kline) || kline.length < 11) {
        issues.push(`第${i}条数据格式错误`);
      }
      
      // 检查价格数据
      const prices = [kline[1], kline[2], kline[3], kline[4]]; // OHLC
      if (prices.some(p => isNaN(parseFloat(p)) || parseFloat(p) <= 0)) {
        issues.push(`第${i}条数据价格异常`);
      }
    }

    // 检查时间顺序
    for (let i = 1; i < Math.min(1000, this.rawData.length); i++) {
      if (parseInt(this.rawData[i][0]) <= parseInt(this.rawData[i-1][0])) {
        issues.push(`时间顺序错误: 索引${i}`);
        break;
      }
    }

    return {
      valid: issues.length === 0,
      issues,
      totalRecords: this.rawData.length,
      timeRange: this.getTimeRange()
    };
  }

  /**
   * 获取统计信息
   * @returns {Object} 数据统计
   */
  getStats() {
    if (this.rawData.length === 0) {
      return null;
    }

    const prices = this.rawData.map(k => parseFloat(k[4]));
    const volumes = this.rawData.map(k => parseFloat(k[5]));

    return {
      totalRecords: this.rawData.length,
      timeRange: this.getTimeRange(),
      priceRange: {
        min: Math.min(...prices),
        max: Math.max(...prices),
        first: prices[0],
        last: prices[prices.length - 1]
      },
      volumeStats: {
        min: Math.min(...volumes),
        max: Math.max(...volumes),
        avg: volumes.reduce((a, b) => a + b, 0) / volumes.length
      }
    };
  }
}

module.exports = DataAdapter;
