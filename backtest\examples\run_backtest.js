/**
 * 回测运行示例
 * 演示如何使用回测框架
 */

const path = require('path');
const Backtester = require('../engine/backtester');
const { backtestConfig, strategyConfigs, backtestScenarios } = require('../config/backtest.config');

async function runSingleBacktest() {
  console.log('🚀 单策略回测示例');
  console.log('='.repeat(50));
  
  try {
    // 创建回测引擎
    const backtester = new Backtester(backtestConfig);
    
    // 加载数据
    const dataPath = path.join(__dirname, '../data/BTCUSDT_1h.json');
    console.log(`📊 加载数据: ${dataPath}`);
    
    const validation = backtester.loadData(dataPath);
    console.log(`✅ 数据验证通过: ${validation.totalRecords} 条记录`);
    
    // 运行回测
    const results = await backtester.runBacktest(strategyConfigs.balanced);
    
    // 显示报告
    console.log(backtester.getReport());
    
    // 导出结果
    if (backtestConfig.exportResults) {
      backtester.exportResults();
    }
    
    return results;
    
  } catch (error) {
    console.error('❌ 回测失败:', error.message);
    throw error;
  }
}

async function runBatchBacktest() {
  console.log('\n🔄 批量回测示例');
  console.log('='.repeat(50));
  
  try {
    const backtester = new Backtester(backtestConfig);
    
    // 加载数据
    const dataPath = path.join(__dirname, '../data/BTCUSDT_1h.json');
    backtester.loadData(dataPath);
    
    // 准备多个配置
    const configs = [
      strategyConfigs.conservative,
      strategyConfigs.balanced,
      strategyConfigs.aggressive
    ];
    
    // 运行批量回测
    const results = await backtester.batchBacktest(configs);
    
    // 显示比较结果
    console.log('\n📊 策略比较结果:');
    console.log('='.repeat(80));
    console.log('策略名称'.padEnd(15) + '总收益%'.padEnd(10) + '年化收益%'.padEnd(12) + '最大回撤%'.padEnd(12) + '夏普比率'.padEnd(10) + '交易次数');
    console.log('-'.repeat(80));
    
    results.forEach(result => {
      if (!result.error) {
        const m = result.metrics;
        console.log(
          result.configName.padEnd(15) +
          m.totalReturn.toFixed(2).padEnd(10) +
          m.annualizedReturn.toFixed(2).padEnd(12) +
          m.maxDrawdown.toFixed(2).padEnd(12) +
          m.sharpeRatio.toFixed(3).padEnd(10) +
          m.totalTrades.toString()
        );
      }
    });
    
    return results;
    
  } catch (error) {
    console.error('❌ 批量回测失败:', error.message);
    throw error;
  }
}

async function runOptimizationExample() {
  console.log('\n🎯 参数优化示例');
  console.log('='.repeat(50));
  
  try {
    const backtester = new Backtester(backtestConfig);
    
    // 加载数据
    const dataPath = path.join(__dirname, '../data/BTCUSDT_1h.json');
    backtester.loadData(dataPath);
    
    // 生成参数组合
    const baseConfig = { ...strategyConfigs.balanced };
    const optimizationConfigs = [];
    
    // 简单的RSI阈值优化
    for (let strongThreshold = 20; strongThreshold <= 35; strongThreshold += 5) {
      for (let mediumThreshold = strongThreshold + 5; mediumThreshold <= 50; mediumThreshold += 5) {
        const config = {
          ...baseConfig,
          name: `RSI_${strongThreshold}_${mediumThreshold}`,
          rsi_strong_threshold: strongThreshold,
          rsi_medium_threshold: mediumThreshold
        };
        optimizationConfigs.push(config);
      }
    }
    
    console.log(`🔍 测试 ${optimizationConfigs.length} 个参数组合...`);
    
    // 运行优化
    const results = await backtester.batchBacktest(optimizationConfigs);
    
    // 找出最佳配置
    const validResults = results.filter(r => !r.error);
    const bestResult = validResults.reduce((best, current) => 
      (current.metrics.sharpeRatio > best.metrics.sharpeRatio) ? current : best
    );
    
    console.log('\n🏆 最佳配置:');
    console.log(`配置名称: ${bestResult.configName}`);
    console.log(`夏普比率: ${bestResult.metrics.sharpeRatio.toFixed(3)}`);
    console.log(`总收益: ${bestResult.metrics.totalReturn.toFixed(2)}%`);
    console.log(`最大回撤: ${bestResult.metrics.maxDrawdown.toFixed(2)}%`);
    console.log(`交易次数: ${bestResult.metrics.totalTrades}`);
    
    return bestResult;
    
  } catch (error) {
    console.error('❌ 参数优化失败:', error.message);
    throw error;
  }
}

async function runPeriodAnalysis() {
  console.log('\n📅 分期间分析示例');
  console.log('='.repeat(50));
  
  try {
    const backtester = new Backtester(backtestConfig);
    
    // 加载数据
    const dataPath = path.join(__dirname, '../data/BTCUSDT_1h.json');
    const validation = backtester.loadData(dataPath);
    
    const totalLength = validation.totalRecords;
    const config = strategyConfigs.balanced;
    
    // 分析不同时期的表现
    const periods = [
      { name: '前半期', startIndex: 50, endIndex: Math.floor(totalLength / 2) },
      { name: '后半期', startIndex: Math.floor(totalLength / 2), endIndex: totalLength - 1 },
      { name: '最近25%', startIndex: Math.floor(totalLength * 0.75), endIndex: totalLength - 1 }
    ];
    
    console.log('📊 分期间回测结果:');
    console.log('='.repeat(70));
    console.log('时期'.padEnd(10) + '总收益%'.padEnd(10) + '年化收益%'.padEnd(12) + '夏普比率'.padEnd(10) + '交易次数');
    console.log('-'.repeat(70));
    
    for (const period of periods) {
      const options = {
        startIndex: period.startIndex,
        endIndex: period.endIndex
      };
      
      const result = await backtester.runBacktest(config, options);
      const m = result.metrics;
      
      console.log(
        period.name.padEnd(10) +
        m.totalReturn.toFixed(2).padEnd(10) +
        m.annualizedReturn.toFixed(2).padEnd(12) +
        m.sharpeRatio.toFixed(3).padEnd(10) +
        m.totalTrades.toString()
      );
    }
    
  } catch (error) {
    console.error('❌ 分期间分析失败:', error.message);
    throw error;
  }
}

// 主函数
async function main() {
  console.log('🎯 ScalpAlert 回测框架示例');
  console.log('='.repeat(60));
  
  try {
    // 检查数据文件是否存在
    const dataPath = path.join(__dirname, '../data/BTCUSDT_1h.json');
    const fs = require('fs');
    
    if (!fs.existsSync(dataPath)) {
      console.log('❌ 数据文件不存在，请先运行数据下载器:');
      console.log('cd backtest/data && node downloader.js');
      return;
    }
    
    // 运行示例
    await runSingleBacktest();
    await runBatchBacktest();
    await runOptimizationExample();
    await runPeriodAnalysis();
    
    console.log('\n✅ 所有示例运行完成！');
    console.log('📁 查看 backtest_results 目录获取详细结果');
    
  } catch (error) {
    console.error('❌ 示例运行失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main();
}

module.exports = {
  runSingleBacktest,
  runBatchBacktest,
  runOptimizationExample,
  runPeriodAnalysis
};
