import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Home, TrendingUp, FileText, BarChart3, Search } from 'lucide-react';
import clsx from 'clsx';

const BottomNavigation = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const navItems = [
    {
      id: 'dashboard',
      label: '首页',
      icon: Home,
      path: '/dashboard',
    },
    {
      id: 'signals',
      label: '信号',
      icon: TrendingUp,
      path: '/signals',
    },
    {
      id: 'analysis',
      label: '分析',
      icon: Search,
      path: '/analysis',
    },
    {
      id: 'trades',
      label: '交易',
      icon: FileText,
      path: '/trades',
    },
    {
      id: 'statistics',
      label: '统计',
      icon: BarChart3,
      path: '/statistics',
    },
  ];

  const handleNavigation = (path) => {
    navigate(path);
  };

  return (
    <nav className="mobile-bottom-nav safe-area-bottom">
      <div className="flex justify-around">
        {navItems.map((item) => {
          const Icon = item.icon;
          const isActive = location.pathname === item.path || 
                          (item.path === '/dashboard' && location.pathname === '/');
          
          return (
            <button
              key={item.id}
              onClick={() => handleNavigation(item.path)}
              className={clsx(
                'nav-item',
                isActive && 'active'
              )}
            >
              <Icon 
                size={20} 
                className={clsx(
                  'mb-1',
                  isActive ? 'text-primary-600' : 'text-gray-500'
                )}
              />
              <span className={clsx(
                'text-xs',
                isActive ? 'text-primary-600' : 'text-gray-500'
              )}>
                {item.label}
              </span>
            </button>
          );
        })}
      </div>
    </nav>
  );
};

export default BottomNavigation;
