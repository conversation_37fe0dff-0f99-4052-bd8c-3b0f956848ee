/**
 * 方案一：拥抱趋势，简化为王
 * 核心理念：让利润奔跑，让亏损停止
 * 交易周期：H4或日线
 * 目标：极高盈亏比，低交易频次
 */

const TechnicalIndicators = require('../utils/technicalIndicators');

class TrendFollowingStrategy {
  constructor(config = {}) {
    this.config = {
      name: '趋势跟踪策略',
      // 均线参数
      ema_fast: 50,           // 快速均线EMA(50)
      ema_slow: 200,          // 慢速均线EMA(200)
      ema_entry: 20,          // 入场确认均线EMA(20)
      
      // 风险管理
      atr_multiplier: 2.0,    // ATR止损倍数
      risk_per_trade: 0.02,   // 每笔交易风险2%
      
      // 趋势确认
      trend_strength_threshold: 0.005, // 趋势强度阈值0.5%
      consolidation_threshold: 0.02,   // 震荡判断阈值2%
      
      // K线形态确认
      enable_candlestick_confirmation: true,
      min_candle_body_ratio: 0.6,     // 最小实体比例
      
      ...config
    };
    
    this.technicalIndicators = new TechnicalIndicators();
  }

  /**
   * 生成交易信号
   * @param {Array} historicalData - OHLCV历史数据
   * @param {Object} currentCandle - 当前K线
   * @returns {Object} 交易信号
   */
  generateSignal(historicalData, currentCandle) {
    if (historicalData.length < this.config.ema_slow + 10) {
      return this.createSignal('hold', 'skip', '数据不足');
    }

    try {
      // 计算技术指标
      const prices = historicalData.map(candle => candle[4]); // 收盘价
      const highs = historicalData.map(candle => candle[2]);
      const lows = historicalData.map(candle => candle[3]);
      
      const ema50 = this.technicalIndicators.calculateEMAFromPrices(prices, this.config.ema_fast);
      const ema200 = this.technicalIndicators.calculateEMAFromPrices(prices, this.config.ema_slow);
      const ema20 = this.technicalIndicators.calculateEMAFromPrices(prices, this.config.ema_entry);
      const atr = this.technicalIndicators.calculateATR(highs, lows, prices, 14);
      
      const currentPrice = currentCandle.close;
      const currentEMA50 = ema50[ema50.length - 1];
      const currentEMA200 = ema200[ema200.length - 1];
      const currentEMA20 = ema20[ema20.length - 1];
      const currentATR = atr[atr.length - 1];
      
      // 1. 趋势判断
      const trendAnalysis = this.analyzeTrend(currentPrice, currentEMA50, currentEMA200, prices);
      
      if (trendAnalysis.direction === 'consolidation') {
        return this.createSignal('hold', 'skip', '市场震荡，空仓观望');
      }
      
      // 2. 入场信号检查
      const entrySignal = this.checkEntrySignal(
        historicalData, 
        currentCandle, 
        currentEMA20, 
        trendAnalysis.direction
      );
      
      if (!entrySignal.valid) {
        return this.createSignal('hold', 'skip', entrySignal.reason);
      }
      
      // 3. K线形态确认
      if (this.config.enable_candlestick_confirmation) {
        const candlestickConfirmation = this.confirmCandlestickPattern(
          historicalData.slice(-3), 
          trendAnalysis.direction
        );
        
        if (!candlestickConfirmation.valid) {
          return this.createSignal('hold', 'skip', candlestickConfirmation.reason);
        }
      }
      
      // 4. 计算止损止盈
      const stopLoss = this.calculateStopLoss(currentPrice, currentATR, trendAnalysis.direction);
      const riskAmount = this.calculatePositionSize(currentPrice, stopLoss);
      
      // 5. 生成信号
      return this.createSignal(
        trendAnalysis.direction === 'bullish' ? 'buy' : 'sell',
        'strong',
        `趋势${trendAnalysis.direction}，回调入场`,
        {
          entry_price: currentPrice,
          stop_loss: stopLoss,
          take_profit: null, // 使用移动止盈
          risk_amount: riskAmount,
          trend_strength: trendAnalysis.strength,
          atr: currentATR
        }
      );
      
    } catch (error) {
      return this.createSignal('hold', 'skip', `计算错误: ${error.message}`);
    }
  }

  /**
   * 核心能力1: 趋势判定模块 (The Trend Filter)
   * 明确定义市场状态：UPTREND, DOWNTREND, SIDEWAYS
   */
  defineMarketTrend(currentPrice, ema50, ema200) {
    // 严格的趋势判定规则
    if (currentPrice > ema50 && ema50 > ema200) {
      return 'UPTREND';  // 严格多头排列
    } else if (currentPrice < ema50 && ema50 < ema200) {
      return 'DOWNTREND'; // 严格空头排列
    } else {
      return 'SIDEWAYS';  // 其他所有情况都是震荡
    }
  }

  /**
   * 分析趋势方向和强度（保持兼容性）
   */
  analyzeTrend(currentPrice, ema50, ema200, prices) {
    const trendDirection = this.defineMarketTrend(currentPrice, ema50, ema200);

    // 计算趋势强度
    const ema50Slope = this.calculateSlope(prices.slice(-10), 10);
    const distanceFromEma200 = Math.abs(currentPrice - ema200) / ema200;

    // 转换为原有格式
    if (trendDirection === 'UPTREND') {
      return {
        direction: 'bullish',
        strength: Math.min(Math.abs(ema50Slope) * 100, 10),
        confidence: this.calculateTrendConfidence(true, distanceFromEma200)
      };
    } else if (trendDirection === 'DOWNTREND') {
      return {
        direction: 'bearish',
        strength: Math.min(Math.abs(ema50Slope) * 100, 10),
        confidence: this.calculateTrendConfidence(false, distanceFromEma200)
      };
    } else {
      return {
        direction: 'consolidation',
        strength: 0,
        confidence: 0
      };
    }
  }

  /**
   * 核心能力2: 入场信号模块 (The Entry Trigger)
   * 在确认趋势方向上寻找高概率的回调结束点
   */
  findEntrySignal(historicalData, currentCandle, ema20, trendDirection) {
    if (trendDirection === 'consolidation') {
      return { valid: false, reason: '震荡市场，空仓观望' };
    }

    const recentCandles = historicalData.slice(-3); // 最近3根K线
    const currentPrice = currentCandle.close;

    // 1. 寻找回调
    const pullbackDetected = this.detectPullback(recentCandles, ema20, trendDirection);
    if (!pullbackDetected.found) {
      return { valid: false, reason: pullbackDetected.reason };
    }

    // 2. 寻找确认K线 (Price Action Confirmation)
    const confirmationSignal = this.findConfirmationCandle(recentCandles, trendDirection);
    if (!confirmationSignal.valid) {
      return { valid: false, reason: confirmationSignal.reason };
    }

    return {
      valid: true,
      reason: `${trendDirection === 'bullish' ? '牛市' : '熊市'}回调+确认K线`,
      confirmationType: confirmationSignal.type
    };
  }

  /**
   * 检测回调
   */
  detectPullback(recentCandles, ema20, trendDirection) {
    if (trendDirection === 'bullish') {
      // 牛市中检查是否有K线触及EMA20
      const touchedEMA20 = recentCandles.some(candle => candle[3] <= ema20 * 1.005); // 低点触及EMA20
      if (touchedEMA20) {
        return { found: true, reason: '检测到牛市回调至EMA20' };
      }
    } else if (trendDirection === 'bearish') {
      // 熊市中检查是否有K线触及EMA20
      const touchedEMA20 = recentCandles.some(candle => candle[2] >= ema20 * 0.995); // 高点触及EMA20
      if (touchedEMA20) {
        return { found: true, reason: '检测到熊市反弹至EMA20' };
      }
    }

    return { found: false, reason: '未检测到有效回调' };
  }

  /**
   * 寻找确认K线形态
   */
  findConfirmationCandle(recentCandles, trendDirection) {
    if (recentCandles.length < 2) {
      return { valid: false, reason: 'K线数据不足' };
    }

    const currentCandle = recentCandles[recentCandles.length - 1];
    const prevCandle = recentCandles[recentCandles.length - 2];

    const [, currentOpen, currentHigh, currentLow, currentClose] = currentCandle;
    const [, prevOpen, prevHigh, prevLow, prevClose] = prevCandle;

    if (trendDirection === 'bullish') {
      // 寻找看涨确认形态

      // 看涨吞没形态
      if (currentClose > prevOpen && currentOpen < prevClose && currentClose > currentOpen) {
        return { valid: true, type: '看涨吞没', reason: '看涨吞没形态确认' };
      }

      // 锤子线形态
      const bodySize = Math.abs(currentClose - currentOpen);
      const lowerShadow = Math.min(currentOpen, currentClose) - currentLow;
      const upperShadow = currentHigh - Math.max(currentOpen, currentClose);

      if (lowerShadow > bodySize * 2 && upperShadow < bodySize && currentClose > currentOpen) {
        return { valid: true, type: '锤子线', reason: '锤子线形态确认' };
      }

    } else if (trendDirection === 'bearish') {
      // 寻找看跌确认形态

      // 看跌吞没形态
      if (currentClose < prevOpen && currentOpen > prevClose && currentClose < currentOpen) {
        return { valid: true, type: '看跌吞没', reason: '看跌吞没形态确认' };
      }

      // 倒锤子线形态
      const bodySize = Math.abs(currentClose - currentOpen);
      const upperShadow = currentHigh - Math.max(currentOpen, currentClose);
      const lowerShadow = Math.min(currentOpen, currentClose) - currentLow;

      if (upperShadow > bodySize * 2 && lowerShadow < bodySize && currentClose < currentOpen) {
        return { valid: true, type: '倒锤子线', reason: '倒锤子线形态确认' };
      }
    }

    return { valid: false, reason: '未找到有效确认K线形态' };
  }

  /**
   * 检查入场信号（保持兼容性）
   */
  checkEntrySignal(historicalData, currentCandle, ema20, trendDirection) {
    return this.findEntrySignal(historicalData, currentCandle, ema20, trendDirection);
  }

  /**
   * K线形态确认
   */
  confirmCandlestickPattern(recentCandles, trendDirection) {
    if (recentCandles.length < 2) {
      return { valid: false, reason: 'K线数据不足' };
    }
    
    const currentCandle = recentCandles[recentCandles.length - 1];
    const [timestamp, open, high, low, close, volume] = currentCandle;
    
    const bodySize = Math.abs(close - open);
    const totalSize = high - low;
    const bodyRatio = totalSize > 0 ? bodySize / totalSize : 0;
    
    // 检查实体比例
    if (bodyRatio < this.config.min_candle_body_ratio) {
      return { valid: false, reason: 'K线实体过小' };
    }
    
    if (trendDirection === 'bullish') {
      // 牛市中寻找看涨形态
      const isBullishCandle = close > open;
      const strongBullish = isBullishCandle && bodyRatio > 0.7;
      
      if (strongBullish) {
        return { valid: true, reason: '强势看涨K线确认' };
      }
    } else if (trendDirection === 'bearish') {
      // 熊市中寻找看跌形态
      const isBearishCandle = close < open;
      const strongBearish = isBearishCandle && bodyRatio > 0.7;
      
      if (strongBearish) {
        return { valid: true, reason: '强势看跌K线确认' };
      }
    }
    
    return { valid: false, reason: 'K线形态不符合要求' };
  }

  /**
   * 核心能力3: 风险与仓位管理模块 (The Risk Manager)
   * 计算结构性止损位置和头寸规模
   */
  calculateInitialStopLoss(historicalData, entryPrice, direction) {
    const recentCandles = historicalData.slice(-5); // 最近5根K线

    if (direction === 'bullish') {
      // 买入信号：止损设在确认K线最低点或回调波段最低点下方
      const recentLows = recentCandles.map(candle => candle[3]); // 低点
      const structuralLow = Math.min(...recentLows);

      // 在结构性低点下方设置止损，使用ATR作为缓冲
      const atr = this.calculateATRFromCandles(recentCandles);
      return structuralLow - (atr * 0.5); // 0.5倍ATR缓冲

    } else {
      // 卖出信号：止损设在确认K线最高点或回调波段最高点上方
      const recentHighs = recentCandles.map(candle => candle[2]); // 高点
      const structuralHigh = Math.max(...recentHighs);

      const atr = this.calculateATRFromCandles(recentCandles);
      return structuralHigh + (atr * 0.5); // 0.5倍ATR缓冲
    }
  }

  /**
   * 从K线数据计算ATR
   */
  calculateATRFromCandles(candles) {
    if (candles.length < 2) return 0;

    let trSum = 0;
    for (let i = 1; i < candles.length; i++) {
      const [, , high, low, close] = candles[i];
      const [, , , , prevClose] = candles[i - 1];

      const tr = Math.max(
        high - low,
        Math.abs(high - prevClose),
        Math.abs(low - prevClose)
      );
      trSum += tr;
    }

    return trSum / (candles.length - 1);
  }

  /**
   * 计算仓位大小
   */
  calculatePositionSize(entryPrice, stopLoss) {
    const riskPerTrade = this.config.risk_per_trade;
    const stopDistance = Math.abs(entryPrice - stopLoss);

    if (stopDistance === 0) return 0;

    return riskPerTrade / (stopDistance / entryPrice);
  }

  /**
   * 计算止损价格（保持兼容性）
   */
  calculateStopLoss(entryPrice, atr, direction) {
    // 使用ATR方法作为备选
    const stopDistance = atr * this.config.atr_multiplier;

    if (direction === 'bullish') {
      return entryPrice - stopDistance;
    } else {
      return entryPrice + stopDistance;
    }
  }

  /**
   * 计算斜率
   */
  calculateSlope(prices, period) {
    if (prices.length < period) return 0;
    
    const recentPrices = prices.slice(-period);
    const firstPrice = recentPrices[0];
    const lastPrice = recentPrices[recentPrices.length - 1];
    
    return (lastPrice - firstPrice) / firstPrice / period;
  }

  /**
   * 计算趋势置信度
   */
  calculateTrendConfidence(isBullish, distanceFromEma200) {
    let confidence = 50; // 基础置信度
    
    // 距离EMA200越远，置信度越高
    confidence += Math.min(distanceFromEma200 * 1000, 30);
    
    return Math.min(confidence, 95);
  }

  /**
   * 创建信号对象
   */
  createSignal(signal_type, signal_strength, reason, details = {}) {
    return {
      signal_type,
      signal_strength,
      reason,
      strategy: this.config.name,
      timestamp: Date.now(),
      ...details
    };
  }

  /**
   * 核心能力4: 离场管理模块 (The Exit Strategist)
   * 使用追踪止损让利润奔跑，直到趋势反转
   */
  manageTrailingStop(openPosition, historicalData, currentPrice) {
    const direction = openPosition.direction;
    const recentCandles = historicalData.slice(-20); // 最近20根K线

    // 方法1: 均线追踪止损（推荐）
    const ema20 = this.calculateEMA20(recentCandles);
    const emaTrailingStop = this.checkEMATrailingStop(currentPrice, ema20, direction);

    if (emaTrailingStop.shouldExit) {
      return {
        shouldExit: true,
        reason: emaTrailingStop.reason,
        method: 'EMA追踪'
      };
    }

    // 方法2: ATR通道追踪止损（Chandelier Exit）
    const chandelierStop = this.calculateChandelierExit(recentCandles, direction);
    const chandelierTrailingStop = this.checkChandelierTrailingStop(currentPrice, chandelierStop, direction);

    if (chandelierTrailingStop.shouldExit) {
      return {
        shouldExit: true,
        reason: chandelierTrailingStop.reason,
        method: 'Chandelier追踪'
      };
    }

    return { shouldExit: false, reason: '继续持有' };
  }

  /**
   * EMA追踪止损检查
   */
  checkEMATrailingStop(currentPrice, ema20, direction) {
    if (direction === 'bullish') {
      // 多头：收盘价跌破EMA20
      if (currentPrice < ema20) {
        return { shouldExit: true, reason: '收盘价跌破EMA20' };
      }
    } else {
      // 空头：收盘价突破EMA20
      if (currentPrice > ema20) {
        return { shouldExit: true, reason: '收盘价突破EMA20' };
      }
    }

    return { shouldExit: false, reason: '价格维持在EMA20正确侧' };
  }

  /**
   * 计算Chandelier Exit
   */
  calculateChandelierExit(recentCandles, direction) {
    const atr = this.calculateATRFromCandles(recentCandles);
    const atrMultiplier = 3; // 3倍ATR

    if (direction === 'bullish') {
      // 多头：过去20根K线最高价减去3倍ATR
      const highest = Math.max(...recentCandles.map(candle => candle[2]));
      return highest - (atr * atrMultiplier);
    } else {
      // 空头：过去20根K线最低价加上3倍ATR
      const lowest = Math.min(...recentCandles.map(candle => candle[3]));
      return lowest + (atr * atrMultiplier);
    }
  }

  /**
   * Chandelier追踪止损检查
   */
  checkChandelierTrailingStop(currentPrice, chandelierStop, direction) {
    if (direction === 'bullish') {
      // 多头：价格跌破Chandelier止损线
      if (currentPrice < chandelierStop) {
        return { shouldExit: true, reason: `价格跌破Chandelier止损线${chandelierStop.toFixed(2)}` };
      }
    } else {
      // 空头：价格突破Chandelier止损线
      if (currentPrice > chandelierStop) {
        return { shouldExit: true, reason: `价格突破Chandelier止损线${chandelierStop.toFixed(2)}` };
      }
    }

    return { shouldExit: false, reason: 'Chandelier止损线未触发' };
  }

  /**
   * 计算EMA20
   */
  calculateEMA20(candles) {
    const prices = candles.map(candle => candle[4]); // 收盘价
    return this.technicalIndicators.calculateEMAFromPrices(prices, 20)[prices.length - 1];
  }

  /**
   * 移动止盈检查（保持兼容性）
   */
  checkTrailingStop(currentPrice, entryPrice, currentEMA20, direction) {
    return this.checkEMATrailingStop(currentPrice, currentEMA20, direction).shouldExit;
  }
}

module.exports = TrendFollowingStrategy;
