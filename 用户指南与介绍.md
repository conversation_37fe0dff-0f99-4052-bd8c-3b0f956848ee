好的，没问题。基于您提供的代码，我将为您创建两份文档：

1.  **技术指标与策略文档**：详细解释系统内部使用的所有技术指标、策略逻辑和风险管理模型。这份文档适合希望深入了解系统运作原理的开发者或高级交易员。
2.  **用户指南与介绍**：一份面向最终用户的操作手册，用通俗易懂的语言介绍系统是什么、如何使用以及不同配置的含义。

---

### 文档一：ScalpAlert 交易系统 - 技术指标与策略文档

**版本: 1.0**
**目标周期: 1小时超短线交易**

#### 1. 系统概述

本系统是一个为1小时K线周期设计的、高度自适应的量化交易信号生成系统。其核心设计哲学是**“市场状态识别 + 多时间周期确认 + 量化评分过滤 + 严格风险管理”**。系统避免使用单一策略，而是根据市场环境动态调整其交易逻辑，以提高在不同行情下的适应性和稳健性。

#### 2. 核心技术指标

系统使用了一系列经典且互补的技术指标来捕捉市场信息：

*   **相对强弱指数 (RSI - Relative Strength Index)**
    *   **周期**: 14
    *   **用途**: 主要用于判断市场的超买/超卖状态，是逆势策略的核心，也是趋势策略中寻找回调入场点的关键依据。
    *   **应用**:
        *   作为信号质量评分的核心组成部分（权重最高）。
        *   直接用于震荡行情下的买卖点判断。
        *   在趋势行情中，用于确认回调的深度。

*   **指数移动平均线 (EMA - Exponential Moving Average)**
    *   **周期**: 短期EMA(9), 长期EMA(21)
    *   **用途**: 判断短期趋势方向和多空力量对比。
    *   **应用**:
        *   金叉 (EMA9 > EMA21) 作为看涨信号的确认条件之一。
        *   死叉 (EMA9 < EMA21) 作为看跌信号的确认条件之一。
        *   作为信号质量评分的组成部分。

*   **平滑异同移动平均线 (MACD - Moving Average Convergence Divergence)**
    *   **参数**: (12, 26, 9)
    *   **用途**: 判断趋势的动能、强度和潜在的转折点。
    *   **应用**:
        *   **MACD柱状线 (Histogram)**: 柱状线由负转正或持续放大，视为看涨动能增强；反之亦然。
        *   **快慢线关系**: 金叉/死叉作为趋势确认的辅助信号。
        *   作为信号质量评分的加分项，用于确认信号的有效性。

*   **平均真实波幅 (ATR - Average True Range)**
    *   **周期**: 14
    *   **用途**: 衡量市场的平均波动率，是系统风险管理的核心。
    *   **应用**:
        *   **动态止损**: 止损价格 = `入场价 +/- (ATR * atr_multiplier)`。波动大时止损宽，波动小时止损窄。
        *   **动态仓位计算**: 仓位规模与ATR计算出的止损距离成反比，确保单笔风险恒定。
        *   **动态止盈**: 止盈目标也基于ATR的倍数进行初步设定。

*   **成交量比率 (Volume Ratio)**
    *   **计算**: `当前K线成交量 / 过去19根K线平均成交量`
    *   **用途**: 验证价格行为的有效性。价涨量增、价跌量增被认为是更可靠的信号。
    *   **应用**: 作为信号质量评分的关键组成部分，成交量显著放大的信号会获得更高评分。

#### 3. 核心交易策略与逻辑

##### 3.1 市场状态自适应策略
这是系统的“大脑”。系统首先通过 `identifyMarketState` 函数判断市场处于**趋势市 (Trending)** 还是 **震荡市 (Ranging)**，然后执行不同的子策略：

*   **趋势市策略 (`determineTrendingSignal`)**:
    *   **逻辑**: 顺势而为，等待回调买入或反弹卖出。
    *   **买入条件**: 在上升趋势中 (如4H趋势向上，EMA金叉)，等待价格回调且RSI回落至中性或偏卖区 (`< 50`) 时寻找买入信号。
    *   **目的**: 避免追高，在趋势的健康回调中安全入场。

*   **震荡市策略 (`determineRangingSignal`)**:
    *   **逻辑**: 区间操作，高抛低吸。
    *   **买入条件**: 当RSI进入预设的超卖区时 (如 `< rsi_strong_threshold`)，产生买入信号。
    *   **目的**: 在价格触及支撑/阻力区间的边缘时进行逆势操作。

##### 3.2 多时间周期分析 (MTF - Multi-Timeframe Analysis)
系统通过分析**日线、4小时线、1小时线**的趋势一致性，为当前操作提供宏观方向指引。
*   **分析**: 判断大、中、小三个周期的趋势是否同向。
*   **输出**: 给出 `long_only` (只做多)、`short_only` (只做空)、`long_bias` (多头倾向)、`wait` (观望) 等综合建议。
*   **应用**: 该建议是信号有效性的**最高级别过滤器**。例如，即使1小时图出现完美的买入信号，但若MTF建议为 `short_only`，该信号也会被拒绝。

##### 3.3 信号量化评分模型
所有潜在信号都会经过一个严格的评分系统 (`calculateQualityScore`)，得分范围0-100。
*   **评分维度**:
    *   **RSI状态** (最高40分): RSI越进入超卖区，得分越高。
    *   **EMA排列** (最高30分): 完美的金叉且价格在均线之上得分最高。
    *   **成交量** (最高30分): 成交量放大越显著，得分越高。
    *   **加分项**: W底形态(+10), MTF趋势一致(+15), MACD确认(+10) 等。
    *   **减分项**: M顶形态(-20, 若启用过滤)。
*   **应用**: 只有得分超过 `min_quality_score` 阈值的信号才会被采纳。

#### 4. 风险与资金管理

这是系统的安全基石，确保长期生存能力。

*   **动态止损 (ATR-Based Stop-Loss)**:
    *   基于ATR设定止损，自动适应市场波动性。

*   **基于风险的仓位规模 (Risk-Based Position Sizing)**:
    *   **核心公式**: `仓位百分比 = (用户风险百分比) / (止损距离百分比)`
    *   **逻辑**: 此公式确保了无论止损设置得宽或窄，每一笔交易如果触及止损，其亏损金额都等于你预设的账户风险百分比 (例如账户总资金的1%)。

*   **智能动态止盈 (Dynamic Take-Profit)**:
    *   **混合模型**: 止盈目标综合考虑了两个因素：
        1.  **风险回报比 (Risk/Reward Ratio)**: 基于ATR止损距离乘以一个预设的回报比率 (如2.0)。
        2.  **市场结构**: 自动寻找 ближайший显著的支撑位或阻力位。
    *   **决策**: 系统会取上述两个目标中**更保守（更容易达到）**的一个作为最终止盈价，提高了盈利的实现概率。

---

### 文档二：ScalpAlert 交易系统 - 用户指南与介绍

#### 1. 系统简介：您的智能交易副驾

欢迎使用 **ScalpAlert** 交易系统！

把它想象成一位经验丰富的交易分析师，24小时不知疲倦地为您监控市场。它不是一个简单的“买卖”信号灯，而是一个**智能的交易决策辅助系统**。

它的核心任务是，在1小时的短线交易中，为您找到**高质量、高概率、且风险可控**的交易机会。

#### 2. 核心理念：我们如何思考市场？

*   **📈 市场是变化的，策略亦应如此**
    *   系统能自动识别当前是“趋势行情”还是“震荡行情”，并采用最适合的策略。趋势中顺势而为，震荡中高抛低吸。

*   **🛡️ 安全第一：先算风险，再算收益**
    *   系统最重要的功能是帮您管理风险。它会根据市场波动性自动计算止损位置，并根据您的风险偏好精确计算出每一笔应该投入多少资金。

*   **🎯 宁缺毋滥：质量重于数量**
    *   系统会对每一个潜在的交易机会进行严格的“打分”，只有多个指标同时确认、得分足够高的“五星好评”信号，才会推送给您。

*   **🔭 高瞻远瞩：顺大势，逆小势**
    *   系统会同时关注日线和4小时的大趋势，确保您的短线操作符合市场的“大方向”，极大提高胜率。

#### 3. 如何使用：选择您的交易风格

系统内置了三种预设配置，您可以根据自己的风险偏好和交易经验一键选择。

*   **👤 保守型 (Conservative)**
    *   **适合谁？**: 刚入门的新手、风险厌恶者，或希望在熊市中稳健操作的用户。
    *   **特点**:
        *   **信号极严**: 只推送最高质量的信号，宁可错过绝不犯错。
        *   **风险最低**: 每笔交易的风险和投入资金都控制在最低水平。
        *   **交易频率**: 低。

*   **⚖️ 平衡型 (Balanced)**
    *   **适合谁？**: 有一定交易经验，希望在风险和机会之间取得平衡的用户。**这是我们推荐的默认选项**。
    *   **特点**:
        *   **信号优质**: 过滤掉大部分噪音，提供可靠的交易信号。
        *   **风险适中**: 风险控制合理，同时能抓住大部分显著的交易机会。
        *   **交易频率**: 中等。

*   **🚀 激进型 (Aggressive)**
    *   **适合谁？**: 经验丰富的高级交易员，能承受更高风险以追求更高回报，或在牛市中希望捕捉更多机会的用户。
    *   **特点**:
        *   **信号更多**: 信号筛选条件相对宽松，会捕捉更多潜在机会。
        *   **风险更高**: 每笔交易的风险和投入资金相对更高。
        *   **交易频率**: 高。

#### 4. 理解信号通知

当系统发现一个高质量的交易机会时，您会收到类似下面的通知：

```
--- 新信号通知 ---
- 资产: 比特币 (Bitcoin)
- 信号类型: 买入 (Buy)
- 信号强度: Strong (强)
- 质量评分: 85/100
- 核心理由: 趋势市强买入：价格回调至支撑 + MACD确认
--------------------
- 入场价: $65,000
- 止损价: $64,200  (保护您的本金)
- 止盈价: $66,600  (锁定您的利润)
- 建议仓位: 5.5% (基于您总资金1%的风险设置)
--------------------
```
*   **信号强度/质量评分**: 帮您判断这个机会有多好。
*   **核心理由**: 告诉您系统为什么会发出这个信号。
*   **止损价/止盈价**: 清晰的风险和回报目标。
*   **建议仓位**: **至关重要！** 这是系统根据您的风险偏好计算出的科学投入金额，请严格参考。

#### 5. 重要使用建议

*   **⚠️ 这不是全自动交易机器人**
    *   系统提供的是高质量的**决策建议**。最终的交易决定权在您手中。您可以结合自己对市场的判断，决定是否执行该信号。

*   **📰 关注重大新闻**
    *   本系统是基于技术分析的，无法预测重大新闻事件（如监管政策、宏观经济数据）对市场的冲击。在重大新闻发布前后，请谨慎交易。

*   **🧪 从模拟盘开始**
    *   强烈建议您先在模拟账户中使用本系统，熟悉其信号特点和节奏后，再投入真实资金。

*   **🧐 定期回顾**
    *   定期回顾您根据信号进行的交易，总结经验。交易是持续学习和适应的过程，ScalpAlert是您在这个过程中的强大工具。

**祝您交易顺利！**