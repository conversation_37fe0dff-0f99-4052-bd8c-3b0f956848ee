import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { signalAPI } from '../utils/api';
import SignalLogic from '../components/SignalLogic';

const Analysis = () => {
  const navigate = useNavigate();
  const [selectedAsset, setSelectedAsset] = useState('binancecoin');
  const [analysisData, setAnalysisData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const assets = [
    { id: 'all', name: '全部', fullName: 'All Assets' },
    { id: 'bitcoin', name: 'BTC', fullName: 'Bitcoin' },
    { id: 'ethereum', name: 'ETH', fullName: 'Ethereum' },
    { id: 'binancecoin', name: 'BNB', fullName: 'BNB' },
    { id: 'solana', name: 'S<PERSON>', fullName: 'Solana' }
  ];

  const fetchAnalysis = async (asset) => {
    setLoading(true);
    setError(null);
    try {
      if (asset === 'all') {
        // 获取所有资产的分析数据
        const allAssets = ['bitcoin', 'ethereum', 'binancecoin', 'solana'];
        const promises = allAssets.map(a => signalAPI.getCurrentAnalysis(a));
        const responses = await Promise.all(promises);

        const allData = responses.map((response, index) => ({
          asset: allAssets[index],
          ...response.data.data
        })).filter(data => data.success !== false);

        setAnalysisData({ isMultiple: true, assets: allData });
      } else {
        // 获取单个资产的详细分析数据
        const response = await signalAPI.getCurrentAnalysis(asset);
        if (response.data.success) {
          // 确保返回的是单个资产的详细数据，不是多资产格式
          const singleAssetData = {
            ...response.data.data,
            isMultiple: false
          };

          setAnalysisData(singleAssetData);
        } else {
          setError(response.data.message || '获取分析数据失败');
        }
      }
    } catch (err) {
      console.error('获取市场分析失败:', err);
      setError('获取分析数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalysis(selectedAsset);
  }, [selectedAsset]);

  const handleAssetChange = (assetId) => {
    setSelectedAsset(assetId);
  };

  const handleRefresh = () => {
    fetchAnalysis(selectedAsset);
  };

  const handleAssetCardClick = (assetId) => {
    setSelectedAsset(assetId);
    // 立即获取新选择资产的数据
    fetchAnalysis(assetId);
  };

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-md mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-bold text-gray-900">市场分析</h1>
            <button
              onClick={handleRefresh}
              disabled={loading}
              className="px-3 py-1 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? '刷新中...' : '刷新'}
            </button>
          </div>
        </div>
      </div>

      {/* Asset Selector */}
      <div className="max-w-md mx-auto px-4 py-4">
        <div className="bg-white rounded-lg shadow p-4">
          <h2 className="text-sm font-medium text-gray-900 mb-3">选择资产</h2>
          <div className="grid grid-cols-5 gap-2">
            {assets.map((asset) => (
              <button
                key={asset.id}
                onClick={() => handleAssetChange(asset.id)}
                className={`p-3 rounded-lg text-center transition-colors ${
                  selectedAsset === asset.id
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <div className="font-medium text-sm">{asset.name}</div>
                <div className="text-xs opacity-75">{asset.fullName}</div>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Analysis Content */}
      <div className="max-w-md mx-auto px-4">
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <div className="flex items-center">
              <div className="text-red-600 text-sm">{error}</div>
            </div>
          </div>
        )}

        {analysisData && (
          <div className="space-y-4">

            {analysisData.isMultiple && selectedAsset === 'all' ? (
              // 全部资产视图
              <div className="space-y-4">
                {analysisData.assets.map((assetData, index) => {
                  const assetInfo = assets.find(a => a.id === assetData.asset);
                  return (
                    <div
                      key={assetData.asset}
                      className="bg-white rounded-lg shadow p-4 cursor-pointer hover:shadow-md transition-shadow"
                      onClick={() => handleAssetCardClick(assetData.asset)}
                    >
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="text-lg font-bold text-gray-900">
                          {assetInfo?.name}
                        </h3>
                        <div className="text-right">
                          <div className="text-lg font-bold text-gray-900">
                            ${assetData.price?.toLocaleString()}
                          </div>
                          <div className={`text-sm ${
                            assetData.price_change_24h >= 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {assetData.price_change_24h >= 0 ? '+' : ''}
                            {assetData.price_change_24h?.toFixed(2)}%
                          </div>
                        </div>
                      </div>

                      {/* 简化的信号信息 */}
                      <div className="grid grid-cols-3 gap-4 text-sm mb-3">
                        <div className="text-center">
                          <div className="text-gray-500">RSI</div>
                          <div className="font-medium">{assetData.rsi?.toFixed(1)}</div>
                        </div>
                        <div className="text-center">
                          <div className="text-gray-500">信号强度</div>
                          <div className={`font-medium ${
                            assetData.signal_strength === 'strong' ? 'text-green-600' :
                            assetData.signal_strength === 'medium' ? 'text-yellow-600' :
                            assetData.signal_strength === 'weak' ? 'text-orange-600' : 'text-gray-600'
                          }`}>
                            {assetData.signal_strength === 'strong' ? '强' :
                             assetData.signal_strength === 'medium' ? '中' :
                             assetData.signal_strength === 'weak' ? '弱' : '跳过'}
                          </div>
                        </div>
                        <div className="text-center">
                          <div className="text-gray-500">信号类型</div>
                          <div className={`font-medium ${
                            assetData.signal_type === 'buy' ? 'text-green-600' :
                            assetData.signal_type === 'sell' ? 'text-red-600' : 'text-gray-600'
                          }`}>
                            {assetData.signal_type === 'buy' ? '买入' :
                             assetData.signal_type === 'sell' ? '卖出' : '持有'}
                          </div>
                        </div>
                      </div>

                      {/* 点击提示 */}
                      <div className="text-center text-xs text-blue-600 border-t pt-2">
                        点击查看详细分析 →
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              // 单个资产详细视图
              <div className="space-y-4">
                {/* Price Info */}
                <div className="bg-white rounded-lg shadow p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-lg font-bold text-gray-900">
                      {assets.find(a => a.id === selectedAsset)?.name}
                    </h3>
                    <div className="text-right">
                      <div className="text-xl font-bold text-gray-900">
                        ${analysisData.price?.toLocaleString()}
                      </div>
                      <div className={`text-sm ${
                        analysisData.price_change_24h >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {analysisData.price_change_24h >= 0 ? '+' : ''}
                        {analysisData.price_change_24h?.toFixed(2)}%
                      </div>
                    </div>
                  </div>
                </div>

                {/* Signal Logic Component */}
                <SignalLogic data={analysisData} />
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default Analysis;
