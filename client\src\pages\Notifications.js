import React, { useState, useEffect } from 'react';
import { Bell, Check, AlertTriangle, TrendingUp, BarChart3, RefreshCw } from 'lucide-react';
import { notificationAPI } from '../utils/api';
import { formatDate } from '../utils/api';
import toast from 'react-hot-toast';

const Notifications = () => {
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [filter, setFilter] = useState('all'); // all, unread, read

  // 获取通知列表
  const fetchNotifications = async () => {
    try {
      const response = await notificationAPI.getList({ limit: 50 });
      let notificationData = response.data.data.notifications;

      // 客户端过滤
      if (filter === 'unread') {
        notificationData = notificationData.filter(n => !n.is_read);
      } else if (filter === 'read') {
        notificationData = notificationData.filter(n => n.is_read);
      }

      setNotifications(notificationData);
    } catch (error) {
      console.error('获取通知失败:', error);
      toast.error('获取通知失败');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // 标记为已读
  const markAsRead = async (notificationId) => {
    try {
      await notificationAPI.markAsRead(notificationId);
      setNotifications(prev =>
        prev.map(n =>
          n.id === notificationId ? { ...n, is_read: 1 } : n
        )
      );
    } catch (error) {
      console.error('标记已读失败:', error);
      toast.error('标记已读失败');
    }
  };

  // 全部标记为已读
  const markAllAsRead = async () => {
    try {
      const unreadNotifications = notifications.filter(n => !n.is_read);
      await Promise.all(
        unreadNotifications.map(n => notificationAPI.markAsRead(n.id))
      );
      setNotifications(prev =>
        prev.map(n => ({ ...n, is_read: 1 }))
      );
      toast.success('已全部标记为已读');
    } catch (error) {
      console.error('批量标记已读失败:', error);
      toast.error('批量标记已读失败');
    }
  };

  // 刷新通知
  const handleRefresh = () => {
    setRefreshing(true);
    fetchNotifications();
  };

  // 获取通知图标
  const getNotificationIcon = (type) => {
    switch (type) {
      case 'signal':
        return <TrendingUp className="text-primary-600" size={20} />;
      case 'warning':
        return <AlertTriangle className="text-warning-600" size={20} />;
      case 'report':
        return <BarChart3 className="text-success-600" size={20} />;
      default:
        return <Bell className="text-gray-600" size={20} />;
    }
  };

  // 获取通知背景色
  const getNotificationBg = (type, isRead) => {
    if (isRead) return 'bg-white';

    switch (type) {
      case 'signal':
        return 'bg-primary-50 border-primary-200';
      case 'warning':
        return 'bg-warning-50 border-warning-200';
      case 'report':
        return 'bg-success-50 border-success-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  useEffect(() => {
    fetchNotifications();
  }, [filter]);

  if (loading) {
    return (
      <div className="mobile-content">
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="card">
              <div className="skeleton h-4 w-3/4 mb-2"></div>
              <div className="skeleton h-6 w-full"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  const unreadCount = notifications.filter(n => !n.is_read).length;

  return (
    <div className="mobile-content">
      {/* 操作栏 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="input text-sm px-3 py-2"
          >
            <option value="all">全部</option>
            <option value="unread">未读</option>
            <option value="read">已读</option>
          </select>
        </div>
        <div className="flex items-center space-x-2">
          {unreadCount > 0 && (
            <button
              onClick={markAllAsRead}
              className="btn-outline text-sm px-3 py-2"
            >
              <Check size={16} className="mr-1" />
              全部已读
            </button>
          )}
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="btn-outline px-3 py-2"
          >
            <RefreshCw size={16} className={`mr-1 ${refreshing ? 'animate-spin' : ''}`} />
            刷新
          </button>
        </div>
      </div>

      {/* 通知统计 */}
      {notifications.length > 0 && (
        <div className="card mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium text-gray-900">通知概览</h3>
              <p className="text-sm text-gray-600">
                共 {notifications.length} 条通知
              </p>
            </div>
            <div className="text-right">
              <p className="text-2xl font-bold text-primary-600">
                {unreadCount}
              </p>
              <p className="text-sm text-gray-600">未读</p>
            </div>
          </div>
        </div>
      )}

      {/* 通知列表 */}
      {notifications.length > 0 ? (
        <div className="space-y-3">
          {notifications.map((notification) => (
            <div
              key={notification.id}
              onClick={() => !notification.is_read && markAsRead(notification.id)}
              className={`p-4 border rounded-lg cursor-pointer transition-all ${
                getNotificationBg(notification.type, notification.is_read)
              } ${!notification.is_read ? 'hover:shadow-md' : ''}`}
            >
              <div className="flex items-start space-x-3">
                {/* 通知图标 */}
                <div className={`w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0 ${
                  notification.type === 'signal' ? 'bg-primary-100' :
                  notification.type === 'warning' ? 'bg-warning-100' :
                  notification.type === 'report' ? 'bg-success-100' :
                  'bg-gray-100'
                }`}>
                  {getNotificationIcon(notification.type)}
                </div>

                {/* 通知内容 */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className={`font-medium ${
                      notification.is_read ? 'text-gray-700' : 'text-gray-900'
                    }`}>
                      {notification.title}
                    </h4>
                    {!notification.is_read && (
                      <div className="w-2 h-2 bg-primary-600 rounded-full flex-shrink-0"></div>
                    )}
                  </div>

                  <p className={`text-sm mb-2 ${
                    notification.is_read ? 'text-gray-500' : 'text-gray-700'
                  }`}>
                    {notification.message}
                  </p>

                  <p className="text-xs text-gray-500">
                    {formatDate(notification.sent_at)}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <Bell className="mx-auto text-gray-400 mb-4" size={64} />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {filter === 'unread' ? '暂无未读通知' :
             filter === 'read' ? '暂无已读通知' : '暂无通知'}
          </h3>
          <p className="text-gray-600 mb-6">
            {filter === 'all'
              ? '当有新的交易信号或重要提醒时，会在这里显示'
              : '切换到其他筛选条件查看更多通知'}
          </p>
        </div>
      )}
    </div>
  );
};

export default Notifications;
