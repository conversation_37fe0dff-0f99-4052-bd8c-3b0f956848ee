{"name": "scalp-alert-h5", "version": "1.0.0", "description": "ScalpAlert 移动端H5交易信号应用", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "cd server && npm run dev", "client:dev": "cd client && npm start", "build": "cd client && npm run build", "start": "cd server && npm start", "install:all": "npm install && cd server && npm install && cd ../client && npm install && cd ../backtest && npm install", "backtest": "cd backtest && npm run"}, "keywords": ["trading", "scalping", "h5", "mobile", "react"], "author": "ScalpAlert Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}