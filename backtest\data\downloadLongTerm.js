/**
 * 长期历史数据下载脚本
 * 专门用于下载更长生命周期的数据用于策略验证
 */

const DataDownloader = require('./downloader');

async function downloadLongTermData() {
  const downloader = new DataDownloader();
  
  console.log('🎯 长期历史数据下载器');
  console.log('='.repeat(50));
  
  // 定义下载任务
  const downloadTasks = [
    {
      symbol: 'ETHUSDT',
      interval: '1h',
      startYear: '2017',
      description: 'ETH 1小时数据 (2017-现在)'
    },
    {
      symbol: 'ETHUSDT', 
      interval: '4h',
      startYear: '2017',
      description: 'ETH 4小时数据 (2017-现在)'
    },
    {
      symbol: 'ETHUSDT',
      interval: '1d', 
      startYear: '2017',
      description: 'ETH 日线数据 (2017-现在)'
    },
    {
      symbol: 'BTCUSDT',
      interval: '1h',
      startYear: '2017', 
      description: 'BTC 1小时数据 (2017-现在)'
    },
    {
      symbol: 'BTCUSDT',
      interval: '4h',
      startYear: '2017',
      description: 'BTC 4小时数据 (2017-现在)'
    }
  ];
  
  // 获取用户选择
  const taskIndex = process.argv[2] ? parseInt(process.argv[2]) - 1 : null;
  
  if (taskIndex !== null && taskIndex >= 0 && taskIndex < downloadTasks.length) {
    // 下载指定任务
    const task = downloadTasks[taskIndex];
    await downloadSingleTask(downloader, task);
  } else {
    // 显示菜单
    console.log('📋 可用下载任务:');
    downloadTasks.forEach((task, index) => {
      console.log(`   ${index + 1}. ${task.description}`);
    });
    
    console.log('\n💡 使用方法:');
    console.log('   node downloadLongTerm.js 1    # 下载任务1');
    console.log('   node downloadLongTerm.js 2    # 下载任务2');
    console.log('   node downloadLongTerm.js all  # 下载所有任务');
    
    // 如果参数是 'all'，下载所有任务
    if (process.argv[2] === 'all') {
      console.log('\n🚀 开始下载所有任务...');
      for (let i = 0; i < downloadTasks.length; i++) {
        console.log(`\n📊 任务 ${i + 1}/${downloadTasks.length}: ${downloadTasks[i].description}`);
        await downloadSingleTask(downloader, downloadTasks[i]);
        
        // 任务间延迟，避免API限制
        if (i < downloadTasks.length - 1) {
          console.log('⏳ 等待5秒后继续下一个任务...');
          await new Promise(resolve => setTimeout(resolve, 5000));
        }
      }
      console.log('\n🎉 所有下载任务完成！');
    }
  }
}

async function downloadSingleTask(downloader, task) {
  try {
    const startDate = new Date(`${task.startYear}-01-01T00:00:00Z`);
    const endDate = new Date();
    
    console.log(`\n📊 下载: ${task.description}`);
    console.log(`📅 时间范围: ${task.startYear}年1月1日 至 现在`);
    console.log(`⏱️ 数据跨度: ${new Date().getFullYear() - parseInt(task.startYear)} 年`);
    
    const filepath = await downloader.downloadKlines(
      task.symbol, 
      task.interval, 
      startDate, 
      endDate
    );
    
    // 验证数据
    await downloader.validateData(filepath);
    
    console.log(`✅ ${task.description} 下载完成`);
    console.log(`📁 文件路径: ${filepath}`);
    
    return filepath;
    
  } catch (error) {
    console.error(`❌ ${task.description} 下载失败:`, error.message);
    throw error;
  }
}

// 专门用于策略验证的数据下载
async function downloadForStrategyValidation() {
  const downloader = new DataDownloader();
  
  console.log('🎯 策略验证数据下载');
  console.log('='.repeat(50));
  
  // 为两个策略下载最适合的数据
  const validationTasks = [
    {
      name: '趋势跟踪策略数据',
      symbol: 'ETHUSDT',
      interval: '4h',  // H4级别最适合趋势策略
      startYear: '2017',
      reason: '4小时级别适合趋势识别和跟踪'
    },
    {
      name: '震荡交易策略数据', 
      symbol: 'ETHUSDT',
      interval: '15m', // M15级别最适合震荡策略
      startYear: '2020', // 15分钟数据量大，从2020年开始
      reason: '15分钟级别适合震荡区间交易'
    },
    {
      name: '通用验证数据',
      symbol: 'ETHUSDT', 
      interval: '1h',  // 1小时作为中间级别
      startYear: '2017',
      reason: '1小时级别平衡趋势和震荡特征'
    }
  ];
  
  console.log('📋 策略验证数据下载计划:');
  validationTasks.forEach((task, index) => {
    console.log(`   ${index + 1}. ${task.name}`);
    console.log(`      ${task.symbol} ${task.interval} (${task.startYear}年起)`);
    console.log(`      原因: ${task.reason}`);
  });
  
  const choice = process.argv[3];
  if (choice === 'validation') {
    console.log('\n🚀 开始下载策略验证数据...');
    
    for (let i = 0; i < validationTasks.length; i++) {
      const task = validationTasks[i];
      console.log(`\n📊 ${i + 1}/${validationTasks.length}: ${task.name}`);
      
      try {
        const startDate = new Date(`${task.startYear}-01-01T00:00:00Z`);
        const endDate = new Date();
        
        const filepath = await downloader.downloadKlines(
          task.symbol,
          task.interval, 
          startDate,
          endDate
        );
        
        await downloader.validateData(filepath);
        console.log(`✅ ${task.name} 完成: ${filepath}`);
        
      } catch (error) {
        console.error(`❌ ${task.name} 失败:`, error.message);
      }
      
      // 任务间延迟
      if (i < validationTasks.length - 1) {
        console.log('⏳ 等待3秒...');
        await new Promise(resolve => setTimeout(resolve, 3000));
      }
    }
    
    console.log('\n🎉 策略验证数据下载完成！');
    console.log('\n💡 现在可以运行:');
    console.log('   npm run new-trend    # 使用4小时数据测试趋势策略');
    console.log('   npm run new-range    # 使用15分钟数据测试震荡策略');
    console.log('   npm run new-compare  # 对比两个策略');
  }
}

// 主函数
async function main() {
  try {
    const mode = process.argv[2];
    
    if (mode === 'validation') {
      await downloadForStrategyValidation();
    } else {
      await downloadLongTermData();
    }
    
  } catch (error) {
    console.error('❌ 程序执行失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main();
}

module.exports = {
  downloadLongTermData,
  downloadForStrategyValidation,
  downloadSingleTask
};
