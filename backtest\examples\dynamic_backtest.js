/**
 * 动态策略回测示例
 * 演示如何使用动态策略调整系统进行回测
 */

const path = require('path');
const DynamicBacktester = require('../engine/dynamicBacktester');
const DataAdapter = require('../adapters/dataAdapter');
const SignalAdapter = require('../adapters/signalAdapter');
const { strategyConfigs } = require('../config/backtest.config');
const { dynamicOptions, dynamicStrategyPresets } = require('../config/dynamicStrategy.config');

/**
 * 运行单个动态策略回测
 */
async function runSingleDynamicBacktest() {
  console.log('🚀 开始单个动态策略回测...\n');
  
  try {
    // 数据路径
    const dataPath = path.join(__dirname, '../data/ETHUSDT_1h.json');
    
    // 初始化回测引擎
    const backtester = new DynamicBacktester({
      initialCapital: 10000,
      tradingFee: 0.001
    });
    
    // 加载数据
    const dataAdapter = new DataAdapter();
    await dataAdapter.loadFromFile(dataPath);
    backtester.setDataAdapter(dataAdapter);
    
    // 设置信号适配器（需要传入dataAdapter）
    const signalAdapter = new SignalAdapter(dataAdapter);
    backtester.setSignalAdapter(signalAdapter);
    
    // 选择基础策略和动态选项
    const baseStrategy = strategyConfigs.balanced;  // 使用平衡策略作为基础
    const dynamicOpts = dynamicOptions.standard;   // 使用标准动态调整
    
    // 简化输出
    
    // 运行动态回测
    const results = await backtester.runDynamicBacktest(baseStrategy, dynamicOpts);
    
    // 显示结果
    console.log('\n📊 动态回测结果:');
    console.log('='.repeat(50));
    
    const metrics = results.metrics;
    console.log(`💰 总收益: ${(metrics.totalReturn * 100).toFixed(2)}%`);
    console.log(`📈 胜率: ${(metrics.winRate * 100).toFixed(2)}%`);
    console.log(`⚖️ 盈亏比: ${metrics.profitFactor.toFixed(2)}`);
    console.log(`📉 最大回撤: ${(metrics.maxDrawdown * 100).toFixed(2)}%`);
    console.log(`💸 总手续费: $${metrics.totalFees.toFixed(2)}`);
    console.log(`📊 总交易数: ${results.summary.totalTrades}`);
    console.log(`🔧 总调整数: ${results.summary.totalAdjustments}`);
    
    // 显示配置变化
    console.log('\n🔧 策略配置变化:');
    const baseConfig = results.config;
    const finalConfig = results.finalConfig;
    
    const keyParams = ['min_quality_score', 'rsi_strong_threshold', 'risk_reward_ratio', 'risk_strong'];
    keyParams.forEach(param => {
      if (Math.abs(baseConfig[param] - finalConfig[param]) > 0.001) {
        const change = ((finalConfig[param] - baseConfig[param]) / baseConfig[param] * 100).toFixed(1);
        console.log(`  ${param}: ${baseConfig[param]} → ${finalConfig[param].toFixed(3)} (${change > 0 ? '+' : ''}${change}%)`);
      }
    });
    
    // 导出结果
    const outputDir = backtester.exportDynamicResults('./backtest_results/dynamic');
    console.log(`\n📁 结果已导出到: ${outputDir}`);
    
    return results;
    
  } catch (error) {
    console.error('❌ 动态回测失败:', error.message);
    throw error;
  }
}

/**
 * 运行动态策略对比测试
 */
async function runDynamicComparison() {
  console.log('🔄 开始动态策略对比测试...\n');
  
  const dataPath = path.join(__dirname, '../data/ETHUSDT_1h.json');
  const results = [];
  
  // 测试不同的动态配置
  const testConfigs = [
    { base: strategyConfigs.conservative, dynamic: dynamicOptions.conservative, name: '保守动态' },
    { base: strategyConfigs.balanced, dynamic: dynamicOptions.standard, name: '平衡动态' },
    { base: strategyConfigs.qualityFirst, dynamic: dynamicOptions.aggressive, name: '质量激进' }
  ];
  
  for (const config of testConfigs) {
    process.stdout.write(`🧪 测试: ${config.name}...`);
    
    try {
      const backtester = new DynamicBacktester({
        initialCapital: 10000,
        tradingFee: 0.001
      });
      
      const dataAdapter = new DataAdapter();
      await dataAdapter.loadFromFile(dataPath);
      backtester.setDataAdapter(dataAdapter);
      
      const signalAdapter = new SignalAdapter(dataAdapter);
      backtester.setSignalAdapter(signalAdapter);
      
      const result = await backtester.runDynamicBacktest(config.base, config.dynamic);
      
      results.push({
        name: config.name,
        metrics: result.metrics,
        adjustments: result.summary.totalAdjustments,
        trades: result.summary.totalTrades
      });
      
      console.log(` ✅ 完成`);
      
    } catch (error) {
      console.log(` ❌ 失败`);
    }
  }
  
  // 生成对比报告
  console.log('\n📊 动态策略对比报告');
  console.log('='.repeat(80));
  console.log('策略名称           总收益%      胜率%     盈亏比     回撤%     交易数     调整数');
  console.log('-'.repeat(80));
  
  results.forEach(result => {
    const metrics = result.metrics;
    console.log(
      `${result.name.padEnd(15)} ` +
      `${(metrics.totalReturn * 100).toFixed(2).padStart(8)} ` +
      `${(metrics.winRate * 100).toFixed(2).padStart(8)} ` +
      `${metrics.profitFactor.toFixed(2).padStart(8)} ` +
      `${(metrics.maxDrawdown * 100).toFixed(2).padStart(8)} ` +
      `${result.trades.toString().padStart(8)} ` +
      `${result.adjustments.toString().padStart(8)}`
    );
  });
  
  // 找出最佳策略
  let bestStrategy = null;
  if (results.length > 0) {
    bestStrategy = results.reduce((best, current) =>
      current.metrics.totalReturn > best.metrics.totalReturn ? current : best
    );
  }
  
  if (bestStrategy) {
    console.log(`\n🏆 最佳动态策略: ${bestStrategy.name}`);
    console.log(`   总收益: ${(bestStrategy.metrics.totalReturn * 100).toFixed(2)}%`);
    console.log(`   胜率: ${(bestStrategy.metrics.winRate * 100).toFixed(2)}%`);
    console.log(`   盈亏比: ${bestStrategy.metrics.profitFactor.toFixed(2)}`);
  } else {
    console.log('\n❌ 没有成功的策略测试结果');
  }
  
  return results;
}

/**
 * 运行动态vs静态对比
 */
async function runDynamicVsStatic() {
  console.log('⚔️ 开始动态vs静态策略对比...\n');
  
  const dataPath = path.join(__dirname, '../data/ETHUSDT_1h.json');
  const baseStrategy = strategyConfigs.balanced;
  
  // 静态回测
  console.log('📊 运行静态回测...');
  const Backtester = require('../engine/backtester');
  const staticBT = new Backtester({
    initialCapital: 10000,
    tradingFee: 0.001
  });

  // 使用原始Backtester的loadData方法
  staticBT.loadData(dataPath);

  const staticResults = await staticBT.runBacktest(baseStrategy);
  
  // 动态回测
  console.log('🔧 运行动态回测...');
  const dynamicBT = new DynamicBacktester({
    initialCapital: 10000,
    tradingFee: 0.001
  });
  
  const dataAdapter2 = new DataAdapter();
  await dataAdapter2.loadFromFile(dataPath);
  dynamicBT.setDataAdapter(dataAdapter2);
  
  const signalAdapter2 = new SignalAdapter(dataAdapter2);
  dynamicBT.setSignalAdapter(signalAdapter2);
  
  const dynamicResults = await dynamicBT.runDynamicBacktest(baseStrategy, dynamicOptions.standard);
  
  // 对比结果
  console.log('\n⚔️ 动态vs静态对比结果');
  console.log('='.repeat(60));
  console.log('指标                静态策略        动态策略        改进');
  console.log('-'.repeat(60));
  
  const comparisons = [
    { name: '总收益%', static: staticResults.metrics.totalReturn * 100, dynamic: dynamicResults.metrics.totalReturn * 100 },
    { name: '胜率%', static: staticResults.metrics.winRate * 100, dynamic: dynamicResults.metrics.winRate * 100 },
    { name: '盈亏比', static: staticResults.metrics.profitFactor, dynamic: dynamicResults.metrics.profitFactor },
    { name: '最大回撤%', static: staticResults.metrics.maxDrawdown * 100, dynamic: dynamicResults.metrics.maxDrawdown * 100 },
    { name: '交易数', static: staticResults.summary.totalTrades, dynamic: dynamicResults.summary.totalTrades }
  ];
  
  comparisons.forEach(comp => {
    const improvement = ((comp.dynamic - comp.static) / Math.abs(comp.static) * 100).toFixed(1);
    const improvementStr = comp.name === '最大回撤%' ? 
      (comp.dynamic < comp.static ? `↓${Math.abs(improvement)}%` : `↑${improvement}%`) :
      (comp.dynamic > comp.static ? `↑${improvement}%` : `↓${Math.abs(improvement)}%`);
    
    console.log(
      `${comp.name.padEnd(15)} ` +
      `${comp.static.toFixed(2).padStart(12)} ` +
      `${comp.dynamic.toFixed(2).padStart(12)} ` +
      `${improvementStr.padStart(12)}`
    );
  });
  
  const overallImprovement = ((dynamicResults.metrics.totalReturn - staticResults.metrics.totalReturn) / Math.abs(staticResults.metrics.totalReturn) * 100).toFixed(1);
  console.log(`\n🎯 动态策略整体改进: ${overallImprovement > 0 ? '+' : ''}${overallImprovement}%`);
  
  return { static: staticResults, dynamic: dynamicResults };
}

// 主函数
async function main() {
  try {
    console.log('🎯 动态策略回测系统');
    console.log('='.repeat(50));
    
    // 选择运行模式
    const mode = process.argv[2] || 'single';
    
    switch (mode) {
      case 'single':
        await runSingleDynamicBacktest();
        break;
      case 'compare':
        await runDynamicComparison();
        break;
      case 'vs-static':
        await runDynamicVsStatic();
        break;
      default:
        console.log('使用方法:');
        console.log('  node dynamic_backtest.js single     - 运行单个动态回测');
        console.log('  node dynamic_backtest.js compare    - 运行动态策略对比');
        console.log('  node dynamic_backtest.js vs-static  - 运行动态vs静态对比');
    }
    
  } catch (error) {
    console.error('❌ 程序执行失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main();
}

module.exports = {
  runSingleDynamicBacktest,
  runDynamicComparison,
  runDynamicVsStatic
};
