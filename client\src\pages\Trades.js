import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FileText, Plus, Filter, TrendingUp, TrendingDown, Clock, DollarSign } from 'lucide-react';
import { tradeAPI } from '../utils/api';
import { formatCurrency, formatDate, getProfitLossColor, getAssetDisplayName } from '../utils/api';
import toast from 'react-hot-toast';

const Trades = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [trades, setTrades] = useState([]);
  const [filter, setFilter] = useState({
    status: 'all',
    asset: 'all',
  });
  const [showCreateModal, setShowCreateModal] = useState(false);

  // 获取交易列表
  const fetchTrades = async () => {
    try {
      const params = {
        limit: 50,
        ...(filter.status !== 'all' && { status: filter.status }),
        ...(filter.asset !== 'all' && { asset: filter.asset }),
      };

      const response = await tradeAPI.getList(params);
      setTrades(response.data.data.trades);
    } catch (error) {
      console.error('获取交易列表失败:', error);
      toast.error('获取交易列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理过滤器变化
  const handleFilterChange = (key, value) => {
    setFilter(prev => ({
      ...prev,
      [key]: value
    }));
  };

  useEffect(() => {
    fetchTrades();
  }, [filter]);

  if (loading) {
    return (
      <div className="mobile-content">
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="card">
              <div className="skeleton h-4 w-3/4 mb-2"></div>
              <div className="skeleton h-6 w-1/2 mb-2"></div>
              <div className="skeleton h-4 w-full"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="mobile-content">
      {/* 操作栏 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowCreateModal(true)}
            className="btn-primary px-4 py-2"
          >
            <Plus size={16} className="mr-1" />
            创建交易
          </button>
        </div>
      </div>

      {/* 过滤器 */}
      <div className="card mb-6">
        <div className="flex items-center mb-4">
          <Filter size={16} className="mr-2 text-gray-600" />
          <span className="font-medium text-gray-900">筛选条件</span>
        </div>

        <div className="grid grid-cols-2 gap-4">
          {/* 状态筛选 */}
          <div>
            <label className="label text-sm">交易状态</label>
            <select
              value={filter.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="input text-sm"
            >
              <option value="all">全部</option>
              <option value="open">持仓中</option>
              <option value="closed">已平仓</option>
            </select>
          </div>

          {/* 资产筛选 */}
          <div>
            <label className="label text-sm">交易对</label>
            <select
              value={filter.asset}
              onChange={(e) => handleFilterChange('asset', e.target.value)}
              className="input text-sm"
            >
              <option value="all">全部</option>
              <option value="bitcoin">BTC</option>
              <option value="ethereum">ETH</option>
              <option value="binancecoin">BNB</option>
              <option value="solana">SOL</option>
            </select>
          </div>
        </div>
      </div>

      {/* 交易列表 */}
      {trades.length > 0 ? (
        <div className="space-y-4">
          {trades.map((trade) => (
            <div
              key={trade.id}
              onClick={() => navigate(`/trades/${trade.id}`)}
              className="card cursor-pointer hover:shadow-md transition-shadow"
            >
              {/* 交易头部 */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                    trade.status === 'open' ? 'bg-primary-100' :
                    trade.profit_loss > 0 ? 'bg-success-100' : 'bg-danger-100'
                  }`}>
                    {trade.status === 'open' ? (
                      <Clock className="text-primary-600" size={20} />
                    ) : trade.profit_loss > 0 ? (
                      <TrendingUp className="text-success-600" size={20} />
                    ) : (
                      <TrendingDown className="text-danger-600" size={20} />
                    )}
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">
                      {getAssetDisplayName(trade.asset)}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {formatDate(trade.buy_time)}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <span className={`badge ${
                    trade.status === 'open' ? 'badge-primary' :
                    trade.profit_loss > 0 ? 'badge-success' : 'badge-danger'
                  }`}>
                    {trade.status === 'open' ? '持仓中' :
                     trade.profit_loss > 0 ? '盈利' : '亏损'}
                  </span>
                  {trade.is_compliant !== null && (
                    <p className="text-xs text-gray-500 mt-1">
                      {trade.is_compliant ? '遵守建议' : '偏离建议'}
                    </p>
                  )}
                </div>
              </div>

              {/* 交易详情 */}
              <div className="grid grid-cols-2 gap-4 mb-3">
                <div>
                  <p className="text-sm text-gray-600">买入价格</p>
                  <p className="font-semibold text-gray-900">
                    ${trade.buy_price?.toLocaleString()}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">投入金额</p>
                  <p className="font-semibold text-gray-900">
                    {formatCurrency(trade.buy_amount)}
                  </p>
                </div>
              </div>

              {/* 盈亏信息 */}
              {trade.status === 'closed' && (
                <div className="grid grid-cols-2 gap-4 pt-3 border-t border-gray-200">
                  <div>
                    <p className="text-sm text-gray-600">卖出价格</p>
                    <p className="font-semibold text-gray-900">
                      ${trade.sell_price?.toLocaleString()}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">盈亏</p>
                    <p className={`font-semibold ${getProfitLossColor(trade.profit_loss)}`}>
                      {trade.profit_loss > 0 ? '+' : ''}{formatCurrency(trade.profit_loss)}
                    </p>
                  </div>
                </div>
              )}

              {/* 止损止盈 */}
              {trade.status === 'open' && (
                <div className="grid grid-cols-2 gap-4 pt-3 border-t border-gray-200 text-sm">
                  <div>
                    <p className="text-gray-600">止损</p>
                    <p className="font-medium text-danger-600">
                      ${trade.stop_loss?.toLocaleString()}
                    </p>
                  </div>
                  <div>
                    <p className="text-gray-600">止盈</p>
                    <p className="font-medium text-success-600">
                      ${trade.take_profit?.toLocaleString()}
                    </p>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <FileText className="mx-auto text-gray-400 mb-4" size={64} />
          <h3 className="text-lg font-medium text-gray-900 mb-2">暂无交易记录</h3>
          <p className="text-gray-600 mb-6">
            {filter.status !== 'all' || filter.asset !== 'all'
              ? '当前筛选条件下没有找到交易记录'
              : '还没有创建任何交易记录'}
          </p>
          <button
            onClick={() => setShowCreateModal(true)}
            className="btn-primary"
          >
            <Plus size={20} className="mr-2" />
            创建第一笔交易
          </button>
        </div>
      )}

      {/* 创建交易模态框 */}
      {showCreateModal && (
        <CreateTradeModal
          onClose={() => setShowCreateModal(false)}
          onSuccess={() => {
            setShowCreateModal(false);
            fetchTrades();
          }}
        />
      )}
    </div>
  );
};

// 创建交易模态框组件
const CreateTradeModal = ({ onClose, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    asset: 'bitcoin',
    buyPrice: '',
    buyAmount: '',
    stopLoss: '',
    takeProfit: '',
    signalId: null,
  });

  const handleChange = (e) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || 0 : value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.buyPrice || !formData.buyAmount || !formData.stopLoss || !formData.takeProfit) {
      toast.error('请填写所有必填字段');
      return;
    }

    try {
      setLoading(true);
      await tradeAPI.create({
        asset: formData.asset,
        buyPrice: formData.buyPrice,
        buyAmount: formData.buyAmount,
        stopLoss: formData.stopLoss,
        takeProfit: formData.takeProfit,
        signalId: formData.signalId,
      });

      toast.success('交易创建成功');
      onSuccess();
    } catch (error) {
      console.error('创建交易失败:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">创建交易</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* 交易对 */}
            <div>
              <label className="label">交易对</label>
              <select
                name="asset"
                value={formData.asset}
                onChange={handleChange}
                className="input"
                disabled={loading}
              >
                <option value="bitcoin">BTC/USDT</option>
                <option value="ethereum">ETH/USDT</option>
                <option value="binancecoin">BNB/USDT</option>
                <option value="solana">SOL/USDT</option>
              </select>
            </div>

            {/* 买入价格 */}
            <div>
              <label className="label">买入价格 ($)</label>
              <input
                type="number"
                name="buyPrice"
                value={formData.buyPrice}
                onChange={handleChange}
                className="input"
                placeholder="请输入买入价格"
                step="0.01"
                disabled={loading}
              />
            </div>

            {/* 投入金额 */}
            <div>
              <label className="label">投入金额 ($)</label>
              <input
                type="number"
                name="buyAmount"
                value={formData.buyAmount}
                onChange={handleChange}
                className="input"
                placeholder="请输入投入金额"
                step="0.01"
                disabled={loading}
              />
            </div>

            {/* 止损价格 */}
            <div>
              <label className="label">止损价格 ($)</label>
              <input
                type="number"
                name="stopLoss"
                value={formData.stopLoss}
                onChange={handleChange}
                className="input"
                placeholder="请输入止损价格"
                step="0.01"
                disabled={loading}
              />
            </div>

            {/* 止盈价格 */}
            <div>
              <label className="label">止盈价格 ($)</label>
              <input
                type="number"
                name="takeProfit"
                value={formData.takeProfit}
                onChange={handleChange}
                className="input"
                placeholder="请输入止盈价格"
                step="0.01"
                disabled={loading}
              />
            </div>

            {/* 提交按钮 */}
            <div className="flex space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="btn-outline flex-1"
                disabled={loading}
              >
                取消
              </button>
              <button
                type="submit"
                className="btn-primary flex-1"
                disabled={loading}
              >
                {loading ? (
                  <div className="loading-spinner"></div>
                ) : (
                  '创建交易'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Trades;
