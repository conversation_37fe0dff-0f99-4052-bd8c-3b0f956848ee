{"name": "scalp-alert-client", "version": "1.0.0", "description": "ScalpAlert 移动端H5前端应用", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "react-scripts": "5.0.1", "axios": "^1.6.2", "recharts": "^2.8.0", "react-hot-toast": "^2.4.1", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32"}, "proxy": "http://localhost:3001"}