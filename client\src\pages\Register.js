import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Eye, EyeOff, UserPlus } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import toast from 'react-hot-toast';

const Register = () => {
  const navigate = useNavigate();
  const { register, loading } = useAuth();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    confirmPassword: '',
    capital: 5000,
    lossLimit: 3000,
  });

  const handleChange = (e) => {
    const { name, value, type } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'number' ? parseFloat(value) || 0 : value,
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // 表单验证
    if (!formData.username || !formData.password || !formData.confirmPassword) {
      toast.error('请填写所有必填字段');
      return;
    }

    if (formData.username.length < 3) {
      toast.error('用户名至少3个字符');
      return;
    }

    if (formData.password.length < 6) {
      toast.error('密码至少6个字符');
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      toast.error('两次输入的密码不一致');
      return;
    }

    if (formData.capital <= 0) {
      toast.error('初始本金必须大于0');
      return;
    }

    if (formData.lossLimit <= 0) {
      toast.error('亏损限制必须大于0');
      return;
    }

    const result = await register({
      username: formData.username,
      password: formData.password,
      capital: formData.capital,
      lossLimit: formData.lossLimit,
    });

    if (result.success) {
      navigate('/dashboard');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-primary-100 flex items-center justify-center px-4 py-8">
      <div className="w-full max-w-md">
        {/* Logo和标题 */}
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-gradient-to-r from-primary-600 to-primary-800 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <span className="text-white font-bold text-2xl">SA</span>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            注册 ScalpAlert
          </h1>
          <p className="text-gray-600">
            开始您的智能交易之旅
          </p>
        </div>

        {/* 注册表单 */}
        <div className="card">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* 用户名 */}
            <div>
              <label className="label">用户名 *</label>
              <input
                type="text"
                name="username"
                value={formData.username}
                onChange={handleChange}
                className="input"
                placeholder="请输入用户名（至少3个字符）"
                autoComplete="username"
                disabled={loading}
              />
            </div>

            {/* 密码 */}
            <div>
              <label className="label">密码 *</label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  className="input pr-10"
                  placeholder="请输入密码（至少6个字符）"
                  autoComplete="new-password"
                  disabled={loading}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                  disabled={loading}
                >
                  {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                </button>
              </div>
            </div>

            {/* 确认密码 */}
            <div>
              <label className="label">确认密码 *</label>
              <div className="relative">
                <input
                  type={showConfirmPassword ? 'text' : 'password'}
                  name="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  className="input pr-10"
                  placeholder="请再次输入密码"
                  autoComplete="new-password"
                  disabled={loading}
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                  disabled={loading}
                >
                  {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                </button>
              </div>
            </div>

            {/* 初始本金 */}
            <div>
              <label className="label">初始本金 ($)</label>
              <input
                type="number"
                name="capital"
                value={formData.capital}
                onChange={handleChange}
                className="input"
                placeholder="5000"
                min="100"
                step="100"
                disabled={loading}
              />
              <p className="text-xs text-gray-500 mt-1">
                建议设置为您实际的交易资金
              </p>
            </div>

            {/* 亏损限制 */}
            <div>
              <label className="label">亏损限制 ($)</label>
              <input
                type="number"
                name="lossLimit"
                value={formData.lossLimit}
                onChange={handleChange}
                className="input"
                placeholder="3000"
                min="100"
                step="100"
                disabled={loading}
              />
              <p className="text-xs text-gray-500 mt-1">
                达到此亏损额度时系统将发出警告
              </p>
            </div>

            {/* 注册按钮 */}
            <button
              type="submit"
              disabled={loading}
              className="btn-primary w-full flex items-center justify-center space-x-2"
            >
              {loading ? (
                <div className="loading-spinner"></div>
              ) : (
                <>
                  <UserPlus size={20} />
                  <span>注册</span>
                </>
              )}
            </button>
          </form>

          {/* 登录链接 */}
          <div className="mt-6 text-center">
            <p className="text-gray-600">
              已有账户？{' '}
              <Link
                to="/login"
                className="text-primary-600 hover:text-primary-700 font-medium"
              >
                立即登录
              </Link>
            </p>
          </div>
        </div>

        {/* 服务条款 */}
        <div className="mt-6 text-center">
          <p className="text-xs text-gray-500">
            注册即表示您同意我们的{' '}
            <a href="#" className="text-primary-600 hover:text-primary-700">
              服务条款
            </a>{' '}
            和{' '}
            <a href="#" className="text-primary-600 hover:text-primary-700">
              隐私政策
            </a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default Register;
