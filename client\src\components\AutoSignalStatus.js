import React, { useState, useEffect } from 'react';
import { Play, Pause, Activity, Clock, Coins } from 'lucide-react';
import api from '../utils/api';
import toast from 'react-hot-toast';

const AutoSignalStatus = () => {
  const [status, setStatus] = useState({
    isRunning: false,
    lastCheckTime: null,
    checkInterval: 300000,
    supportedAssets: []
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchStatus();
    // 每30秒更新一次状态
    const interval = setInterval(fetchStatus, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchStatus = async () => {
    try {
      const response = await api.get('/auto-signal/status');
      if (response.data.success) {
        setStatus(response.data.data);
      }
    } catch (error) {
      console.error('获取自动信号状态失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleStart = async () => {
    try {
      const response = await api.post('/auto-signal/start');
      if (response.data.success) {
        toast.success('自动信号检测已启动');
        fetchStatus();
      }
    } catch (error) {
      console.error('启动自动信号检测失败:', error);
      toast.error('启动失败');
    }
  };

  const handleStop = async () => {
    try {
      const response = await api.post('/auto-signal/stop');
      if (response.data.success) {
        toast.success('自动信号检测已停止');
        fetchStatus();
      }
    } catch (error) {
      console.error('停止自动信号检测失败:', error);
      toast.error('停止失败');
    }
  };

  const formatTime = (timeString) => {
    if (!timeString) return '从未运行';
    return new Date(timeString).toLocaleString('zh-CN', {
      timeZone: 'Asia/Shanghai',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatInterval = (ms) => {
    if (!ms || isNaN(ms)) return '未设置';
    const minutes = Math.round(ms / 1000 / 60);
    return `${minutes} 分钟`;
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex justify-center items-center py-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
            status.isRunning ? 'bg-green-100' : 'bg-gray-100'
          }`}>
            <Activity className={`${
              status.isRunning ? 'text-green-600' : 'text-gray-600'
            }`} size={20} />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">自动信号检测</h3>
            <p className={`text-sm ${
              status.isRunning ? 'text-green-600' : 'text-gray-600'
            }`}>
              {status.isRunning ? '运行中' : '已停止'}
            </p>
          </div>
        </div>
        
        <button
          onClick={status.isRunning ? handleStop : handleStart}
          className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors ${
            status.isRunning
              ? 'bg-red-100 text-red-700 hover:bg-red-200'
              : 'bg-green-100 text-green-700 hover:bg-green-200'
          }`}
        >
          {status.isRunning ? (
            <>
              <Pause size={16} />
              <span>停止</span>
            </>
          ) : (
            <>
              <Play size={16} />
              <span>启动</span>
            </>
          )}
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* 检测间隔 */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Clock className="text-blue-600" size={16} />
            <span className="text-sm font-medium text-gray-700">检测间隔</span>
          </div>
          <p className="text-lg font-semibold text-gray-900">
            {formatInterval(status.checkInterval)}
          </p>
        </div>

        {/* 上次检测 */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Activity className="text-purple-600" size={16} />
            <span className="text-sm font-medium text-gray-700">上次检测</span>
          </div>
          <p className="text-sm text-gray-900">
            {formatTime(status.lastCheckTime)}
          </p>
        </div>

        {/* 支持币种 */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Coins className="text-orange-600" size={16} />
            <span className="text-sm font-medium text-gray-700">支持币种</span>
          </div>
          <p className="text-lg font-semibold text-gray-900">
            {status.supportedAssets.length} 个
          </p>
          <div className="flex flex-wrap gap-1 mt-2">
            {status.supportedAssets.map((asset) => (
              <span
                key={asset}
                className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full"
              >
                {asset.toUpperCase()}
              </span>
            ))}
          </div>
        </div>
      </div>

      {/* 状态指示器 */}
      <div className="mt-4 p-3 bg-gray-50 rounded-lg">
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${
            status.isRunning ? 'bg-green-500 animate-pulse' : 'bg-gray-400'
          }`}></div>
          <span className="text-sm text-gray-600">
            {status.isRunning 
              ? '系统正在自动监控市场信号，无需手动操作' 
              : '自动检测已停止，可手动生成信号'
            }
          </span>
        </div>
      </div>

      {/* 说明文字 */}
      <div className="mt-4 text-xs text-gray-500">
        <p>• 自动信号检测会根据您的配置参数定期扫描市场</p>
        <p>• 发现符合条件的信号时会自动创建并发送通知</p>
        <p>• 您可以随时启动或停止自动检测功能</p>
      </div>
    </div>
  );
};

export default AutoSignalStatus;
