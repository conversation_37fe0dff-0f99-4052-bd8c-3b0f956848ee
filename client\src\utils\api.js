import axios from 'axios';
import toast from 'react-hot-toast';

// 创建axios实例
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器 - 添加认证token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 处理错误和token过期
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    const { response } = error;
    
    if (response) {
      const { status, data } = response;
      
      // Token过期或无效
      if (status === 401 || status === 403) {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        window.location.href = '/login';
        toast.error('登录已过期，请重新登录');
        return Promise.reject(error);
      }
      
      // 显示错误消息
      const message = data?.message || '请求失败';
      toast.error(message);
    } else if (error.request) {
      // 网络错误
      toast.error('网络连接失败，请检查网络设置');
    } else {
      // 其他错误
      toast.error('请求发生错误');
    }
    
    return Promise.reject(error);
  }
);

// API方法封装
export const authAPI = {
  // 用户注册
  register: (userData) => api.post('/auth/register', userData),
  
  // 用户登录
  login: (credentials) => api.post('/auth/login', credentials),
  
  // 获取用户信息
  getProfile: () => api.get('/auth/profile'),
  
  // 更新用户设置
  updateSettings: (settings) => api.put('/auth/settings', settings),
  
  // 验证token
  verifyToken: () => api.post('/auth/verify'),
};

export const signalAPI = {
  // 获取最新信号
  getLatest: (params) => api.get('/signals/latest', { params }),
  
  // 生成新信号
  generate: (data) => api.post('/signals/generate', data),
  
  // 获取信号详情
  getById: (id) => api.get(`/signals/${id}`),
  
  // 停用信号
  deactivate: (id) => api.put(`/signals/${id}/deactivate`),
  
  // 获取信号历史
  getHistory: (asset, params) => api.get(`/signals/history/${asset}`, { params }),

  // 获取信号统计
  getStats: (timeRange = '7d') => api.get(`/signals/stats/${timeRange}`),

  // 获取当前市场分析
  getCurrentAnalysis: (asset) => api.get(`/signals/analysis/${asset}`),

  // 获取策略表现
  getPerformance: (timeRange = '30d') => api.get(`/signals/performance/${timeRange}`),
};

export const tradeAPI = {
  // 创建交易
  create: (tradeData) => api.post('/trades', tradeData),
  
  // 卖出交易
  sell: (id, sellData) => api.put(`/trades/${id}/sell`, sellData),
  
  // 获取交易列表
  getList: (params) => api.get('/trades', { params }),
  
  // 获取交易详情
  getById: (id) => api.get(`/trades/${id}`),
  
  // 获取交易统计
  getStats: (params) => api.get('/trades/stats/summary', { params }),
};

export const notificationAPI = {
  // 获取通知列表
  getList: (params) => api.get('/notifications', { params }),

  // 标记为已读
  markAsRead: (id) => api.put(`/notifications/${id}/read`),

  // 获取未读数量
  getUnreadCount: () => api.get('/notifications/unread-count'),
};

// 导出API
export const exportAPI = {
  exportTrades: (params) => {
    const queryString = new URLSearchParams(params).toString();
    const url = `/api/export/trades/csv?${queryString}`;
    return downloadFile(url, `trades_${new Date().toISOString().split('T')[0]}.csv`);
  },
  exportSignals: (params) => {
    const queryString = new URLSearchParams(params).toString();
    const url = `/api/export/signals/csv?${queryString}`;
    return downloadFile(url, `signals_${new Date().toISOString().split('T')[0]}.csv`);
  },
  generateReport: (params) => {
    const queryString = new URLSearchParams(params).toString();
    const url = `/api/export/report/pdf?${queryString}`;
    return downloadFile(url, `report_${params.period || 'month'}_${new Date().toISOString().split('T')[0]}.txt`);
  },
};

// 文件下载辅助函数
const downloadFile = async (url, filename) => {
  try {
    const token = localStorage.getItem('token');
    const baseURL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';
    const response = await fetch(`${baseURL.replace('/api', '')}${url}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error('下载失败');
    }

    const blob = await response.blob();
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);

    return { success: true };
  } catch (error) {
    console.error('文件下载失败:', error);
    throw error;
  }
};

// 工具函数
export const formatCurrency = (amount, currency = '$') => {
  if (amount === null || amount === undefined) return '-';
  return `${currency}${Number(amount).toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })}`;
};

export const formatPercentage = (value, decimals = 2) => {
  if (value === null || value === undefined) return '-';
  return `${Number(value).toFixed(decimals)}%`;
};

export const formatDate = (dateString) => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    timeZone: 'Asia/Shanghai',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  });
};

export const formatTime = (dateString) => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleTimeString('zh-CN', {
    timeZone: 'Asia/Shanghai',
    hour: '2-digit',
    minute: '2-digit',
  });
};

export const getSignalStrengthColor = (strength) => {
  switch (strength) {
    case 'strong':
      return 'text-success-600 bg-success-100';
    case 'medium':
      return 'text-warning-600 bg-warning-100';
    case 'weak':
      return 'text-gray-600 bg-gray-100';
    case 'skip':
      return 'text-gray-600 bg-gray-100';
    default:
      return 'text-gray-600 bg-gray-100';
  }
};

export const getSignalStrengthText = (strength) => {
  switch (strength) {
    case 'strong':
      return '强';
    case 'medium':
      return '中';
    case 'weak':
      return '弱';
    case 'skip':
      return '跳过';
    default:
      return '未知';
  }
};

export const getProfitLossColor = (amount) => {
  if (amount > 0) return 'text-success-600';
  if (amount < 0) return 'text-danger-600';
  return 'text-gray-600';
};

export const getAssetDisplayName = (asset) => {
  const assetNames = {
    'bitcoin': 'BTC',
    'ethereum': 'ETH',
    'binancecoin': 'BNB',
    'cardano': 'ADA',
    'solana': 'SOL',
  };
  return assetNames[asset] || asset.toUpperCase();
};

// 信号配置API
export const signalConfigAPI = {
  getConfig: () => api.get('/signal-config'),
  saveConfig: (config) => api.post('/signal-config', config),
  resetConfig: () => api.post('/signal-config/reset'),
  getPresets: () => api.get('/signal-config/presets')
};

export default api;
