import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, LineChart, Line } from 'recharts';
import { TrendingUp, Target, Award, Activity } from 'lucide-react';
import { signalAPI } from '../utils/api';

const SignalStats = () => {
  const [stats, setStats] = useState({
    strengthDistribution: [],
    qualityDistribution: [],
    assetDistribution: [],
    dailySignals: [],
    totalSignals: 0,
    avgQualityScore: 0,
    strongSignalRatio: 0
  });
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('7d'); // 7d, 30d, 90d

  useEffect(() => {
    fetchStats();
  }, [timeRange]);

  const fetchStats = async () => {
    try {
      setLoading(true);
      const response = await signalAPI.getStats(timeRange);
      if (response.data.success) {
        setStats(response.data.data);
      }
    } catch (error) {
      console.error('获取信号统计失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 信号强度分布颜色
  const strengthColors = {
    'strong': '#10B981',
    'medium': '#F59E0B', 
    'weak': '#EF4444',
    'skip': '#6B7280'
  };

  // 质量评分分布颜色
  const qualityColors = ['#EF4444', '#F59E0B', '#10B981', '#059669'];

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      {/* 标题和时间范围选择 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            <Activity className="text-blue-600" size={20} />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">信号统计分析</h3>
            <p className="text-sm text-gray-600">信号质量和分布情况</p>
          </div>
        </div>
        
        <div className="flex space-x-2">
          {[
            { value: '7d', label: '7天' },
            { value: '30d', label: '30天' },
            { value: '90d', label: '90天' }
          ].map((option) => (
            <button
              key={option.value}
              onClick={() => setTimeRange(option.value)}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                timeRange === option.value
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>

      {/* 关键指标卡片 */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Target className="text-blue-600" size={16} />
            <span className="text-sm font-medium text-gray-700">总信号数</span>
          </div>
          <p className="text-2xl font-bold text-gray-900">{stats.totalSignals}</p>
        </div>

        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Award className="text-green-600" size={16} />
            <span className="text-sm font-medium text-gray-700">平均质量</span>
          </div>
          <p className="text-2xl font-bold text-gray-900">{stats.avgQualityScore}分</p>
        </div>

        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <TrendingUp className="text-orange-600" size={16} />
            <span className="text-sm font-medium text-gray-700">强信号占比</span>
          </div>
          <p className="text-2xl font-bold text-gray-900">{stats.strongSignalRatio}%</p>
        </div>

        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Activity className="text-purple-600" size={16} />
            <span className="text-sm font-medium text-gray-700">日均信号</span>
          </div>
          <p className="text-2xl font-bold text-gray-900">
            {stats.dailySignals.length > 0 ? 
              Math.round(stats.totalSignals / stats.dailySignals.length) : 0}
          </p>
        </div>
      </div>

      {/* 图表区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 信号强度分布 */}
        <div>
          <h4 className="font-medium text-gray-900 mb-4">信号强度分布</h4>
          <ResponsiveContainer width="100%" height={200}>
            <BarChart data={stats.strengthDistribution}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="strength" />
              <YAxis />
              <Tooltip
                formatter={(value, name) => [value, '信号数量']}
                labelFormatter={(label) => `强度: ${label}`}
              />
              <Bar dataKey="count" radius={[4, 4, 0, 0]}>
                {stats.strengthDistribution.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={strengthColors[entry.strength] || '#6B7280'} />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* 质量评分分布 */}
        <div>
          <h4 className="font-medium text-gray-900 mb-4">质量评分分布</h4>
          <ResponsiveContainer width="100%" height={200}>
            <PieChart>
              <Pie
                data={stats.qualityDistribution}
                cx="50%"
                cy="50%"
                innerRadius={40}
                outerRadius={80}
                paddingAngle={5}
                dataKey="count"
              >
                {stats.qualityDistribution.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={qualityColors[index % qualityColors.length]} />
                ))}
              </Pie>
              <Tooltip 
                formatter={(value, name) => [value, '信号数量']}
                labelFormatter={(label) => `评分: ${label}`}
              />
            </PieChart>
          </ResponsiveContainer>
        </div>

        {/* 币种分布 */}
        <div>
          <h4 className="font-medium text-gray-900 mb-4">币种分布</h4>
          <div className="space-y-3">
            {stats.assetDistribution.map((item, index) => (
              <div key={item.asset} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full`} 
                       style={{ backgroundColor: qualityColors[index % qualityColors.length] }}>
                  </div>
                  <span className="text-sm font-medium text-gray-900">
                    {item.asset.toUpperCase()}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">{item.count}个</span>
                  <span className="text-xs text-gray-500">
                    ({((item.count / stats.totalSignals) * 100).toFixed(1)}%)
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 每日信号趋势 */}
        <div>
          <h4 className="font-medium text-gray-900 mb-4">每日信号趋势</h4>
          <ResponsiveContainer width="100%" height={200}>
            <LineChart data={stats.dailySignals}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="date" 
                tickFormatter={(value) => new Date(value).toLocaleDateString()}
              />
              <YAxis />
              <Tooltip 
                formatter={(value, name) => [value, '信号数量']}
                labelFormatter={(label) => `日期: ${new Date(label).toLocaleDateString()}`}
              />
              <Line 
                type="monotone" 
                dataKey="count" 
                stroke="#3B82F6" 
                strokeWidth={2}
                dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* 说明文字 */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <div className="text-xs text-gray-600 space-y-1">
          <p>• <strong>强信号</strong>: 质量评分80+，建议仓位1%</p>
          <p>• <strong>中等信号</strong>: 质量评分60-79，建议仓位0.7%</p>
          <p>• <strong>弱信号</strong>: 质量评分40-59，建议仓位0.3%</p>
          <p>• <strong>跳过信号</strong>: 质量评分低于40，不建议交易</p>
        </div>
      </div>
    </div>
  );
};

export default SignalStats;
