/**
 * 主回测引擎
 * 协调数据、信号生成、资金管理和指标计算
 */

const DataAdapter = require('../adapters/dataAdapter');
const SignalAdapter = require('../adapters/signalAdapter');
const Portfolio = require('./portfolio');
const MetricsCalculator = require('./metrics');

class Backtester {
  constructor(config = {}) {
    this.config = {
      initialCapital: 10000,
      tradingFee: 0.001, // 0.1%
      startIndex: 50, // 从第50根K线开始，确保有足够数据计算指标
      ...config
    };
    
    this.dataAdapter = null;
    this.signalAdapter = null;
    this.portfolio = null;
    this.metricsCalculator = new MetricsCalculator();
    
    this.results = null;
    this.isRunning = false;
  }

  /**
   * 设置数据适配器
   * @param {DataAdapter} dataAdapter - 数据适配器实例
   */
  setDataAdapter(dataAdapter) {
    this.dataAdapter = dataAdapter;
  }

  /**
   * 设置信号适配器
   * @param {SignalAdapter} signalAdapter - 信号适配器实例
   */
  setSignalAdapter(signalAdapter) {
    this.signalAdapter = signalAdapter;
  }

  /**
   * 加载历史数据
   * @param {string} dataPath - 数据文件路径
   */
  loadData(dataPath) {
    const fs = require('fs');
    const data = JSON.parse(fs.readFileSync(dataPath, 'utf8'));

    console.log(`📊 加载数据: ${data.symbol} ${data.interval}`);
    console.log(`📅 时间范围: ${data.startDate} - ${data.endDate}`);
    console.log(`📈 总记录数: ${data.totalRecords}`);

    this.dataAdapter = new DataAdapter(data.data);
    this.signalAdapter = new SignalAdapter(this.dataAdapter);

    // 验证数据
    const validation = this.dataAdapter.validateData();
    if (!validation.valid) {
      throw new Error(`数据验证失败: ${validation.issues.join(', ')}`);
    }

    console.log('✅ 数据加载完成');
    return validation;
  }

  /**
   * 运行回测
   * @param {Object} signalConfig - 信号配置
   * @param {Object} options - 回测选项
   */
  async runBacktest(signalConfig, options = {}) {
    if (!this.dataAdapter || !this.signalAdapter) {
      throw new Error('请先加载数据');
    }

    this.isRunning = true;
    console.log('\n🚀 开始回测...');
    
    // 初始化组合
    this.portfolio = new Portfolio(this.config.initialCapital, this.config.tradingFee);
    
    const dataLength = this.dataAdapter.getLength();
    const startIndex = Math.max(this.config.startIndex, options.startIndex || 0);
    const endIndex = Math.min(dataLength - 1, options.endIndex || dataLength - 1);
    
    console.log(`📊 回测范围: 索引 ${startIndex} - ${endIndex} (共 ${endIndex - startIndex + 1} 根K线)`);
    
    let signalCount = 0;
    let tradeCount = 0;
    
    // 主回测循环
    for (let i = startIndex; i <= endIndex && this.isRunning; i++) {
      const currentCandle = this.dataAdapter.getCurrentCandle(i);
      if (!currentCandle) continue;
      
      const currentPrice = currentCandle.close;
      const timestamp = currentCandle.timestamp;
      
      // 检查止损止盈
      if (this.portfolio.position) {
        const trade = this.portfolio.checkStopLossTakeProfit(currentPrice, timestamp);
        if (trade) {
          tradeCount++;
        }
      }
      
      // 如果没有持仓，检查是否有新信号
      if (!this.portfolio.position) {
        try {
          const signal = await this.signalAdapter.generateSignal('bitcoin', signalConfig, i);
          
          if (signal.signal_type === 'buy' && signal.signal_strength !== 'skip') {
            signalCount++;
            const opened = this.portfolio.openPosition(signal, timestamp, signalConfig);
            if (opened) {
              console.log(`📈 第${signalCount}个信号: ${signal.signal_strength} @ $${currentPrice.toFixed(2)}`);
            }
          }
        } catch (error) {
          console.error(`信号生成错误 (索引 ${i}):`, error.message);
        }
      }
      
      // 更新权益曲线
      this.portfolio.updateEquity(timestamp, currentPrice);
      
      // 进度报告
      if (i % 1000 === 0) {
        const progress = ((i - startIndex) / (endIndex - startIndex) * 100).toFixed(1);
        const status = this.portfolio.getStatus();
        console.log(`⏳ 进度: ${progress}% | 资金: $${status.totalEquity.toFixed(2)} | 信号: ${signalCount} | 交易: ${tradeCount}`);
      }
    }
    
    // 如果回测结束时还有持仓，强制平仓
    if (this.portfolio.position) {
      const finalCandle = this.dataAdapter.getCurrentCandle(endIndex);
      this.portfolio.closePosition(finalCandle.close, finalCandle.timestamp, 'Backtest End');
      tradeCount++;
    }
    
    console.log('\n✅ 回测完成');
    
    // 计算指标
    const backtestDays = this.calculateBacktestDays(startIndex, endIndex);
    const metrics = this.metricsCalculator.calculateAllMetrics(this.portfolio, backtestDays);
    
    this.results = {
      config: signalConfig,
      metrics,
      portfolio: this.portfolio,
      summary: {
        totalSignals: signalCount,
        totalTrades: tradeCount,
        backtestPeriod: {
          startIndex,
          endIndex,
          totalCandles: endIndex - startIndex + 1,
          days: backtestDays
        }
      }
    };
    
    this.isRunning = false;
    return this.results;
  }

  /**
   * 计算回测天数
   * @param {number} startIndex - 开始索引
   * @param {number} endIndex - 结束索引
   * @returns {number} 天数
   */
  calculateBacktestDays(startIndex, endIndex) {
    const startCandle = this.dataAdapter.getCurrentCandle(startIndex);
    const endCandle = this.dataAdapter.getCurrentCandle(endIndex);
    
    if (!startCandle || !endCandle) return 0;
    
    return (endCandle.timestamp - startCandle.timestamp) / (1000 * 60 * 60 * 24);
  }

  /**
   * 停止回测
   */
  stop() {
    this.isRunning = false;
    console.log('⏹️ 回测已停止');
  }

  /**
   * 获取结果报告
   * @returns {string} 格式化的报告
   */
  getReport() {
    if (!this.results) {
      return '❌ 没有回测结果';
    }
    
    return this.metricsCalculator.generateReport(this.results.metrics);
  }

  /**
   * 导出结果
   * @param {string} outputDir - 输出目录
   */
  exportResults(outputDir = './backtest_results') {
    if (!this.results) {
      throw new Error('没有回测结果可导出');
    }
    
    const fs = require('fs');
    const path = require('path');
    
    // 创建输出目录
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    // 导出交易记录
    const tradesCSV = this.portfolio.exportTradesToCSV();
    if (tradesCSV) {
      fs.writeFileSync(path.join(outputDir, `trades_${timestamp}.csv`), tradesCSV);
    }
    
    // 导出权益曲线
    const equityData = this.portfolio.equity.map(point => ({
      timestamp: new Date(point.timestamp).toISOString(),
      capital: point.capital,
      totalEquity: point.totalEquity,
      drawdown: point.drawdown
    }));
    fs.writeFileSync(path.join(outputDir, `equity_${timestamp}.json`), JSON.stringify(equityData, null, 2));
    
    // 导出完整结果
    const fullResults = {
      ...this.results,
      portfolio: {
        trades: this.portfolio.trades,
        equity: this.portfolio.equity,
        finalStatus: this.portfolio.getStatus()
      }
    };
    fs.writeFileSync(path.join(outputDir, `results_${timestamp}.json`), JSON.stringify(fullResults, null, 2));
    
    // 导出报告
    const report = this.getReport();
    fs.writeFileSync(path.join(outputDir, `report_${timestamp}.txt`), report);
    
    console.log(`📁 结果已导出到: ${outputDir}`);
    return outputDir;
  }

  /**
   * 批量回测（测试不同配置）
   * @param {Array} configList - 配置列表
   * @param {Object} options - 选项
   */
  async batchBacktest(configList, options = {}) {
    const results = [];
    
    console.log(`🔄 开始批量回测，共 ${configList.length} 个配置`);
    
    for (let i = 0; i < configList.length; i++) {
      const config = configList[i];
      console.log(`\n📊 测试配置 ${i + 1}/${configList.length}: ${config.name || `配置${i + 1}`}`);
      
      try {
        const result = await this.runBacktest(config, options);
        results.push({
          configIndex: i,
          configName: config.name || `配置${i + 1}`,
          ...result
        });
      } catch (error) {
        console.error(`❌ 配置 ${i + 1} 回测失败:`, error.message);
        results.push({
          configIndex: i,
          configName: config.name || `配置${i + 1}`,
          error: error.message
        });
      }
    }
    
    // 排序结果（按夏普比率）
    const validResults = results.filter(r => !r.error);
    validResults.sort((a, b) => (b.metrics?.sharpeRatio || 0) - (a.metrics?.sharpeRatio || 0));
    
    console.log('\n🏆 批量回测完成，按夏普比率排序:');
    validResults.forEach((result, index) => {
      const metrics = result.metrics;
      console.log(`${index + 1}. ${result.configName}: 夏普=${metrics.sharpeRatio.toFixed(3)}, 收益=${metrics.totalReturn.toFixed(2)}%, 回撤=${metrics.maxDrawdown.toFixed(2)}%`);
    });
    
    return results;
  }
}

module.exports = Backtester;
