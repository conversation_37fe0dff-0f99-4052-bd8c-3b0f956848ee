@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  * {
    @apply border-gray-200;
  }

  input, textarea, select {
    @apply focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
  }

  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }

  .btn-secondary {
    @apply btn bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
  }

  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }

  .btn-danger {
    @apply btn bg-danger-600 text-white hover:bg-danger-700 focus:ring-danger-500;
  }

  .btn-warning {
    @apply btn bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;
  }

  .btn-outline {
    @apply btn border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-primary-500;
  }

  .card {
    @apply bg-white rounded-xl shadow-sm border border-gray-200 p-6;
  }

  .input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-lg text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
  }

  .label {
    @apply block text-sm font-medium text-gray-700 mb-2;
  }

  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-success {
    @apply badge bg-success-100 text-success-800;
  }

  .badge-danger {
    @apply badge bg-danger-100 text-danger-800;
  }

  .badge-warning {
    @apply badge bg-warning-100 text-warning-800;
  }

  .badge-primary {
    @apply badge bg-primary-100 text-primary-800;
  }

  .badge-gray {
    @apply badge bg-gray-100 text-gray-800;
  }

  .mobile-container {
    @apply max-w-md mx-auto bg-white min-h-screen;
  }

  .mobile-header {
    @apply sticky top-0 z-10 bg-white border-b border-gray-200 px-4 py-3;
  }

  .mobile-content {
    @apply px-4 py-6 pb-20;
  }

  .mobile-bottom-nav {
    @apply fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-md bg-white border-t border-gray-200 px-4 py-2;
  }

  .nav-item {
    @apply flex flex-col items-center justify-center py-2 px-3 text-xs font-medium text-gray-500 hover:text-primary-600 transition-colors duration-200;
  }

  .nav-item.active {
    @apply text-primary-600;
  }

  .signal-card {
    @apply card hover:shadow-md transition-shadow duration-200 cursor-pointer;
  }

  .signal-strength-high {
    @apply border-l-4 border-success-500;
  }

  .signal-strength-medium {
    @apply border-l-4 border-warning-500;
  }

  .signal-strength-low {
    @apply border-l-4 border-gray-400;
  }

  .trade-profit {
    @apply text-success-600 font-semibold;
  }

  .trade-loss {
    @apply text-danger-600 font-semibold;
  }

  .loading-spinner {
    @apply animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600;
  }

  .skeleton {
    @apply animate-pulse bg-gray-200 rounded;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-primary-800 bg-clip-text text-transparent;
  }

  .shadow-card {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  }

  .shadow-card-hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }
}

/* 移动端优化 */
@media (max-width: 640px) {
  .mobile-optimized {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }

  .touch-action-manipulation {
    touch-action: manipulation;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
