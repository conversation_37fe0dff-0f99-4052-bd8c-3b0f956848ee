// 统一的信号配置预设模板
// 所有相关文件都应该从这里导入配置，避免重复定义

const signalPresets = {
  conservative: {
    name: '保守型',
    description: '低风险，高质量信号',
    rsi_strong_threshold: 25,
    rsi_medium_threshold: 35,
    risk_strong: 0.005,      // 强信号风险 (0.5%资金)
    risk_medium: 0.003,      // 中等信号风险 (0.3%资金)
    risk_weak: 0.001,        // 弱信号风险 (0.1%资金)
    atr_multiplier: 1.5,     // ATR止损倍数
    risk_reward_ratio: 2.0,  // 风险回报比 (1:2)
    volume_multiplier: 1.8,
    enable_volume_filter: true,
    enable_m_head_filter: true,
    enable_w_bottom_bonus: true,
    skip_weak_signals: false,
    min_quality_score: 50
  },

  balanced: {
    name: '平衡型',
    description: '平衡风险和收益',
    rsi_strong_threshold: 30,
    rsi_medium_threshold: 40,
    risk_strong: 0.01,       // 强信号风险 (1%资金)
    risk_medium: 0.008,      // 中等信号风险 (0.8%资金)
    risk_weak: 0.005,        // 弱信号风险 (0.5%资金)
    atr_multiplier: 1.5,     // ATR止损倍数
    risk_reward_ratio: 2.0,  // 风险回报比 (1:2)
    volume_multiplier: 1.5,
    enable_volume_filter: true,
    enable_m_head_filter: false,
    enable_w_bottom_bonus: true,
    skip_weak_signals: false,
    min_quality_score: 40
  },

  aggressive: {
    name: '激进型',
    description: '高风险，更多交易机会',
    rsi_strong_threshold: 35,
    rsi_medium_threshold: 45,
    risk_strong: 0.015,      // 强信号风险 (1.5%资金)
    risk_medium: 0.012,      // 中等信号风险 (1.2%资金)
    risk_weak: 0.008,        // 弱信号风险 (0.8%资金)
    atr_multiplier: 1.5,     // ATR止损倍数
    risk_reward_ratio: 2.5,  // 风险回报比 (1:2.5，更激进)
    volume_multiplier: 1.2,
    enable_volume_filter: false,
    enable_m_head_filter: false,
    enable_w_bottom_bonus: true,
    skip_weak_signals: false,
    min_quality_score: 30
  }
};

// 转换为数据库格式（布尔值转为0/1）
const convertToDbFormat = (preset) => {
  const dbPreset = { ...preset };
  
  // 转换布尔值为数字
  dbPreset.enable_volume_filter = preset.enable_volume_filter ? 1 : 0;
  dbPreset.enable_m_head_filter = preset.enable_m_head_filter ? 1 : 0;
  dbPreset.enable_w_bottom_bonus = preset.enable_w_bottom_bonus ? 1 : 0;
  dbPreset.skip_weak_signals = preset.skip_weak_signals ? 1 : 0;
  
  return dbPreset;
};

// 转换为前端格式（0/1转为布尔值）
const convertToFrontendFormat = (dbConfig) => {
  const frontendConfig = { ...dbConfig };
  
  // 转换数字为布尔值
  frontendConfig.enable_volume_filter = Boolean(dbConfig.enable_volume_filter);
  frontendConfig.enable_m_head_filter = Boolean(dbConfig.enable_m_head_filter);
  frontendConfig.enable_w_bottom_bonus = Boolean(dbConfig.enable_w_bottom_bonus);
  frontendConfig.skip_weak_signals = Boolean(dbConfig.skip_weak_signals);
  
  return frontendConfig;
};

// 获取所有预设（前端格式）
const getAllPresets = () => {
  return signalPresets;
};

// 获取所有预设（数据库格式）
const getAllPresetsDbFormat = () => {
  const dbPresets = {};
  for (const [key, preset] of Object.entries(signalPresets)) {
    dbPresets[key] = convertToDbFormat(preset);
  }
  return dbPresets;
};

// 检测配置类型
const detectConfigType = (config) => {
  for (const [presetKey, preset] of Object.entries(signalPresets)) {
    const dbPreset = convertToDbFormat(preset);
    
    // 比较关键参数（使用新的风控参数）
    const matches = (
      config.rsi_strong_threshold === dbPreset.rsi_strong_threshold &&
      config.rsi_medium_threshold === dbPreset.rsi_medium_threshold &&
      config.risk_strong === dbPreset.risk_strong &&           // 新的风险参数
      config.risk_medium === dbPreset.risk_medium &&           // 新的风险参数
      config.risk_weak === dbPreset.risk_weak &&               // 新的风险参数
      config.atr_multiplier === dbPreset.atr_multiplier &&     // ATR倍数参数
      config.volume_multiplier === dbPreset.volume_multiplier &&
      config.enable_volume_filter === dbPreset.enable_volume_filter &&
      config.enable_m_head_filter === dbPreset.enable_m_head_filter &&
      config.enable_w_bottom_bonus === dbPreset.enable_w_bottom_bonus &&
      config.skip_weak_signals === dbPreset.skip_weak_signals &&
      config.min_quality_score === dbPreset.min_quality_score
    );
    
    if (matches) {
      return preset.name;
    }
  }
  
  return '自定义配置';
};

module.exports = {
  signalPresets,
  convertToDbFormat,
  convertToFrontendFormat,
  getAllPresets,
  getAllPresetsDbFormat,
  detectConfigType
};
